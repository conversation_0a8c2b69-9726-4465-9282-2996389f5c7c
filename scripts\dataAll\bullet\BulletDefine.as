package dataAll.bullet
{
   import com.sounto.math.Maths;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._base.IO_Define;
   import dataAll.arms.creator.ArmsDataCreator;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsName;
   import dataAll.arms.define.ArmsType;
   import dataAll.body.attack.AttackType;
   import dataAll.body.attack.HurtData;
   import dataAll.body.attack.HurtKind;
   import dataAll.body.define.BodyCamp;
   import dataAll.bullet.position.BulletPositionDefine;
   import dataAll.bullet.position.BulletPositionDefineGroup;
   import dataAll.image.ImageUrlDefine;
   
   public class BulletDefine extends BulletPositionDefine implements IO_Define
   {
      
      public static var NO:String = "no";
      
      public static var LONG_LINE:String = "longLine";
      
      public static var RECT:String = "rect";
      
      public static const HIT_ARR:Array = [RECT,LONG_LINE];
      
      public static const HIT_CN_ARR:Array = ["子弹","射线"];
      
      public static var pro_arr:Array = [];
      
      public static var imgNameArr:Array = ["bulletImg","bulletLeftImg","smokeImg","hitImg","hitFloorImg","fireImg","selfBoomImg"];
      
      private static const LaborZombie_1:String = "LaborZombie_1";
      
      protected var V:Number = Math.random() / 5 + 0.01;
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var kind:String = "";
      
      public var baseLabel:String = "";
      
      public var armsType:String = "";
      
      public var sameCampB:Boolean = true;
      
      public var noHitB:Boolean = false;
      
      protected var _hurtRatio:Number = 0;
      
      public var hurtMul:Number = 0;
      
      public var transBackMul:Number = 1;
      
      public var attackType:String = AttackType.DIRECT;
      
      public var noHurtEffectB:Boolean = false;
      
      public var whippB:Boolean = true;
      
      public var noMagneticB:Boolean = false;
      
      public var noBeClearB:Boolean = false;
      
      public var actionLabel:String = "";
      
      public var bulletLife:Number = 2;
      
      public var lifeRandom:Number = 0;
      
      public var imgClearDelay:Number = 0;
      
      public var bulletWidth:int = 7;
      
      public var aiShootRange:Number = 0;
      
      public var bulletShakeWidth:int = 0;
      
      public var hitType:String = RECT;
      
      public var attackGap:Number = 0;
      
      public var attackDelay:Number = 0;
      
      public var bulletNum:int = 1;
      
      public var shootGap:Number = 0;
      
      public var shootNum:int = 1;
      
      public var positionD:BulletPositionDefineGroup = null;
      
      public var bulletSpeed:Number;
      
      public var speedD:BulletSpeedDefine = new BulletSpeedDefine();
      
      public var gravity:Number = 0;
      
      public var bulletVra:Number = 0;
      
      public var shootRecoil:Number = 0;
      
      public var screenShakeValue:Number = 0;
      
      public var skillArr:Array = [];
      
      public var godSkillArr:Array = [];
      
      public var bulletSkillArr:Array = [];
      
      public var followD:BulletFollowDefine = new BulletFollowDefine();
      
      public var bounceD:BulletBounceDefine = new BulletBounceDefine();
      
      public var critD:BulletCritDefine = new BulletCritDefine();
      
      public var critD3:BulletCritDefine = new BulletCritDefine();
      
      public var noHitTime:Number = 0;
      
      public var hideTime:Number = 0;
      
      public var hitGap:Number = 0;
      
      public var twoHitGap:Number = 0;
      
      public var twoHitSameNameB:Boolean = false;
      
      public var oneHitBodyB:Boolean = false;
      
      public var penetrationNum:int = 0;
      
      public var penetrationGap:int = 0;
      
      public var boomD:BulletBoomDefine = new BulletBoomDefine();
      
      public var bindingD:BulletBindingDefine = new BulletBindingDefine();
      
      public var beatBack:Number = 0;
      
      public var targetShakeValue:Number = 0;
      
      public var lineD:BulletLineDefine = new BulletLineDefine();
      
      public var playImgLabel:String;
      
      public var bulletImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var fireImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var bulletLeftImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var hitImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var hitFloorImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var smokeImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var selfBoomImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var implodingB:Boolean = false;
      
      public function BulletDefine()
      {
         super();
         this.critD3.mul = 3;
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var xx0:int = 0;
         this.name = String(xml0.name);
         this.cnName = String(xml0.cnName);
         this.kind = String(xml0.@kind);
         this.baseLabel = String(xml0.baseLabel);
         if(this.baseLabel == "")
         {
            this.baseLabel = this.name;
         }
         this.actionLabel = String(xml0.actionLabel);
         this.armsType = String(xml0.armsType);
         if(String(xml0.sameCampB) != "")
         {
            this.sameCampB = Boolean(int(String(xml0.sameCampB)));
         }
         if(String(xml0.twoHitSameNameB) != "")
         {
            this.twoHitSameNameB = Boolean(int(String(xml0.twoHitSameNameB)));
         }
         if(String(xml0.oneHitBodyB) != "")
         {
            this.oneHitBodyB = Boolean(int(String(xml0.oneHitBodyB)));
         }
         if(String(xml0.noHitB) != "")
         {
            this.noHitB = Boolean(int(xml0.noHitB));
         }
         if(String(xml0.noHurtEffectB) != "")
         {
            this.noHurtEffectB = Boolean(int(xml0.noHurtEffectB));
         }
         if(String(xml0.noMagneticB) != "")
         {
            this.noMagneticB = Boolean(int(xml0.noMagneticB));
         }
         if(String(xml0.noBeClearB) != "")
         {
            this.noBeClearB = Boolean(int(xml0.noBeClearB));
         }
         shakeAngle = Number(xml0.shakeAngle);
         this.hurtRatio = Number(xml0.hurtRatio);
         this.hurtMul = Number(xml0.hurtMul);
         this.transBackMul = Number(xml0.transBackMul);
         if(String(xml0.transBackMul) == "")
         {
            this.transBackMul = 1;
         }
         this.attackType = String(xml0.attackType);
         this.whippB = Boolean(int(xml0.whippB));
         if(String(xml0.whippB) == "")
         {
            this.whippB = true;
         }
         this.bulletLife = Number(xml0.bulletLife);
         if(this.bulletLife == 0)
         {
            this.bulletLife = 2;
         }
         this.lifeRandom = Number(xml0.lifeRandom);
         this.imgClearDelay = Number(xml0.imgClearDelay);
         this.bulletWidth = int(xml0.bulletWidth);
         this.aiShootRange = Number(xml0.aiShootRange);
         this.bulletShakeWidth = int(xml0.bulletShakeWidth);
         this.hitType = String(xml0.hitType);
         if(this.hitType == "")
         {
            this.hitType = RECT;
         }
         this.attackGap = Number(xml0.attackGap);
         this.attackDelay = Number(xml0.attackDelay);
         this.bulletNum = int(xml0.bulletNum);
         if(this.bulletNum == 0)
         {
            this.bulletNum = 1;
         }
         this.shootGap = Number(xml0.shootGap);
         shootAngle = Number(xml0.shootAngle);
         this.shootNum = int(xml0.shootNum);
         if(this.shootNum == 0)
         {
            this.shootNum = 1;
         }
         gatlinNum = Number(xml0.gatlinNum);
         gatlinRange = Number(xml0.gatlinRange);
         shootPoint = ComMethod.getPoint(String(xml0.shootPoint));
         this.bulletSpeed = Number(xml0.bulletSpeed);
         this.speedD.inData_byXML(xml0.speedD[0],this.bulletSpeed);
         this.shootRecoil = Number(xml0.shootRecoil);
         this.screenShakeValue = Number(xml0.screenShakeValue);
         this.gravity = Number(xml0.gravity);
         this.bulletVra = Number(xml0.bulletVra);
         bulletAngle = Number(xml0.bulletAngle);
         if(xml0.bulletAngle.length() == 0)
         {
            bulletAngle = -1000;
         }
         bulletAngleRange = Number(xml0.bulletAngleRange);
         if(xml0.positionD.length() > 0)
         {
            this.positionD = new BulletPositionDefineGroup();
            this.positionD.inData_byXML(xml0.positionD[0],this);
         }
         if(String(xml0.skillArr) != "")
         {
            this.skillArr = String(xml0.skillArr).split(",");
         }
         var skillLabel0:String = String(xml0.skillLabel);
         if(skillLabel0 != "")
         {
            this.skillArr.push(skillLabel0);
         }
         if(String(xml0.godSkillArr) != "")
         {
            this.godSkillArr = String(xml0.godSkillArr).split(",");
         }
         if(String(xml0.bulletSkillArr) != "")
         {
            this.bulletSkillArr = String(xml0.bulletSkillArr).split(",");
         }
         this.followD.inData_byXML(xml0.followD[0]);
         this.bounceD.inData_byXML(xml0.bounceD[0]);
         this.critD.inData_byXML(xml0.critD[0]);
         this.bindingD.inData_byXML(xml0.bindingD[0]);
         this.penetrationNum = int(xml0.penetrationNum);
         this.penetrationGap = int(xml0.penetrationGap);
         if(xml0.twoHitGap.length() == 0)
         {
            if(this.penetrationNum > 0)
            {
               this.twoHitGap = 0.2;
            }
         }
         else
         {
            this.twoHitGap = Number(xml0.twoHitGap);
         }
         this.hitGap = Number(xml0.hitGap);
         this.noHitTime = Number(xml0.noHitTime);
         this.hideTime = Number(xml0.hideTime);
         this.boomD.inData_byXML(xml0.boomD[0]);
         this.beatBack = Number(xml0.beatBack);
         this.targetShakeValue = int(xml0.targetShakeValue);
         this.lineD.inData_byXML(xml0.lineD[0]);
         this.bulletImg.inData_byXML(xml0.bulletImgUrl[0]);
         this.fireImg.inData_byXML(xml0.fireImgUrl[0]);
         this.bulletLeftImg.inData_byXML(xml0.bulletLeftImgUrl[0]);
         this.hitImg.inData_byXML(xml0.hitImgUrl[0]);
         this.hitFloorImg.inData_byXML(xml0.hitFloorImgUrl[0]);
         this.smokeImg.inData_byXML(xml0.smokeImgUrl[0]);
         this.selfBoomImg.inData_byXML(xml0.selfBoomImgUrl[0]);
         this.implodingB = this.name.indexOf("imploding") >= 0;
         if(this is ArmsDefine == false)
         {
            if(this.kind == HurtKind.no)
            {
               if(this.boomD.radius > 0)
               {
                  this.kind = HurtKind.boom;
               }
            }
         }
         if(this.isLongLine() && this.penetrationGap >= 1000)
         {
            xx0 = 0;
         }
      }
      
      public function followProducerPenGap() : Boolean
      {
         if(this.penetrationGap == 0 && this.bounceD.hurtNumAdd > 0)
         {
            return false;
         }
         return true;
      }
      
      public function getShootRange() : int
      {
         return ArmsDataCreator.getShootRange(this.armsType,this.hitType,this.bulletWidth,this.bulletLife,this.bulletSpeed,this.aiShootRange);
      }
      
      public function getShowShootRange() : int
      {
         var v0:Number = this.getShootRange();
         return ArmsDataCreator.getShowShootRange(v0);
      }
      
      public function setShootRange(v0:Number) : void
      {
         if(this.hitType == LONG_LINE)
         {
            this.bulletWidth = v0;
         }
      }
      
      public function getShakeLineWidth() : int
      {
         var v0:Number = this.bulletWidth + this.bulletShakeWidth * (1 - 2 * Math.random());
         if(this.name == ArmsName.consLeo)
         {
            v0 *= 1.2;
         }
         return v0;
      }
      
      public function getHitFloorImgDefine() : ImageUrlDefine
      {
         if(this.hitFloorImg.url == "")
         {
            return this.hitImg;
         }
         return this.hitFloorImg;
      }
      
      public function getAIShootRange(precisionB0:Boolean = true) : int
      {
         var v0:Number = NaN;
         if(this.aiShootRange == 0)
         {
            v0 = 1;
            if(precisionB0)
            {
               v0 = this.getOnlyAnglePrecision();
               v0 = 1 - (1 - v0) * 0.4;
               if(v0 < 0.6)
               {
                  v0 = 0.6;
               }
            }
            return v0 * this.getShootRange();
         }
         return this.aiShootRange;
      }
      
      public function getPrecision() : Number
      {
         var s0:Number = 1 - shakeAngle / 30;
         if(s0 < 0.4)
         {
            s0 = 0.4;
         }
         var a0:Number = 1 - shootAngle / 30;
         if(a0 < 0.3)
         {
            a0 = 0.3;
         }
         var l0:Number = (this.getShootRange() + 500) / 1100;
         if(l0 > 1)
         {
            l0 = 1;
         }
         return s0 * a0 * l0;
      }
      
      public function getOnlyAnglePrecision() : Number
      {
         return ArmsDataCreator.getOnlyAnglePrecision(shakeAngle,shootAngle);
      }
      
      public function getLineD() : BulletLineDefine
      {
         return this.lineD;
      }
      
      public function getAllSkillArr() : Array
      {
         return this.skillArr.concat(this.godSkillArr).concat(this.bindingD.skillArr);
      }
      
      public function getPrecessSkillArr() : Array
      {
         return this.getAllSkillArr();
      }
      
      public function getHurtData() : HurtData
      {
         var h0:HurtData = new HurtData();
         h0.hurtRatio = this.hurtRatio;
         h0.hurtMul = this.hurtMul;
         h0.attackType = this.attackType;
         h0.beatBack = this.beatBack;
         h0.shakeValue = this.targetShakeValue;
         h0.setHitImgByBullet(this);
         h0.from = HurtData.FROM_BULLET;
         h0.skillArr = this.getAllSkillArr();
         if(this.critD.pro > 0 || this.critD3.pro > 0)
         {
            h0.critArr = [];
            if(this.critD3.pro > 0)
            {
               h0.critArr.push(this.critD3);
            }
            if(this.critD.pro > 0)
            {
               h0.critArr.push(this.critD);
            }
         }
         return h0;
      }
      
      public function getBloodMul() : Number
      {
         var at0:Number = this.getBulletNumPerS();
         if(at0 <= 0.2)
         {
            return 0.7;
         }
         if(at0 <= 0.1)
         {
            return 0.35;
         }
         return 1;
      }
      
      public function getBulletNumPerS() : Number
      {
         var at0:Number = this.attackGap;
         if(at0 < 0.015)
         {
            at0 = 0.015;
         }
         var num0:int = this.shootNum * this.bulletNum;
         return num0 / at0;
      }
      
      public function lineImageB() : Boolean
      {
         return this.bulletImg.url == LONG_LINE;
      }
      
      public function lineBmpB() : Boolean
      {
         return this.bulletImg.len > 0;
      }
      
      public function bulletFollowShootPointB() : Boolean
      {
         return this.armsType == ArmsType.laser;
      }
      
      public function isMineB() : Boolean
      {
         return BulletName.mineArr.indexOf(this.name) >= 0;
      }
      
      public function getBulletImgByRa(ra0:Number, camp0:String) : ImageUrlDefine
      {
         if(this.name == LaborZombie_1 || this.isMineB())
         {
            if(camp0 == BodyCamp.WE)
            {
               return this.bulletLeftImg;
            }
            return this.bulletImg;
         }
         if(this.bulletLeftImg.url != "")
         {
            ra0 = Maths.ZhunJ(ra0);
            if(ra0 < Math.PI / 2 && ra0 > -Math.PI / 2)
            {
               return this.bulletImg;
            }
            return this.bulletLeftImg;
         }
         return this.bulletImg;
      }
      
      public function getAllImageUrl() : Array
      {
         var name0:String = null;
         var i0:ImageUrlDefine = null;
         var arr0:Array = [];
         for each(name0 in pro_arr)
         {
            if(this[name0] is ImageUrlDefine)
            {
               i0 = this[name0];
               if(i0.url != LONG_LINE && i0.url != "")
               {
                  ComMethod.addNoRepeatInArr(arr0,i0.url);
               }
            }
         }
         return arr0;
      }
      
      public function set hurtRatio(v0:Number) : void
      {
         this._hurtRatio = v0 / this.V;
      }
      
      public function get hurtRatio() : Number
      {
         return this._hurtRatio * this.V;
      }
      
      public function getHitSoundVolume() : Number
      {
         var num0:Number = NaN;
         var v0:Number = 1;
         var imgD0:ImageUrlDefine = this.hitImg;
         num0 = this.bulletNum;
         if(this is ArmsDefine)
         {
            num0 *= this.shootNum;
         }
         if(num0 >= 1)
         {
            if(imgD0.getSoundVolumeSetB() == true)
            {
               v0 = 1 / num0 * 0.7 + 0.3;
            }
            else
            {
               v0 = 1 / num0 * 0.9 + 0.1;
            }
         }
         else
         {
            v0 = 1;
         }
         return v0;
      }
      
      public function getHitFloorSoundVolume() : Number
      {
         if(this.hitFloorImg.url == "")
         {
            return this.getHitSoundVolume();
         }
         return 1;
      }
      
      public function isLongLine() : Boolean
      {
         return this.hitType == LONG_LINE;
      }
      
      public function isRect() : Boolean
      {
         return this.hitType == RECT;
      }
      
      public function canBounceB() : Boolean
      {
         if(this.hitType == LONG_LINE && this.bulletLife > 0.12)
         {
            return false;
         }
         return true;
      }
      
      public function getName() : String
      {
         return this.name;
      }
      
      public function getCnName() : String
      {
         return this.cnName;
      }
      
      public function inAllImgName(d0:BulletDefine) : void
      {
         var name0:String = null;
         var imgD0:ImageUrlDefine = null;
         var baseD0:ImageUrlDefine = null;
         for each(name0 in imgNameArr)
         {
            imgD0 = this[name0];
            baseD0 = d0[name0];
            imgD0.name = baseD0.name;
         }
      }
   }
}

