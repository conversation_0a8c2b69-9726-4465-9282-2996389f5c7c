package dataAll._app.love
{
   import com.sounto.utils.ClassProperty;
   import dataAll._app.love.define.LoveRoleAll;
   
   public class LoveLikeSave
   {
      
      public static var pro_arr:Array = [];
      
      public var likeArr:Array = [];
      
      public var hateArr:Array = [];
      
      public function LoveLikeSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function random(defAll0:LoveRoleAll) : void
      {
         var likeHateArr0:Array = defAll0.getLikeHateRandom(4);
         this.likeArr = likeHateArr0[0];
         this.hateArr = likeHateArr0[1];
      }
      
      public function getLikeRandomName() : String
      {
         return this.likeArr[int(Math.random() * this.likeArr.length)];
      }
      
      public function getHateRandomName() : String
      {
         return this.hateArr[int(Math.random() * this.hateArr.length)];
      }
   }
}

