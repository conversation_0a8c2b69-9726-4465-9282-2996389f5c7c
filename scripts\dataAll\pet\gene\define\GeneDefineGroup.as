package dataAll.pet.gene.define
{
   import dataAll.must.define.MustDefine;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.pro.PropertyArrayDefineGroup;
   import dataAll.things.define.ThingsDefine;
   
   public class GeneDefineGroup
   {
      
      public var obj:Object = {};
      
      private var targetObj:Object = {};
      
      public var arr:Array = [];
      
      public var pro:GenePropertyArrayDefineGroup = new GenePropertyArrayDefineGroup();
      
      public var strengthenPro:PropertyArrayDefineGroup = new PropertyArrayDefineGroup();
      
      public var allProNameArr:Array = [];
      
      public var drop:GeneDropDefine = new GeneDropDefine();
      
      public function GeneDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var thingsXML0:* = undefined;
         var fatherName0:String = null;
         var n:* = undefined;
         var d0:GeneDefine = null;
         var fatherXML0:XMLList = xml0.father;
         var index0:int = 0;
         for(i in fatherXML0)
         {
            thingsXML0 = fatherXML0[i].gene;
            fatherName0 = fatherXML0[i].@name;
            for(n in thingsXML0)
            {
               d0 = new GeneDefine();
               d0.inData_byXML(thingsXML0[n],fatherName0);
               this.obj[d0.name] = d0;
               this.targetObj[d0.targetBodyName] = d0;
               if(d0.secBodyName != "")
               {
                  this.targetObj[d0.secBodyName] = d0;
               }
               this.arr.push(d0);
               d0.index = index0;
               index0++;
            }
         }
      }
      
      public function inProXML(xml0:XML) : void
      {
         this.pro.inData_byXML(xml0);
         this.drop.inData_byXML(xml0.drop[0]);
         this.allProNameArr = this.pro.getNameArr();
      }
      
      public function afterDeal() : void
      {
         var d0:GeneDefine = null;
         var evo0:GeneDefine = null;
         var first0:GeneDefine = null;
         for each(d0 in this.arr)
         {
            evo0 = d0.getEvoGeneDefine();
            if(Boolean(evo0))
            {
               evo0.beforeDefine = d0;
            }
         }
         for each(d0 in this.arr)
         {
            if(Boolean(d0.beforeDefine))
            {
               first0 = d0.beforeDefine;
               while(Boolean(first0.beforeDefine))
               {
                  first0 = first0.beforeDefine;
               }
               d0.firstDefine = first0;
            }
         }
      }
      
      public function testDispatchPrice() : void
      {
         var d0:GeneDefine = null;
         var cnArr0:Array = null;
         var cn0:String = null;
         var thingsD0:ThingsDefine = null;
         for each(d0 in this.arr)
         {
            if(d0.lowDispaArr.length > 0 || d0.highDispaArr.length > 0)
            {
               cnArr0 = d0.lowDispaArr.concat(d0.highDispaArr);
               for each(cn0 in cnArr0)
               {
                  thingsD0 = Gaming.defineGroup.things.getDefineByCnName(cn0);
                  if(thingsD0.smeltD.price == 0)
                  {
                     trace(thingsD0.cnName + "---------没有价格");
                  }
               }
            }
         }
      }
      
      public function getBookArr() : Array
      {
         var d0:GeneDefine = null;
         var arr0:Array = [];
         for each(d0 in this.arr)
         {
            if(d0.isShowInBookB())
            {
               arr0.push(d0);
            }
         }
         return arr0;
      }
      
      public function getDefine(name0:String) : GeneDefine
      {
         return this.obj[name0];
      }
      
      public function getDefineByTargetName(name0:String) : GeneDefine
      {
         return this.targetObj[name0];
      }
      
      public function getDefineByCn(cn0:String) : GeneDefine
      {
         var d0:GeneDefine = null;
         for each(d0 in this.obj)
         {
            if(d0.cnName == cn0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getRandomGene() : GeneDefine
      {
         return this.arr[int(Math.random() * this.arr.length)];
      }
      
      public function getNormalNameArr() : Array
      {
         var d0:GeneDefine = null;
         var nameArr0:Array = [];
         for each(d0 in this.arr)
         {
            if(d0.lv == 1 && !d0.superB)
            {
               nameArr0.push(d0.name);
            }
         }
         return nameArr0;
      }
      
      public function getAllStrengthenValueByLv(name0:String, lv0:int) : Number
      {
         var n:* = undefined;
         var v0:Number = 0;
         var d0:PropertyArrayDefine = this.strengthenPro.getDefine(name0);
         for(n in d0.dataArr)
         {
            if(n >= lv0)
            {
               break;
            }
            v0 += d0.dataArr[n];
         }
         return v0;
      }
      
      public function getStrengthenMust(name0:String, lv0:int) : MustDefine
      {
         var pd0:PropertyArrayDefine = null;
         var d0:MustDefine = new MustDefine();
         var num0:int = this.getStrengthenDrugNum(name0,lv0);
         if(lv0 <= 99)
         {
            d0.lv = lv0;
            d0.inThingsDataByArr(["strengthenDrug;" + num0]);
            d0.coin = Math.ceil(Gaming.defineGroup.normal.getPlayerCoinIncome(lv0) / 20);
         }
         else
         {
            d0.lv = 99;
            if(name0 == "dps")
            {
               d0.inThingsDataByArr(["intensDrug;" + 10]);
            }
            else
            {
               d0.inThingsDataByArr(["intensDrug;" + 6]);
            }
            pd0 = this.strengthenPro.getDefine(name0);
            if(lv0 > pd0.getDataLen() - 1)
            {
               d0.lv = lv0;
            }
         }
         return d0;
      }
      
      private function getStrengthenDrugNum(name0:String, lv0:int) : int
      {
         var v0:int = this.getPlayerStrengthenDrugNum(lv0);
         if(name0 == "head")
         {
            v0 = Math.ceil(v0 / 6);
         }
         else
         {
            v0 = Math.ceil(v0 / 3);
         }
         return v0;
      }
      
      public function getPlayerStrengthenDrugNum(lv0:int) : int
      {
         return this.strengthenPro.getPropertyValue("drugMust",lv0 - 25);
      }
      
      public function getAllStrengthenDrugNum(name0:String, lv0:int) : int
      {
         var num0:int = 0;
         for(var i:int = 26; i <= lv0; i++)
         {
            num0 += this.getStrengthenDrugNum(name0,i);
         }
         return num0;
      }
      
      public function getIconNum() : int
      {
         var d0:GeneDefine = null;
         var obj0:Object = {};
         var num0:int = 0;
         for each(d0 in this.obj)
         {
            if(!obj0.hasOwnProperty(d0.iconUrl))
            {
               obj0[d0.iconUrl] = 0;
               num0++;
            }
         }
         return num0;
      }
   }
}

