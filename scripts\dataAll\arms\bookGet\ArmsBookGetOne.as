package dataAll.arms.bookGet
{
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.arms.define.ArmsRangeDefine;
   
   public class ArmsBookGetOne
   {
      
      public var name:String = "";
      
      public var evoLv:int = 0;
      
      private var cn:String = "";
      
      public function ArmsBookGetOne()
      {
         super();
      }
      
      public static function panArr(one0:ArmsBookGetOne, arr0:Array) : Boolean
      {
         var da0:Object = null;
         var bb0:Boolean = false;
         for each(da0 in arr0)
         {
            bb0 = panOne(one0,da0);
            if(bb0)
            {
               return true;
            }
         }
         return false;
      }
      
      public static function panOne(one0:ArmsBookGetOne, da0:Object) : Boolean
      {
         var arms0:ArmsData = da0 as ArmsData;
         if(Boolean(arms0))
         {
            if(arms0.name == one0.name && arms0.save.evoLv >= one0.evoLv)
            {
               return true;
            }
         }
         return false;
      }
      
      public function inData(s0:String) : void
      {
         this.name = s0;
         this.evoLv = 0;
         var f0:int = s0.indexOf("*");
         if(f0 > 0)
         {
            this.name = s0.substr(0,f0);
            this.evoLv = int(s0.substr(f0 + 1));
         }
      }
      
      public function getCn() : String
      {
         var d0:ArmsRangeDefine = null;
         var cn0:String = null;
         if(this.cn == "")
         {
            d0 = Gaming.defineGroup.bullet.getArmsRangeDefine(this.name);
            cn0 = d0.def.cnName;
            this.cn = ArmsEvoCtrl.getCnName(cn0,this.evoLv,d0.def);
         }
         return this.cn;
      }
   }
}

