package dataAll._app.outfit.define
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.StringMethod;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipType;
   
   public class OutfitMustDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var type:String = "";
      
      public var partType:String = "";
      
      public var colorArr:Array = [];
      
      public var nameArr:Array = [];
      
      public var lv:int = 0;
      
      public function OutfitMustDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getMustStr() : String
      {
         var str0:String = this.getFirst();
         if(this.colorArr.length > 0)
         {
            str0 += this.getColor(this.colorArr);
         }
         if(this.type == "arms")
         {
            str0 += this.arms_str(this);
         }
         else if(this.type == "equip")
         {
            str0 += this.equip_str(this);
         }
         if(this.lv > 1)
         {
            str0 += "(" + this.lv + "级或" + this.lv + "级以上)";
         }
         return str0 + this.getBack();
      }
      
      private function getColor(colorArr0:Array) : String
      {
         var color0:String = null;
         var cnArr0:Array = [];
         for each(color0 in colorArr0)
         {
            cnArr0.push(EquipColor.getCn(color0) + "色");
         }
         return StringMethod.concatStringArr(cnArr0,999,"或");
      }
      
      private function arms_str(d0:OutfitMustDefine) : String
      {
         var nameArr0:Array = null;
         var name0:String = null;
         var armsD0:ArmsRangeDefine = null;
         var str0:String = "";
         if(d0.nameArr.length > 0)
         {
            nameArr0 = [];
            for each(name0 in d0.nameArr)
            {
               armsD0 = Gaming.defineGroup.bullet.getArmsRangeDefine(name0);
               nameArr0.push(armsD0.def.cnName);
            }
            str0 = TextWay.mixedStringArr(nameArr0,99,ComMethod.color(" 或 ","#FF8040"));
         }
         if(d0.partType != "")
         {
            str0 += Gaming.defineGroup.armsCharger.getCn(d0.partType);
         }
         if(str0 == "")
         {
            str0 = "武器";
         }
         return str0;
      }
      
      private function equip_str(d0:OutfitMustDefine) : String
      {
         var nameArr0:Array = null;
         var name0:String = null;
         var equipD0:EquipDefine = null;
         var str0:String = "";
         if(d0.nameArr.length > 0)
         {
            nameArr0 = [];
            for each(name0 in d0.nameArr)
            {
               equipD0 = Gaming.defineGroup.getAllEquipDefine(name0);
               nameArr0.push(equipD0.cnName);
            }
            str0 = TextWay.mixedStringArr(nameArr0,99,ComMethod.color(" 或 ","#FF8040"));
         }
         if(d0.partType != "" && d0.partType != EquipType.FASHION)
         {
            str0 += EquipType.getCnName(d0.partType);
         }
         return str0;
      }
      
      private function getFirst() : String
      {
         return "";
      }
      
      private function getBack() : String
      {
         return "";
      }
   }
}

