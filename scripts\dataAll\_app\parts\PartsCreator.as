package dataAll._app.parts
{
   import dataAll._app.parts.define.PartsConst;
   import dataAll._app.parts.define.PartsPropertyDefine;
   import dataAll._app.parts.define.PartsPropertyDefineGroup;
   import dataAll._app.parts.define.PartsRare;
   import dataAll._app.parts.define.PartsType;
   import dataAll._app.setting.SettingSave;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsTipType;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.EquipData;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsBatchColor;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ItemsSave;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.save.ThingsSave;
   import dataAll.things.save.ThingsSaveGroup;
   
   public class PartsCreator
   {
      
      public function PartsCreator()
      {
         super();
      }
      
      public static function autoDescomposePan(s0:ItemsSave, dataType0:String) : Array
      {
         var things0:Array = null;
         var autoRightB0:Boolean = Gaming.PG.da.post.haveAutoDesB();
         if(autoRightB0 == false)
         {
            return null;
         }
         var desB0:Boolean = false;
         var setting0:SettingSave = Gaming.PG.save.setting;
         var desArr0:Array = null;
         if(dataType0 == ItemsDataGroup.TYPE_ARMS)
         {
            desArr0 = setting0.arms_batchDecomposeColorArr;
         }
         else
         {
            if(dataType0 != ItemsDataGroup.TYPE_EQUIP)
            {
               return null;
            }
            desArr0 = setting0.equip_batchDecomposeColorArr;
         }
         if(desArr0.indexOf(ItemsBatchColor.auto) >= 0)
         {
            desB0 = ItemsBatchColor.dataPanColorArr(s0,desArr0);
         }
         if(desB0)
         {
            return getDecomposeThingsBy(s0);
         }
         return null;
      }
      
      public static function autoDescomposeGift(d0:GiftAddDefine, dataType0:String) : GiftAddDefine
      {
         var things0:Array = null;
         var thingsName0:String = null;
         var num0:int = 0;
         var new0:GiftAddDefine = null;
         var s0:ItemsSave = d0.itemsSave;
         if(Boolean(s0))
         {
            things0 = autoDescomposePan(s0,dataType0);
            if(Boolean(things0))
            {
               if(things0.length >= 2)
               {
                  thingsName0 = things0[0];
                  num0 = int(things0[1]);
                  new0 = new GiftAddDefine();
                  new0.inData_byStr("parts;" + thingsName0 + ";" + num0);
                  return new0;
               }
            }
         }
         return d0;
      }
      
      public static function getSwapName(name0:String) : String
      {
         var lv0:Number = NaN;
         var new0:String = null;
         var sarr0:Array = name0.split("_");
         var baseLabe0:String = sarr0[0];
         var normalB0:Boolean = PartsType.isNormalPartsB(baseLabe0);
         if(normalB0 && baseLabe0 != PartsType.NORMAL_PARTS)
         {
            lv0 = Number(sarr0[1]);
            if(!isNaN(lv0))
            {
               new0 = PartsType.NORMAL_PARTS + "_" + lv0;
               INIT.TRACE(name0 + " 改名为：" + new0);
               return new0;
            }
            INIT.TRACE("包含零件名称的物品：" + name0);
         }
         return name0;
      }
      
      public static function addPartsAddData(da0:PartsAddData, td0:ThingsDefine, tda0:ThingsData = null, armsDa0:ArmsData = null) : PartsAddData
      {
         var dg0:PartsPropertyDefineGroup = null;
         var gripType0:String = null;
         var d0:PartsPropertyDefine = null;
         var v0:Number = NaN;
         var dpsAdd0:Number = NaN;
         if(td0.isPartsNormalB())
         {
            if(Boolean(tda0))
            {
               dg0 = Gaming.defineGroup.partsProperty;
               gripType0 = PartsType.getWearType(tda0.save.site);
               d0 = dg0.getDeifne(gripType0);
               if(Boolean(d0))
               {
                  v0 = 0;
                  if(Boolean(armsDa0))
                  {
                     v0 = d0.getProValue(tda0.save.getTrueLevel(),armsDa0.save.getTrueLevel(),armsDa0.getColor(),gripType0);
                  }
                  else
                  {
                     v0 = d0.getProValue(tda0.save.getTrueLevel(),1,EquipColor.WHITE,gripType0);
                  }
                  da0.setPro(d0.effectName,v0);
               }
            }
         }
         else
         {
            if(Boolean(td0.getAddObj()))
            {
               da0.inMaxObj(td0.getAddObj());
            }
            if(td0.skillArr.length > 0)
            {
               da0.addNewSkillArr(td0.skillArr);
            }
            if(td0.isPartsSpecialB())
            {
               dpsAdd0 = PartsConst.getSpecialDpsAddByLv(td0.itemsLevel);
               if(dpsAdd0 > 0)
               {
                  da0.dpsMul += dpsAdd0;
               }
            }
            else if(td0.isPartsRareB())
            {
               da0.addRareSkillArr(td0.skillArr);
               if(Boolean(tda0))
               {
                  PartsRare.addDeal(da0,tda0,armsDa0);
               }
            }
         }
         return da0;
      }
      
      public static function getPartsAddDataByDataArr(arr0:Array, armsDa0:ArmsData = null) : PartsAddData
      {
         var tda0:ThingsData = null;
         var da0:PartsAddData = new PartsAddData();
         for each(tda0 in arr0)
         {
            addPartsAddData(da0,tda0.save.getDefine(),tda0,armsDa0);
         }
         return da0;
      }
      
      public static function getPartsGatherText(d0:ThingsDefine, da0:ThingsData = null) : String
      {
         var dg0:PartsPropertyDefineGroup = null;
         var gripType0:String = null;
         var pd0:PartsPropertyDefine = null;
         var add_da0:PartsAddData = null;
         var armsDa0:ArmsData = null;
         if(Boolean(da0))
         {
            armsDa0 = da0.getPartsFatherArmsData();
         }
         var str0:String = "";
         if(d0.isPartsNormalB())
         {
            if(Boolean(da0) && da0.getPlaceType() == ItemsDataGroup.PLACE_WEAR)
            {
               dg0 = Gaming.defineGroup.partsProperty;
               gripType0 = PartsType.getWearType(da0.save.site);
               pd0 = dg0.getDeifne(gripType0);
               str0 += "提升武器的" + pd0.effectCnName + "。";
            }
            else
            {
               str0 += PartsType.normalDescrip;
            }
         }
         else
         {
            add_da0 = new PartsAddData();
            addPartsAddData(add_da0,d0,da0,armsDa0);
            str0 += PartsType.getTypeCn(d0.objType,true) + "\n";
            str0 += add_da0.getGatherText();
         }
         if(d0.description != "")
         {
            str0 += d0.description;
         }
         if(d0.isPartsRareB())
         {
            str0 += "\n<purple 前往“图鉴>百科>物品与掉落>稀有零件”查看属性说明/>";
         }
         return str0;
      }
      
      public static function getRareGather(obj0:Object, color0:String = "gray", compareObj0:Object = null, armsD0:ArmsDefine = null, tipType0:String = "no") : String
      {
         var d0:PropertyArrayDefine = null;
         var n:String = null;
         var valueReplace0:String = null;
         var canUseB0:Boolean = false;
         var cv0:* = undefined;
         var s0:String = null;
         var proDefineArr0:Array = Gaming.defineGroup.partsProperty.rarePro.propertyArr;
         var str0:String = "";
         for each(d0 in proDefineArr0)
         {
            n = d0.name;
            if(obj0.hasOwnProperty(n))
            {
               valueReplace0 = "";
               canUseB0 = true;
               if(Boolean(armsD0))
               {
                  canUseB0 = PartsAddData.canUseGather(d0,armsD0);
               }
               if(canUseB0 == false)
               {
                  if(ArmsTipType.noUseHideB(tipType0))
                  {
                     continue;
                  }
                  valueReplace0 = "<red 无效/>";
               }
               cv0 = null;
               if(canUseB0)
               {
                  if(Boolean(compareObj0) && Boolean(compareObj0.hasOwnProperty(n)))
                  {
                     cv0 = compareObj0[n];
                  }
               }
               s0 = EquipPropertyDataCreator.getOneTextRange(d0,obj0[n],cv0,true,color0,valueReplace0);
               if(s0 != "")
               {
                  if(str0 != "")
                  {
                     str0 += "\n";
                  }
                  str0 += s0;
               }
            }
         }
         return str0;
      }
      
      public static function getDecomposeArr(da0:IO_ItemsData, haveSelfPartsB0:Boolean) : Array
      {
         if(da0 is ArmsData)
         {
            return getArmsDataDecomposeArr(da0 as ArmsData,haveSelfPartsB0);
         }
         if(da0 is EquipData)
         {
            return getEquipDataDecomposeArr(da0 as EquipData);
         }
         return [];
      }
      
      public static function canDecomposePan(da0:IO_ItemsData) : Boolean
      {
         if(da0 is ArmsData)
         {
            return true;
         }
         if(da0 is EquipData)
         {
            return EquipType.canDecomposePartsArr.indexOf(da0.getSave().getChildType()) >= 0;
         }
         return false;
      }
      
      public static function getDecomposeThingsBy(s0:ItemsSave) : Array
      {
         if(s0 is ArmsSave)
         {
            return getDecomposeThingsByArmsSave(s0 as ArmsSave);
         }
         if(s0 is EquipSave)
         {
            return getDecomposeThingsByEquipSave(s0 as EquipSave);
         }
         return null;
      }
      
      private static function getArmsDataDecomposeArr(da0:ArmsData, haveSelfPartsB0:Boolean = false) : Array
      {
         var s0:ArmsSave = da0.save;
         var things0:Array = getDecomposeThingsByArmsSave(s0);
         var thingsS0:ThingsSave = thingsToSave(things0);
         var saveArr0:Array = [thingsS0];
         if(haveSelfPartsB0)
         {
            saveArr0 = saveArr0.concat(da0.partsData.saveGroup.getSaveArrClone());
            saveArr0 = ThingsSaveGroup.setMergeSaveArrBySame(saveArr0);
         }
         return saveArr0;
      }
      
      private static function getDecomposeThingsByArmsSave(s0:ArmsSave) : Array
      {
         var armsType_d0:ArmsChargerDefine = Gaming.defineGroup.armsCharger.getDefine(s0.getArmsType());
         var partsMul0:Number = armsType_d0.decomposeArr.length;
         var lv0:int = getDecomposeLv(s0.color,s0.getTrueLevel(),Gaming.PG.da.level);
         return getThings(PartsType.NORMAL_PARTS,lv0,partsMul0);
      }
      
      private static function getEquipDataDecomposeArr(da0:EquipData) : Array
      {
         var s0:EquipSave = da0.save;
         var things0:Array = getDecomposeThingsByEquipSave(s0);
         var thingsS0:ThingsSave = thingsToSave(things0);
         return [thingsS0];
      }
      
      private static function getDecomposeThingsByEquipSave(s0:EquipSave) : Array
      {
         var partsMul0:Number = EquipType.getDecomposePartNum(s0.partType);
         var addLv0:int = EquipColor.moreBlackB(s0.color) ? 0 : -2;
         var lv0:int = getDecomposeLv(s0.color,s0.getTrueLevel(),Gaming.PG.da.level,addLv0);
         return getThings(PartsType.NORMAL_PARTS,lv0,partsMul0);
      }
      
      public static function getDecomposeLv(color0:String, arms_lv0:int, heroLv0:int, extraAddLv0:int = 0) : int
      {
         var minLv0:int = PartsConst.minLv;
         var armsLvAdd0:int = getDecomposeArmsLvAdd(arms_lv0);
         var colorAdd0:int = getDecomposeColorAdd(color0);
         var v0:int = arms_lv0 + armsLvAdd0 + colorAdd0 + extraAddLv0;
         if(v0 < minLv0)
         {
            v0 = minLv0;
         }
         return v0;
      }
      
      private static function getDecomposeArmsLvAdd(arms_lv0:int) : int
      {
         var lv0:int = 0;
         if(arms_lv0 >= 60)
         {
            lv0 = -int((arms_lv0 - 50) / 5 + 1);
         }
         else if(arms_lv0 >= 40)
         {
            lv0 = -int((arms_lv0 - 40) / 10 + 1);
         }
         var maxDecomposeLv0:int = PartsConst.getMaxDecomposeLv();
         var nowMax0:int = arms_lv0 + lv0 - 4;
         if(nowMax0 > maxDecomposeLv0 - 5)
         {
            lv0 -= nowMax0 - (maxDecomposeLv0 - 5);
         }
         return lv0;
      }
      
      public static function getDecomposeColorAdd(color0:String) : int
      {
         var lv0:int = -8;
         if(color0 == EquipColor.WHITE || color0 == EquipColor.GREEN || color0 == EquipColor.BLUE)
         {
            lv0 = -8;
         }
         else if(color0 == EquipColor.PURPLE)
         {
            lv0 = -7;
         }
         else if(color0 == EquipColor.ORANGE)
         {
            lv0 = -6;
         }
         else if(color0 == EquipColor.RED)
         {
            lv0 = -4;
         }
         else if(color0 == EquipColor.BLACK)
         {
            lv0 = -4;
         }
         else if(color0 == EquipColor.DARKGOLD)
         {
            lv0 = -3;
         }
         else if(color0 == EquipColor.PURGOLD || color0 == EquipColor.YAGOLD)
         {
            lv0 = -3;
         }
         return lv0;
      }
      
      public static function test() : void
      {
         for(var i:int = 1; i <= 85; i++)
         {
            trace(getLvAndNum(i));
         }
      }
      
      public static function getTempData(name0:String, num0:int = 1) : ThingsData
      {
         var s0:ThingsSave = new ThingsSave();
         var t_d0:ThingsDefine = Gaming.defineGroup.things.getDefine(name0);
         s0.inData_byDefine(t_d0);
         s0.nowNum = num0;
         var da0:ThingsData = new ThingsData();
         da0.inData_bySave(s0,Gaming.PG.da,null);
         return da0;
      }
      
      private static function thingsToSave(tarr0:Array) : ThingsSave
      {
         var s0:ThingsSave = new ThingsSave();
         var thingsName0:String = tarr0[0];
         var num0:int = int(tarr0[1]);
         var t_d0:ThingsDefine = Gaming.defineGroup.things.getDefine(thingsName0);
         s0.inData_byDefine(t_d0);
         s0.nowNum = num0;
         return s0;
      }
      
      private static function getThings(name0:String, lv0:int, numMul0:Number = 1) : Array
      {
         var cLv0:int = PartsConst.cLv;
         var cx0:int = lv0 % cLv0;
         var true_lv0:int = int(lv0 / cLv0) * cLv0;
         var t_d0:ThingsDefine = Gaming.defineGroup.things.getDefine(name0 + "_" + true_lv0);
         if(!(t_d0 is ThingsDefine))
         {
            t_d0 = Gaming.defineGroup.things.getDefine(name0);
         }
         var thingsName0:String = t_d0.name;
         var num0:int = Math.pow(2,cx0) * numMul0;
         return [thingsName0,num0];
      }
      
      public static function getLvAndNum(lv0:int) : Array
      {
         var maxLv0:int = PartsConst.getMaxPartsLevel();
         var sLv0:int = 0;
         if(lv0 > maxLv0)
         {
            sLv0 = lv0 - maxLv0;
            lv0 = maxLv0;
         }
         var cLv0:int = PartsConst.cLv;
         var cx0:int = lv0 % cLv0;
         var true_lv0:int = int(lv0 / cLv0) * cLv0;
         var num0:int = Math.pow(2,cx0 + sLv0);
         return [true_lv0,num0];
      }
      
      public static function getGiftThingsArrByPartArr(nameArr0:Array, lv0:int) : Array
      {
         var name0:String = null;
         var lvNumArr0:Array = getLvAndNum(lv0);
         var trueLv0:int = int(lvNumArr0[0]);
         var num0:int = int(lvNumArr0[1]);
         var arr0:Array = [];
         for each(name0 in nameArr0)
         {
            arr0.push(name0 + "_" + trueLv0 + ";" + num0);
         }
         return arr0;
      }
      
      public static function getGiftAddDefineGroupByPartArr(nameArr0:Array, lv0:int) : GiftAddDefineGroup
      {
         var name0:String = null;
         var d0:GiftAddDefine = null;
         var lvNumArr0:Array = getLvAndNum(lv0);
         var trueLv0:int = int(lvNumArr0[0]);
         var num0:int = int(lvNumArr0[1]);
         var arr0:Array = [];
         var dg0:GiftAddDefineGroup = new GiftAddDefineGroup();
         for each(name0 in nameArr0)
         {
            d0 = new GiftAddDefine();
            dg0.addGiftByStr("parts;" + name0 + "_" + trueLv0 + ";" + num0);
         }
         return dg0;
      }
      
      public static function getThingsDataByPartsDefine(d0:ThingsDefine, num0:int = 1) : ThingsData
      {
         var s0:ThingsSave = new ThingsSave();
         s0.inData_byDefine(d0);
         s0.nowNum = num0;
         var da0:ThingsData = new ThingsData();
         da0.inData_bySave(s0,Gaming.PG.da,null);
         return da0;
      }
   }
}

