package dataAll.arms.define
{
   import dataAll.pro.PropertyArrayDefine;
   
   public class ArmsTipDefine
   {
      
      public static var _hurtRatio:PropertyArrayDefine = new PropertyArrayDefine();
      
      public static var _shootSpeed:PropertyArrayDefine = new PropertyArrayDefine();
      
      public static var _capacity:PropertyArrayDefine = new PropertyArrayDefine();
      
      public static var _reloadGap:PropertyArrayDefine = new PropertyArrayDefine();
      
      public static var _shootWidth:PropertyArrayDefine = new PropertyArrayDefine();
      
      public static var _precision:PropertyArrayDefine = new PropertyArrayDefine();
      
      public static var pro_arr:Array = ["hurtRatio","shootSpeed","capacity","reloadGap","precision","shootWidth"];
      
      public var hurtRatio:Number = 0;
      
      public var shootSpeed:Number = 0;
      
      public var capacity:Number = 0;
      
      public var reloadGap:Number = 0;
      
      public var shootWidth:Number = 0;
      
      public var precision:Number = 0;
      
      public function ArmsTipDefine()
      {
         super();
      }
      
      public static function init() : void
      {
         _hurtRatio.cnName = "单发伤害";
         _hurtRatio.gatherColor = "orangeness";
         _hurtRatio.wanB = true;
         _shootSpeed.cnName = "射击速度";
         _shootSpeed.unit = "发/秒";
         _shootSpeed.gatherColor = "purpleness";
         _shootSpeed.fixedNum = 3;
         _capacity.cnName = "弹容";
         _capacity.gatherColor = "greeness";
         _reloadGap.cnName = "装弹时间";
         _reloadGap.unit = "秒";
         _reloadGap.bigBestB = false;
         _reloadGap.fixedNum = 3;
         _reloadGap.gatherColor = "gray";
         _shootWidth.cnName = "射程";
         _shootWidth.unit = "";
         _shootWidth.gatherColor = "gray";
         _precision.cnName = "精准度";
         _precision.unit = "%";
         _precision.gatherColor = "gray";
      }
      
      public static function getDefine(name0:String) : PropertyArrayDefine
      {
         return ArmsTipDefine["_" + name0];
      }
   }
}

