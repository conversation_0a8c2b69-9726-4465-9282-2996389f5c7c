package dataAll.ui.text
{
   import com.sounto.utils.StringMethod;
   import dataAll.ui.color.UIColor;
   
   public class MixedTextLabel
   {
      
      public static const REPLACE:String = "replace";
      
      public static const ICON:String = "icon";
      
      private static const temp:MixedTextLabel = new MixedTextLabel();
      
      public static const ICON_CHAR:String = "`";
      
      private static var firstChar:String = "<";
      
      private static var endChar:String = ">";
      
      private static var typeFirstObj:Object = {};
      
      private static var replaceObj:Object = {};
      
      private static var iconWidthObj:Object = {};
      
      private var allStr:String = "";
      
      private var nowLabel:String = "";
      
      public var state:String = MixedTextState.NO;
      
      public var type:String = "replace";
      
      public var replaceStr:String = "";
      
      public var replacePureStr:String = "";
      
      public var label:String = "";
      
      public function MixedTextLabel()
      {
         super();
      }
      
      public static function staticInit() : void
      {
         var colorName0:String = null;
         replaceObj["</>"] = "</font>";
         for each(colorName0 in UIColor.colorNameArr)
         {
            replaceObj["<" + colorName0 + ">"] = "<font color=\'" + UIColor.getHtml(colorName0) + "\'>";
         }
         typeFirstObj[ICON] = "i_";
         iconWidthObj["chargerBox"] = 48;
      }
      
      public static function mixedToHtmlText(t0:String) : String
      {
         var char0:String = null;
         var htmlText0:String = "";
         var arrLen0:int = t0.length;
         var l0:MixedTextLabel = temp;
         l0.init();
         for(var i:int = 0; i < arrLen0; i++)
         {
            char0 = t0.charAt(i);
            l0.inChar(i,char0);
            if(l0.state == MixedTextState.NO)
            {
               htmlText0 += char0;
            }
            else if(l0.state == MixedTextState.OVER)
            {
               htmlText0 += l0.replaceStr;
               l0.init();
            }
            else if(l0.state == MixedTextState.ERROR)
            {
               l0.init();
               return htmlText0;
            }
         }
         return htmlText0;
      }
      
      public function init() : void
      {
         this.allStr = "";
         this.nowLabel = "";
         this.state = MixedTextState.NO;
         this.label = "";
         this.type = REPLACE;
         this.replaceStr = "";
         this.replacePureStr = "";
      }
      
      public function getIconSpaceNum() : int
      {
         if(iconWidthObj.hasOwnProperty(this.label))
         {
            return Math.ceil(iconWidthObj[this.label] / 6);
         }
         return 2;
      }
      
      public function inChar(i0:int, char0:String) : void
      {
         if(this.state == MixedTextState.NO)
         {
            if(char0 == firstChar)
            {
               this.allStr += char0;
               this.state = MixedTextState.FIND_END;
            }
         }
         else if(this.state == MixedTextState.FIND_END)
         {
            this.allStr += char0;
            if(char0 == firstChar)
            {
               this.state = MixedTextState.ERROR;
            }
            else if(char0 == endChar)
            {
               this.overDeal();
               this.state = MixedTextState.OVER;
            }
            else
            {
               this.nowLabel += char0;
            }
         }
      }
      
      private function overDeal() : void
      {
         var n:* = undefined;
         var first0:String = null;
         for(n in typeFirstObj)
         {
            first0 = typeFirstObj[n];
            if(this.nowLabel.indexOf(first0) == 0)
            {
               this.type = n;
               this.label = this.nowLabel.substr(first0.length);
               break;
            }
         }
         if(this.type == REPLACE)
         {
            this.label = this.nowLabel;
         }
         if(this.type == REPLACE)
         {
            if(replaceObj.hasOwnProperty(this.allStr))
            {
               this.replaceStr = replaceObj[this.allStr];
            }
            else
            {
               this.replaceStr = this.allStr;
            }
            this.replacePureStr = "";
         }
         else if(this.type == ICON)
         {
            this.replaceStr = StringMethod.getRepeartStr(this.getIconSpaceNum(),ICON_CHAR);
            this.replacePureStr = this.replaceStr;
         }
      }
   }
}

