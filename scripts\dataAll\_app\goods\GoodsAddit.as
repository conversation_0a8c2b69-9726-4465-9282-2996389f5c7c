package dataAll._app.goods
{
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._player.PlayerData;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.device.DeviceDataCreator;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.equip.vehicle.VehicleSave;
   import dataAll.equip.weapon.WeaponDataCreator;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.things.ThingsData;
   import dataAll.things.ThingsUseCtrl;
   import dataAll.things.define.ThingsDefine;
   import gameAll.arms.GameArmsCtrl;
   
   public class GoodsAddit
   {
      
      public function GoodsAddit()
      {
         super();
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      public static function addByGoodsData(da0:GoodsData, allowAutoUseB0:Boolean = false) : void
      {
         var pd0:PlayerData = Gaming.PG.da;
         var d0:GoodsDefine = da0.def;
         var type0:String = d0.dataType;
         var fun0:Function = GoodsAddit["add_" + type0];
         if(fun0 is Function)
         {
            fun0(da0,allowAutoUseB0);
         }
      }
      
      private static function add_base(da0:GoodsData, allowAutoUseB0:Boolean) : void
      {
         var gift0:GiftAddDefine = new GiftAddDefine();
         gift0.inData_byStr("base;" + da0.def.defineLabel + ";" + da0.getTrueNum());
         GiftAddit.addByDefine(gift0,Gaming.PG.da);
      }
      
      private static function add_things(da0:GoodsData, allowAutoUseB0:Boolean) : void
      {
         var t_da0:ThingsData = null;
         var pd0:PlayerData = Gaming.PG.da;
         var d0:GoodsDefine = da0.def;
         var td0:ThingsDefine = Gaming.defineGroup.things.getDefine(d0.defineLabel);
         if(td0.isPartsB())
         {
            add_parts(da0,allowAutoUseB0);
         }
         else
         {
            t_da0 = pd0.thingsBag.addDataByName(d0.defineLabel,da0.getTrueNum()) as ThingsData;
            if(d0.autoUseB && allowAutoUseB0)
            {
               ThingsUseCtrl.useThings(pd0.thingsBag,t_da0.save.name,da0.nowNum,true);
            }
         }
      }
      
      private static function add_parts(da0:GoodsData, allowAutoUseB0:Boolean) : void
      {
         var pd0:PlayerData = Gaming.PG.da;
         var d0:GoodsDefine = da0.def;
         pd0.partsBag.addDataByName(d0.defineLabel,da0.getTrueNum());
      }
      
      private static function add_equip(da0:GoodsData, allowAutoUseB0:Boolean) : void
      {
         var pd0:PlayerData = Gaming.PG.da;
         var d0:GoodsDefine = da0.def;
         var equipD0:EquipDefine = Gaming.defineGroup.getAllEquipDefine(d0.defineLabel);
         var equipS0:EquipSave = null;
         if(equipD0.isNormalB())
         {
            equipS0 = Gaming.defineGroup.equipCreator.getSuperSave("red",pd0.level,d0.defineLabel,false);
         }
         else
         {
            equipS0 = GiftAddit.getEquipSaveByName(d0.defineLabel,pd0.level);
         }
         equipS0.lockB = true;
         equipS0.shopB = true;
         equipS0.setBuyPrice(da0.getOnePrice());
         pd0.equipBag.addSave(equipS0);
      }
      
      private static function add_fashion(da0:GoodsData, allowAutoUseB0:Boolean) : void
      {
         var equipS0:EquipSave = null;
         var pd0:PlayerData = Gaming.PG.da;
         var d0:GoodsDefine = da0.def;
         for(var i:int = 0; i < da0.getTrueNum(); i++)
         {
            equipS0 = Gaming.defineGroup.equipCreator.getFashionSave(d0.defineLabel);
            equipS0.lockB = true;
            equipS0.shopB = true;
            equipS0.setBuyPrice(da0.getOnePrice());
            pd0.equipBag.addSave(equipS0);
         }
      }
      
      private static function add_device(da0:GoodsData, allowAutoUseB0:Boolean) : void
      {
         var equipS0:EquipSave = null;
         var pd0:PlayerData = Gaming.PG.da;
         var d0:GoodsDefine = da0.def;
         for(var i:int = 0; i < da0.getTrueNum(); i++)
         {
            equipS0 = DeviceDataCreator.getSave(d0.defineLabel,1);
            equipS0.lockB = true;
            equipS0.shopB = true;
            equipS0.setBuyPrice(da0.getOnePrice());
            pd0.equipBag.addSave(equipS0);
         }
      }
      
      private static function add_weapon(da0:GoodsData, allowAutoUseB0:Boolean) : void
      {
         var equipS0:EquipSave = null;
         var pd0:PlayerData = Gaming.PG.da;
         var d0:GoodsDefine = da0.def;
         for(var i:int = 0; i < da0.getTrueNum(); i++)
         {
            equipS0 = WeaponDataCreator.getSave(d0.defineLabel,1);
            equipS0.lockB = true;
            equipS0.shopB = true;
            equipS0.setBuyPrice(da0.getOnePrice());
            pd0.equipBag.addSave(equipS0);
         }
      }
      
      private static function add_vehicle(da0:GoodsData, allowAutoUseB0:Boolean) : void
      {
         var s0:VehicleSave = null;
         var pd0:PlayerData = Gaming.PG.da;
         var d0:GoodsDefine = da0.def;
         for(var i:int = 0; i < da0.getTrueNum(); i++)
         {
            s0 = VehicleDataCreator.getSave(d0.defineLabel,pd0.level);
            s0.lockB = true;
            s0.shopB = true;
            s0.setBuyPrice(da0.getOnePrice());
            pd0.equipBag.addSave(s0);
         }
      }
      
      private static function add_arms(da0:GoodsData, allowAutoUseB0:Boolean) : void
      {
         var pd0:PlayerData = Gaming.PG.da;
         var lv0:int = pd0.level;
         var d0:GoodsDefine = da0.def;
         var s0:ArmsSave = Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(lv0,d0.defineLabel);
         s0.lockB = true;
         s0.shopB = true;
         s0.setBuyPrice(da0.getOnePrice());
         pd0.armsBag.addSave(s0);
         GameArmsCtrl.addArmsSaveResoure(s0);
      }
      
      private static function add_gene(da0:GoodsData, allowAutoUseB0:Boolean) : void
      {
         var pd0:PlayerData = Gaming.PG.da;
         var d0:GoodsDefine = da0.def;
         var s0:GeneSave = Gaming.defineGroup.geneCreator.getSuperSave(pd0.level,d0.defineLabel);
         s0.setBuyPrice(da0.getOnePrice());
         pd0.geneBag.addSave(s0);
      }
      
      private static function add_armsSkin(da0:GoodsData, allowAutoUseB0:Boolean) : void
      {
         PD.main.addArmsSkin(da0.def.defineLabel);
      }
   }
}

