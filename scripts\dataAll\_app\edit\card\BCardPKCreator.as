package dataAll._app.edit.card
{
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ArrayMethod;
   
   public class BCardPKCreator
   {
      
      public static const bcardBattle:String = "bcardBattle";
      
      public static const bossCardPK:String = "bossCardPK";
      
      private static var ARR:Array = [];
      
      private static var OBJ:Object = {};
      
      private static var fatherArrObj:Object = {};
      
      private static const randomStarArr:Array = [3,3,4,4,5,5,5,5,6,6,6,7,7,7,8,8,8,8];
      
      public function BCardPKCreator()
      {
         super();
      }
      
      public static function openUI() : void
      {
      }
      
      public static function inXml(xml0:XML) : void
      {
         var i:* = undefined;
         var father0:String = null;
         var thingsXML0:XMLList = null;
         var index0:int = 0;
         var n:* = undefined;
         var da0:BossCardData = null;
         var s0:BossCardSave = null;
         var fatherXML0:XMLList = xml0["father"];
         for(i in fatherXML0)
         {
            father0 = fatherXML0[i].@name;
            thingsXML0 = fatherXML0[i]["body"];
            index0 = 0;
            for(n in thingsXML0)
            {
               da0 = new BossCardData();
               s0 = new BossCardSave();
               s0.inData_byXML(thingsXML0[n]);
               da0.inData_bySave(s0);
               OBJ[da0.id] = da0;
               ARR.push(da0);
               if(!fatherArrObj.hasOwnProperty(father0))
               {
                  fatherArrObj[father0] = [];
               }
               fatherArrObj[father0].push(da0);
            }
         }
      }
      
      public static function getData(id0:String) : BossCardData
      {
         return OBJ[id0];
      }
      
      public static function getArr() : Array
      {
         return ARR;
      }
      
      public static function getArrByFather(f0:String) : Array
      {
         return fatherArrObj[f0];
      }
      
      public static function randomAdd() : void
      {
         var first0:int = 132;
         var firstDate0:StringDate = new StringDate("2025-06-02");
         var str0:String = "";
         for(var i:int = 0; i < 30; i++)
         {
            str0 += "\n\t<father name=\"" + (i + first0) + "\" date=\"" + firstDate0.getDateStr() + "\">";
            str0 += randomAddOne();
            str0 += "\n\t</father>";
            firstDate0.addDay(7);
         }
         INIT.tempTrace(str0);
      }
      
      private static function randomAddOne() : String
      {
         var setBodyName0:String = null;
         var star0:int = 0;
         var s0:BossCardSave = null;
         var j0:String = null;
         var haveArr0:Array = ["BoomSkullS"];
         var str0:String = "";
         var len0:int = int(randomStarArr.length);
         for(var i:int = 0; i < len0; i++)
         {
            setBodyName0 = "";
            star0 = ArrayMethod.getElementLimit(randomStarArr,i);
            s0 = BossCardCreator.getSaveStar(star0,haveArr0,setBodyName0);
            s0.id = i + "";
            ArrayMethod.addNoRepeatInArr(haveArr0,s0.n);
            j0 = "\t\t" + s0.getXMLStr();
            str0 += "\n" + j0;
         }
         return str0;
      }
   }
}

