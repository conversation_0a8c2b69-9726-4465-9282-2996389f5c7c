package dataAll._app.book
{
   import com.sounto.utils.ArrayMethod;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.equip.define.EquipColor;
   import dataAll.ui.text.ProTipType;
   
   public class BookDefineGroup
   {
      
      private var obj:Object = {};
      
      private var arr:Array = [];
      
      private var fatherArrObj:Object = {};
      
      public function BookDefineGroup()
      {
         super();
      }
      
      public function saveBookDefine() : void
      {
         this.saveArms();
      }
      
      private function addBook(d0:IO_BookDefine, father0:String) : void
      {
         this.obj[d0.getBookId()] = d0;
         this.arr.push(d0);
         this.addInFatherArr(d0,father0);
      }
      
      private function addInFatherArr(d0:IO_BookDefine, father0:String) : void
      {
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
         }
         this.fatherArrObj[father0].push(d0);
      }
      
      public function getDefine(id0:String) : IO_BookDefine
      {
         return this.obj[id0];
      }
      
      public function getArrByFather(father0:String) : Array
      {
         return this.fatherArrObj[father0];
      }
      
      public function getArrByMoreLabel(type0:String, childType0:String) : Array
      {
         var first0:String = null;
         if(type0 == BookFather.arms)
         {
            first0 = childType0.replace("Arms","");
            return this.getArmsArrByFirst(first0);
         }
         return [];
      }
      
      private function getArmsArrByFirst(first0:String) : Array
      {
         var d0:IO_BookDefine = null;
         var armsD0:ArmsDefine = null;
         var sim0:SimBookDefine = null;
         var addB0:Boolean = false;
         var arr0:Array = this.getArrByFather(BookFather.arms);
         var rr0:Array = [];
         for each(d0 in arr0)
         {
            armsD0 = d0 as ArmsDefine;
            if(armsD0.armsType != ArmsType.props)
            {
               if(Boolean(armsD0))
               {
                  addB0 = false;
                  if(armsD0.color == first0)
                  {
                     if(first0 == EquipColor.YAGOLD)
                     {
                        if(!armsD0.isUltiB())
                        {
                           addB0 = true;
                        }
                     }
                     else if(first0 == EquipColor.BLACK)
                     {
                        if(!armsD0.isZodiacB())
                        {
                           if(armsD0.evoMaxLv <= 0)
                           {
                              addB0 = true;
                           }
                        }
                     }
                     else
                     {
                        addB0 = true;
                     }
                  }
                  if(first0 == "ulti")
                  {
                     if(armsD0.isUltiB())
                     {
                        addB0 = true;
                     }
                  }
                  else if(first0 == "year")
                  {
                     if(armsD0.isZodiacB())
                     {
                        addB0 = true;
                     }
                  }
                  else if(first0 == "cons")
                  {
                     if(armsD0.isConsB())
                     {
                        addB0 = true;
                     }
                  }
                  else if(first0 == "blackEvo")
                  {
                     if(armsD0.color == EquipColor.BLACK && armsD0.evoMaxLv > 0)
                     {
                        addB0 = true;
                     }
                  }
                  if(addB0)
                  {
                     rr0.push(d0);
                  }
               }
               sim0 = d0 as SimBookDefine;
               if(Boolean(sim0) && first0 == EquipColor.DARKGOLD)
               {
                  rr0.push(sim0);
               }
            }
         }
         rr0.sort(this.sortArmsLvFun);
         return rr0;
      }
      
      private function sortArmsLvFun(a:ArmsDefine, b:ArmsDefine) : int
      {
         return ArrayMethod.sortNumberFun(b.composeLv,a.composeLv);
      }
      
      private function saveArms() : void
      {
         var arr0:Array = null;
         var rangeD0:ArmsRangeDefine = null;
         var armsD0:ArmsDefine = null;
         var obj0:Object = Gaming.defineGroup.bullet.rangeTypeObj;
         for each(arr0 in obj0)
         {
            for each(rangeD0 in arr0)
            {
               armsD0 = rangeD0.def;
               if(EquipColor.moreColorPan(armsD0.color,EquipColor.RED))
               {
                  this.addBook(armsD0,BookFather.arms);
               }
            }
         }
         this.addSimArms();
      }
      
      private function addSimArms() : void
      {
      }
      
      private function getNormalSimArms(getMethodStr0:String, color0:String = "", lv0:int = 0) : SimBookDefine
      {
         var d0:SimBookDefine = new SimBookDefine();
         d0.iconUrl = "specialGun/unknowArms";
         d0.imgUrl = d0.iconUrl;
         var str0:String = "";
         str0 += ProTipType.getBookTitleMixed("属性");
         str0 += "\n类型：未知新型";
         if(color0 != "")
         {
            str0 += "\n成色：" + EquipColor.getColorCn(color0);
         }
         if(lv0 > 0)
         {
            str0 += "\n等级：" + lv0 + "级";
         }
         str0 += "\n\n" + ProTipType.getBookTitleMixed("获得方式");
         str0 += "\n" + getMethodStr0;
         d0.info = str0;
         return d0;
      }
   }
}

