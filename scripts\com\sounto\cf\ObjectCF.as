package com.sounto.cf
{
   import com.common.data.Base64;
   
   public class ObjectCF
   {
      
      private var varObj:Object = {};
      
      public function ObjectCF()
      {
         super();
      }
      
      public function setAttribute(varName:String, varValue:Object) : *
      {
         var v0:String = null;
         var tmpObj:Object = null;
         if(varValue != null)
         {
            v0 = AscCF.encodeBase64(Base64.encodeObject(varValue));
            tmpObj = {"value":v0};
         }
         this.varObj[varName] = tmpObj;
      }
      
      public function getAttribute(varName:String) : Object
      {
         if(this.varObj[varName] == null)
         {
            return 0;
         }
         return Base64.decodeObject(AscCF.decodeBase64(this.varObj[varName].value));
      }
   }
}

