package dataAll.body.define
{
   import com.sounto.utils.ClassProperty;
   
   public class BodyMotionDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var F_G:Number = 1;
      
      public var F_I:Number = 1;
      
      public var F_F:Number = 0.5;
      
      public var F_AIR:Number = 0;
      
      public var tween:Number = 100;
      
      public var jumpDelayT:Number = 0.1;
      
      public var moveWhenVB:Boolean = false;
      
      public var jumpMul:Number = 1;
      
      public var vRan:Number = 0.2;
      
      public var imgRaB:Boolean = false;
      
      public var dieEN:int = 0;
      
      public function BodyMotionDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
   }
}

