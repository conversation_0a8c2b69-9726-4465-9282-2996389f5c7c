package dataAll._app.edit.arms
{
   import dataAll._app.edit.TorDataGroup;
   import dataAll._app.edit.TorSave;
   import dataAll._app.edit.tor.TorEditMethod;
   import dataAll._base.OneData;
   import dataAll._base.OneSaveGroup;
   import dataAll.arms.define.ArmsRangeDefine;
   
   public class ArmsTorDataGroup extends TorDataGroup
   {
      
      protected var armsSaveG:ArmsTorSaveGroup = null;
      
      public function ArmsTorDataGroup()
      {
         super();
         dataClass = ArmsTorData;
      }
      
      override public function inData_bySave(sg0:OneSaveGroup) : void
      {
         this.armsSaveG = sg0 as ArmsTorSaveGroup;
         super.inData_bySave(sg0);
      }
      
      override protected function getNewData() : OneData
      {
         var da0:ArmsTorData = super.getNewData() as ArmsTorData;
         da0.setFatherG(this);
         return da0;
      }
      
      public function newArmsData(baseLabel0:String, uidx0:String) : ArmsTorData
      {
         var s0:TorSave = new TorSave();
         var name0:String = ArmsEditMethod.getArmsName(saveG.getNewLastId(),uidx0);
         INIT.tempTrace("添加：" + baseLabel0 + "  " + name0);
         s0.initName(name0,baseLabel0);
         return addSave(s0) as ArmsTorData;
      }
      
      public function addPan() : String
      {
         var max0:int = 12;
         if(arr.length >= max0)
         {
            return "最多只能添加" + max0 + "个。";
         }
         return "";
      }
      
      override protected function removeDataEvent(da0:OneData) : void
      {
         super.removeDataEvent(da0);
         var armsDa0:ArmsTorData = da0 as ArmsTorData;
         var range0:ArmsRangeDefine = armsDa0.getRangeDef();
         if(Boolean(range0))
         {
            Gaming.defineGroup.bullet.delOutRange(range0);
         }
      }
      
      public function getArmsDefineArr() : Array
      {
         var da0:ArmsTorData = null;
         var arr0:Array = [];
         for each(da0 in arr)
         {
            arr0.push(da0.getArmsDef());
         }
         return arr0;
      }
      
      public function mainPan(da0:ArmsTorData) : Boolean
      {
         return da0.getName() == this.armsSaveG.mId;
      }
      
      public function setMain(da0:ArmsTorData) : void
      {
         this.armsSaveG.mId = da0.getName();
      }
      
      public function clearMain() : void
      {
         this.armsSaveG.mId = "";
      }
      
      public function getMain() : ArmsTorData
      {
         var da0:ArmsTorData = null;
         if(Boolean(this.armsSaveG))
         {
            da0 = getDataByName(this.armsSaveG.mId) as ArmsTorData;
            if(Boolean(da0))
            {
               if(this.mainPan(da0))
               {
                  return da0;
               }
            }
            return null;
         }
         return null;
      }
      
      public function panCodeObj(obj0:Object) : String
      {
         var baseDef0:ArmsRangeDefine = null;
         var name0:String = obj0["name"];
         if(name0 == null || name0 == "")
         {
            return "代码中没有参数name。";
         }
         var da0:ArmsTorData = getDataByName(name0) as ArmsTorData;
         if(Boolean(da0))
         {
            return "你已经有了当前武器。" + (Gaming.testCtrl.canCheatingB() ? name0 : "");
         }
         var range0:ArmsRangeDefine = Gaming.defineGroup.bullet.getOutDef(name0);
         if(Boolean(range0))
         {
            return "你的系统中已经有了当前武器。\n如果有异常，请刷新游戏。" + (Gaming.testCtrl.canCheatingB() ? name0 : "");
         }
         var baseLabel0:String = obj0["baseLabel"];
         if(baseLabel0 == null || baseLabel0 == "")
         {
            return "代码中没有参数baseLabel。";
         }
         baseDef0 = Gaming.defineGroup.bullet.getRangeOnlyRange(baseLabel0);
         if(baseDef0 == null)
         {
            return "不存在武器：" + baseLabel0;
         }
         return "";
      }
      
      public function addDataByCode(code0:String, obj0:Object = null) : ArmsTorData
      {
         if(obj0 == null)
         {
            obj0 = TorEditMethod.codeToObj(code0);
         }
         var s0:TorSave = new TorSave();
         s0.obj = obj0;
         var da0:ArmsTorData = addSave(s0) as ArmsTorData;
         da0.setTempCode(code0);
         return da0;
      }
   }
}

