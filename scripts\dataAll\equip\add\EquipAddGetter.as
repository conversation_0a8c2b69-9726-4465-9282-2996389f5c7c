package dataAll.equip.add
{
   import dataAll._player.define.PlayerDataName;
   import dataAll._player.io.IO_HavePlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.define.EquipPro;
   
   public class EquipAddGetter implements IO_HavePlayerData, IO_EquipAddGetter
   {
      
      public var name:String = "";
      
      private var pd:NormalPlayerData;
      
      public function EquipAddGetter(name0:String)
      {
         super();
         this.name = name0;
      }
      
      public function setAllPlayerData(pd0:NormalPlayerData) : void
      {
         this.pd = pd0;
      }
      
      public function setPlayerData(pd0:NormalPlayerData) : void
      {
         this.pd = pd0;
      }
      
      public function getProAddObj() : Object
      {
         var mul0:Number = NaN;
         var pro0:String = null;
         var obj0:Object = {};
         if(this.name == PlayerDataName.lotteryGetter)
         {
            mul0 = this.pd.getDropMerge().getLotteryMul();
            if(mul0 > 0)
            {
               for each(pro0 in EquipPro.lotteryDropArr)
               {
                  obj0[pro0] = mul0;
               }
            }
         }
         return obj0;
      }
      
      public function getProAddMax(pro0:String) : Number
      {
         return 0;
      }
   }
}

