package dataAll._app.edit.list
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._base.IO_Define;
   import dataAll._base.NormalDefine;
   
   public class EditListAgent
   {
      
      public static const MAX_LINE:int = 17;
      
      public static const MAX_TXT:int = 5;
      
      public var info:String = "";
      
      private var titleArr:Array = null;
      
      private var titleCnArr:Array = null;
      
      private var firstTitle:String = "";
      
      private var dataArrArrObj:Object = {};
      
      private var allObj:Object = {};
      
      private var textArrObj:Object = {};
      
      private var noLinkArr:Array = null;
      
      private var noLinkSuffix:String = "";
      
      public var tipFun:Function;
      
      public var linkFun:Function;
      
      public var linkAfterHideB:Boolean = true;
      
      public function EditListAgent()
      {
         super();
      }
      
      public function clearFun() : void
      {
         this.tipFun = null;
         this.linkFun = null;
         this.linkAfterHideB = true;
         this.noLinkArr = null;
         this.noLinkSuffix = "";
      }
      
      public function inTitle(nameArr0:Array, cnArr0:Array) : void
      {
         this.titleArr = nameArr0;
         this.titleCnArr = cnArr0;
      }
      
      public function setFirstTitle(f0:String) : void
      {
         this.firstTitle = f0;
      }
      
      public function getTitleHtml(choose0:String) : String
      {
         var name0:String = null;
         var cn0:String = null;
         var s0:String = "";
         var len0:int = int(this.titleArr.length);
         for(var i:int = 0; i < len0; i++)
         {
            name0 = this.titleArr[i];
            cn0 = this.titleCnArr[i];
            if(s0 != "")
            {
               s0 += "\n";
            }
            if(name0 == choose0)
            {
               s0 += cn0;
            }
            else
            {
               s0 += TextMethod.link(cn0,name0);
            }
         }
         return s0;
      }
      
      public function getFirstTitle(nowTitle0:String) : String
      {
         if(Boolean(this.titleArr) && this.titleArr.length >= 1)
         {
            if(this.titleArr.indexOf(this.firstTitle) >= 0)
            {
               return this.firstTitle;
            }
            if(this.titleArr.indexOf(nowTitle0) >= 0)
            {
               return nowTitle0;
            }
            return this.titleArr[0];
         }
         return "";
      }
      
      public function getTitleIndex(title0:String) : int
      {
         if(Boolean(this.titleArr))
         {
            return this.titleArr.indexOf(title0);
         }
         return -1;
      }
      
      public function addNormalLast(name0:String, cn0:String, father0:String, color0:String = "") : Array
      {
         var d0:NormalDefine = new NormalDefine();
         d0.name = name0;
         if(color0 == "")
         {
            d0.cnName = cn0;
         }
         else
         {
            d0.cnName = TextMethod.color(cn0,color0);
         }
         d0.father = father0;
         return this.addDataLast(d0,father0);
      }
      
      public function addDataLast(d0:IO_Define, father0:String) : Array
      {
         var arrArr0:Array = this.getDataArrArr(father0,true);
         var lastArr0:Array = null;
         if(arrArr0.length == 0)
         {
            lastArr0 = this.newDataArr(father0);
         }
         else
         {
            lastArr0 = arrArr0[arrArr0.length - 1];
         }
         if(lastArr0.length >= MAX_LINE)
         {
            lastArr0 = this.newDataArr(father0);
         }
         lastArr0.push(d0);
         this.allObj[d0.getName()] = d0;
         return lastArr0;
      }
      
      public function addDataLastArr(darr0:Array, father0:String) : void
      {
         var d0:IO_Define = null;
         for each(d0 in darr0)
         {
            this.addDataLast(d0,father0);
         }
      }
      
      public function newDataArr(father0:String) : Array
      {
         var arrArr0:Array = this.getDataArrArr(father0,true);
         var arr0:Array = [];
         arrArr0.push(arr0);
         return arr0;
      }
      
      private function getDataArrArr(father0:String, newIfNullB0:Boolean = false) : Array
      {
         var arrArr0:Array = this.dataArrArrObj[father0];
         if(arrArr0 == null && newIfNullB0)
         {
            arrArr0 = [];
            this.dataArrArrObj[father0] = arrArr0;
         }
         return arrArr0;
      }
      
      public function getData(father0:String, txtIndex0:int, lineIndex0:int) : IO_Define
      {
         var arr0:Array = null;
         var d0:IO_Define = null;
         var arrArr0:Array = this.getDataArrArr(father0);
         if(Boolean(arrArr0))
         {
            if(txtIndex0 >= 0 && txtIndex0 <= arrArr0.length - 1)
            {
               arr0 = ArrayMethod.getElement(arrArr0,txtIndex0,null,null) as Array;
               if(Boolean(arr0))
               {
                  return ArrayMethod.getElement(arr0,lineIndex0,null,null) as IO_Define;
               }
            }
         }
         return null;
      }
      
      public function getDataByName(name0:String) : IO_Define
      {
         return this.allObj[name0];
      }
      
      public function setNoLinkArr(arr0:Array, suffix0:String = " √") : void
      {
         this.noLinkArr = arr0;
         this.noLinkSuffix = suffix0;
      }
      
      public function cutPan() : void
      {
         var father0:String = null;
         var fatherCn0:String = null;
         var arr0:Array = null;
         var arr_len0:int = 0;
         var newArr0:Array = null;
         var j:int = 0;
         var defArr0:Array = null;
         var newI0:int = 0;
         var newFather0:String = null;
         var newFatherCn0:String = null;
         var newObj0:Object = {};
         var newTitleArr0:Array = [];
         var newTitleCnArr0:Array = [];
         var fatherIndex0:int = 0;
         for each(father0 in this.titleArr)
         {
            fatherCn0 = this.titleCnArr[fatherIndex0];
            arr0 = this.dataArrArrObj[father0];
            if(Boolean(arr0))
            {
               arr_len0 = int(arr0.length);
               if(arr_len0 > MAX_TXT)
               {
                  newArr0 = null;
                  for(j = 0; j < arr_len0; j++)
                  {
                     defArr0 = arr0[j];
                     if(j % MAX_TXT == 0)
                     {
                        newArr0 = [];
                        newI0 = j / MAX_TXT + 1;
                        newFather0 = father0 + (newI0 == 1 ? "" : newI0);
                        newFatherCn0 = fatherCn0 + newI0;
                        newObj0[newFather0] = newArr0;
                        newTitleArr0.push(newFather0);
                        newTitleCnArr0.push(newFatherCn0);
                     }
                     newArr0.push(defArr0);
                  }
               }
               else
               {
                  newObj0[father0] = arr0;
                  newTitleArr0.push(father0);
                  newTitleCnArr0.push(fatherCn0);
               }
            }
            fatherIndex0++;
         }
         this.dataArrArrObj = newObj0;
         this.titleArr = newTitleArr0;
         this.titleCnArr = newTitleCnArr0;
      }
      
      public function createAllText() : void
      {
         var n:* = undefined;
         var arrArr0:Array = null;
         var textArr0:Array = null;
         var index0:int = 0;
         var arr0:Array = null;
         var text0:String = null;
         var firstB0:Boolean = false;
         var d0:IO_Define = null;
         var one0:String = null;
         this.textArrObj = {};
         for(n in this.dataArrArrObj)
         {
            arrArr0 = this.dataArrArrObj[n];
            textArr0 = [];
            index0 = 0;
            for each(arr0 in arrArr0)
            {
               text0 = "";
               firstB0 = true;
               for each(d0 in arr0)
               {
                  one0 = this.getOneText(d0);
                  if(!firstB0)
                  {
                     text0 += "\n";
                  }
                  text0 += one0;
                  firstB0 = false;
               }
               textArr0.push(text0);
               index0++;
            }
            this.textArrObj[n] = textArr0;
         }
      }
      
      private function getOneText(d0:IO_Define) : String
      {
         var s0:String = "";
         var linkB0:Boolean = true;
         var name0:String = d0.getName();
         if(name0 == "")
         {
            linkB0 = false;
         }
         else if(Boolean(this.noLinkArr))
         {
            if(this.noLinkArr.indexOf(name0) >= 0)
            {
               linkB0 = false;
            }
         }
         if(linkB0)
         {
            s0 = TextMethod.link(d0.getCnName(),d0.getName());
         }
         else
         {
            s0 = d0.getCnName();
            if(name0 != "")
            {
               s0 += this.noLinkSuffix;
            }
         }
         return s0;
      }
      
      public function getTextArr(father0:String) : Array
      {
         return this.textArrObj[father0];
      }
   }
}

