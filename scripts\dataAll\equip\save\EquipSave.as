package dataAll.equip.save
{
   import UI.test.SaveTestBox;
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.ObjectMethod;
   import dataAll._player.base.PlayerBaseData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipData;
   import dataAll.equip.creator.EquipEvoCtrl;
   import dataAll.equip.creator.EquipStrengthenCtrl;
   import dataAll.equip.creator.EquipUpgradeCtrl;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.device.DeviceSave;
   import dataAll.equip.jewelry.JewelrySave;
   import dataAll.equip.shield.ShieldSave;
   import dataAll.equip.vehicle.VehicleSave;
   import dataAll.equip.weapon.WeaponSave;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.creator.ItemsUpgradeCtrl;
   import dataAll.items.save.ComplexSave;
   import dataAll.items.save.ItemsSave;
   
   public class EquipSave extends ComplexSave
   {
      
      private static const ZERO:EquipSave = new EquipSave();
      
      public static var readNum:int = 0;
      
      public static var pro_arr:Array = [];
      
      public static var mePro_arr:Array = null;
      
      protected var CF:NiuBiCF = new NiuBiCF();
      
      private var _obj:Object = {};
      
      public var skillArr:Array = [];
      
      public var partType:String = "";
      
      public var imgName:String = "";
      
      public var proNum:int = 0;
      
      public var heroSkillAddObj:Object = {};
      
      public var strengthenLv:int = 0;
      
      public var strengthenNum:int = 0;
      
      public var sMaxLv:int = 0;
      
      public var evoLv:int = 1;
      
      public function EquipSave()
      {
         super();
         itemsType = ItemsDataGroup.TYPE_EQUIP;
         this.nowNum = 1;
      }
      
      public static function getSaveClass(type0:String) : Class
      {
         if(type0 == EquipType.VEHICLE)
         {
            return VehicleSave;
         }
         if(type0 == EquipType.DEVICE)
         {
            return DeviceSave;
         }
         if(type0 == EquipType.WEAPON)
         {
            return WeaponSave;
         }
         if(type0 == EquipType.JEWELRY)
         {
            return JewelrySave;
         }
         if(type0 == EquipType.FASHION)
         {
            return FashionSave;
         }
         if(type0 == EquipType.SHIELD)
         {
            return ShieldSave;
         }
         return EquipSave;
      }
      
      public function set nowNum(v0:Number) : void
      {
         this.CF.setAttribute("nowNum",v0);
      }
      
      public function get nowNum() : Number
      {
         return this.CF.getAttribute("nowNum");
      }
      
      public function set obj(v0:Object) : void
      {
         this._obj = ClassProperty.copyObj(v0);
      }
      
      public function get obj() : Object
      {
         return ClassProperty.copyObj(this._obj);
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
         this.obj = ClassProperty.copyObj(obj0["obj"]);
         if(name == "loveShell")
         {
            name = "loveShell_1";
            this.imgName = "loveShell_1";
         }
         if(obj0.hasOwnProperty("heroSkillAddObj"))
         {
            this.heroSkillAddObj = obj0["heroSkillAddObj"];
         }
         else
         {
            this.heroSkillAddObj = {};
         }
         this.fleshSMaxLv();
         itemsType = ItemsDataGroup.TYPE_EQUIP;
         var d0:EquipDefine = this.getDefine();
         if(d0.objInSaveB())
         {
            this.obj = d0.getAddObj();
         }
      }
      
      public function deal346() : void
      {
         var pro0:String = null;
         var oo:Object = null;
         var v2:* = undefined;
         var v0:Number = NaN;
         var newV0:Number = NaN;
         var dealB0:Boolean = false;
         var max0:Number = NaN;
         var d0:EquipDefine = this.getDefine();
         if(d0.isNormalB())
         {
            pro0 = EquipDefine.DPS_ALL_BLACK;
            oo = this.obj;
            if(oo.hasOwnProperty(pro0))
            {
               v2 = oo[pro0];
               v0 = Number(oo[pro0]);
               newV0 = 0;
               dealB0 = false;
               if(d0.isCanEvoB() && d0.getComposeProConstB() == false)
               {
                  max0 = d0.getFillDpsAllBlack();
                  if(max0 > 0)
                  {
                     if(v2 == null || isNaN(v0) || v0 < 0 || v0 > max0)
                     {
                        newV0 = max0;
                        dealB0 = true;
                     }
                  }
               }
               if(dealB0 == false)
               {
                  if(v2 == null || isNaN(v0) || v0 > 0.43)
                  {
                     newV0 = -1;
                     dealB0 = true;
                  }
               }
               if(dealB0)
               {
                  SaveTestBox.addText("修复装备：" + d0.cnName + " el:" + this.evoLv + " lv:" + this.getTrueLevel() + "  属性：" + oo[pro0] + "→" + newV0);
                  oo[pro0] = newV0;
                  this.obj = oo;
               }
            }
         }
      }
      
      public function getSaveObj(oldObj0:Object) : Object
      {
         var obj0:Object = ClassProperty.thinSaveObj(this,this.getZero(),this.getProArr());
         obj0.name = name;
         obj0.imgName = this.imgName;
         if(Gaming.isLocal())
         {
            this.checkSaveObj(oldObj0,obj0);
         }
         return obj0;
      }
      
      private function checkSaveObj(oldObj0:Object, obj0:Object) : Boolean
      {
         var class0:Class = getSaveClass(this.getPartType());
         var s0:EquipSave = new class0();
         s0.inData_byObj(obj0);
         var a:Object = ClassProperty.copyObj(s0);
         var b:Object = ClassProperty.copyObj(oldObj0);
         var bb0:Boolean = ObjectMethod.samePan(a,b);
         if(bb0 == false)
         {
            INIT.showError("装备 " + name + "：检测异常不同");
         }
         return bb0;
      }
      
      public function getProArr() : Array
      {
         return pro_arr;
      }
      
      public function getZero() : EquipSave
      {
         return ZERO;
      }
      
      public function inDataByDefine(d0:EquipDefine) : void
      {
         this.setImgName(d0.name);
         this.partType = d0.type;
         cnName = d0.cnName;
         if(d0.objInSaveB())
         {
            this.obj = d0.getAddObj();
         }
         if(d0.skillArr.length > 0)
         {
            this.skillArr = d0.skillArr.concat([]);
         }
      }
      
      public function fleshSMaxLv() : void
      {
         if(isNaN(this.sMaxLv) || this.sMaxLv < this.strengthenLv)
         {
            this.sMaxLv = this.strengthenLv;
         }
      }
      
      public function purgoldInDefine(d0:EquipDefine) : void
      {
         name = d0.name;
         this.setImgName(d0.name);
         cnName = d0.cnName;
      }
      
      public function setImgName(str0:String) : void
      {
         this.imgName = str0;
      }
      
      public function getDataClass() : EquipData
      {
         return new EquipData();
      }
      
      override public function isImportantB() : Boolean
      {
         if(super.isImportantB())
         {
            return true;
         }
         if(EquipColor.moreColorPan(color,EquipColor.DARKGOLD))
         {
            return true;
         }
         if(EquipType.NORMAL_ARR.indexOf(this.partType) == -1)
         {
            return true;
         }
         return false;
      }
      
      override public function getTrueLevel() : int
      {
         return itemsLevel + addLevel;
      }
      
      public function getMaxLevel() : int
      {
         var add0:int = EquipUpgradeCtrl.maxAddLevel;
         var v0:int = itemsLevel + add0;
         var pmax0:int = PlayerBaseData.MAX_LEVEL;
         if(v0 > pmax0)
         {
            v0 = pmax0;
         }
         return v0;
      }
      
      override public function getStrengthenLv() : int
      {
         return this.strengthenLv;
      }
      
      override public function getEvoLv() : int
      {
         return this.evoLv;
      }
      
      override public function setStrengthenLvAndMax(lv0:int) : void
      {
         this.strengthenLv = lv0;
         this.sMaxLv = lv0;
         lockB = true;
      }
      
      public function getLevelTipString() : String
      {
         if(this.partType == EquipType.FASHION)
         {
            return "";
         }
         return this.getTrueLevel() + "";
      }
      
      public function getPartType() : String
      {
         return this.partType;
      }
      
      public function getDefine() : EquipDefine
      {
         return Gaming.defineGroup.equip.getDefine(this.imgName);
      }
      
      public function getFatherDefine() : EquipFatherDefine
      {
         var d0:EquipDefine = this.getDefine();
         return Gaming.defineGroup.equip.getFatherDefine(d0.father);
      }
      
      override public function copyOne() : ItemsSave
      {
         return this.clone();
      }
      
      public function clone() : EquipSave
      {
         var obj0:Object = ClassProperty.copyObj(this);
         var class0:Class = getSaveClass(this.partType);
         var s0:EquipSave = new class0();
         s0.inData_byObj(obj0);
         return s0;
      }
      
      override public function getChildType() : String
      {
         return this.partType;
      }
      
      override public function getChildTypeCnName() : String
      {
         return EquipType.getCnName(this.getChildType());
      }
      
      override public function isMoreRedB() : Boolean
      {
         return this.getDefine().isMoreRedB();
      }
      
      override public function getTrueName() : String
      {
         return this.imgName;
      }
      
      override public function getCnName() : String
      {
         return EquipEvoCtrl.getCnName(this.getDefine(),this.evoLv);
      }
      
      override public function getBaseCn() : String
      {
         return this.getDefine().cnName;
      }
      
      public function getComposeMustNum() : int
      {
         return this.getDefine().composeMustNum;
      }
      
      public function isHaveSkillAddB() : Boolean
      {
         return ComMethod.getObjElementNum(this.heroSkillAddObj) >= 1;
      }
      
      public function getTrueObj() : Object
      {
         var obj0:Object = this.obj;
         if(this.strengthenLv > 0)
         {
            obj0 = EquipStrengthenCtrl.getNewObj(this);
         }
         if(this.getDefine().isCanEvoB())
         {
            obj0 = EquipEvoCtrl.getNewObj(this.getDefine(),obj0,this.evoLv);
         }
         return obj0;
      }
      
      public function getSkillArr() : Array
      {
         return this.skillArr;
      }
      
      public function getStarNum() : int
      {
         return int(this.strengthenLv / 5);
      }
      
      override public function getAllUpgradeConverStoneNum() : int
      {
         return ItemsUpgradeCtrl.getAllConverStone(itemsLevel,addLevel,color,true);
      }
      
      public function getSortIdByOtherSave(s0:EquipSave) : String
      {
         var n:* = undefined;
         var str0:String = null;
         var v0:Number = NaN;
         var v1:Number = NaN;
         var mul0:Number = NaN;
         var moreNum0:int = 50;
         var moreMul0:Number = 0;
         for(n in this.obj)
         {
            v0 = Number(this.obj[n]);
            if(s0.obj.hasOwnProperty(n))
            {
               v1 = Number(s0.obj[n]);
               if(v0 > v1)
               {
                  moreNum0++;
                  if(v0 > 0 && v1 > 0)
                  {
                     mul0 = v0 / v1;
                     moreMul0 += mul0;
                  }
               }
               else if(v0 > v1)
               {
                  moreNum0--;
               }
            }
         }
         return moreNum0 + "_" + TextWay.toNum(String(Math.round(moreMul0 * 1000)),8);
      }
      
      override public function getSimulateData(pd0:NormalPlayerData) : IO_ItemsData
      {
         var da0:EquipData = this.getDataClass();
         da0.inData_bySave(this,pd0,null);
         return da0;
      }
   }
}

