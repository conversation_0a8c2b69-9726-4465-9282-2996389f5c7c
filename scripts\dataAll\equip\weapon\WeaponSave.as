package dataAll.equip.weapon
{
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.save.EquipSave;
   
   public class WeaponSave extends EquipSave
   {
      
      private static const ZERO:WeaponSave = new WeaponSave();
      
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = null;
      
      public var s:String = "";
      
      public var cf:Boolean = false;
      
      public function WeaponSave()
      {
         super();
      }
      
      override public function getProArr() : Array
      {
         return pro_arr;
      }
      
      override public function getZero() : EquipSave
      {
         return ZERO;
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
      }
      
      override public function inDataByDefine(d0:EquipDefine) : void
      {
         var weaponDefine0:WeaponDefine = null;
         super.inDataByDefine(d0);
         weaponDefine0 = d0 as WeaponDefine;
         name = weaponDefine0.name;
         itemsLevel = weaponDefine0.lv;
         skillArr = weaponDefine0.skillArr;
      }
      
      override public function getDataClass() : EquipData
      {
         return new WeaponData();
      }
      
      override public function getDefine() : EquipDefine
      {
         return this.getWeaponDefine();
      }
      
      public function getWeaponDefine() : WeaponDefine
      {
         return Gaming.defineGroup.weapon.getDefine(name);
      }
      
      override public function getTrueObj() : Object
      {
         return obj;
      }
      
      override public function clone() : EquipSave
      {
         var obj0:Object = ClassProperty.copyObj(this);
         var s0:EquipSave = new WeaponSave();
         s0.inData_byObj(obj0);
         return s0;
      }
      
      override public function getSkillArr() : Array
      {
         return this.getWeaponDefine().skillArr;
      }
   }
}

