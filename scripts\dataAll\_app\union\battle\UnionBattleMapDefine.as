package dataAll._app.union.battle
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.worldMap.define.WorldMapDefine;
   
   public class UnionBattleMapDefine
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var levelName:String = "";
      
      public function UnionBattleMapDefine()
      {
         super();
         this.diff = 1;
      }
      
      public function get dpsMax() : Number
      {
         return this.CF.getAttribute("dpsMax");
      }
      
      public function set dpsMax(v0:Number) : void
      {
         this.CF.setAttribute("dpsMax",v0);
      }
      
      public function get diff() : Number
      {
         return this.CF.getAttribute("diff");
      }
      
      public function set diff(v0:Number) : void
      {
         this.CF.setAttribute("diff",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getMapDefine(name0:String) : WorldMapDefine
      {
         return Gaming.defineGroup.worldMap.getDefine(this.name);
      }
      
      public function getDpsMax(rank0:int) : Number
      {
         var max0:Number = 0.15;
         if(rank0 == 1)
         {
            max0 = 0.3;
         }
         else if(rank0 == 2)
         {
            max0 = 0.3;
         }
         else if(rank0 == 3)
         {
            max0 = 0.3;
         }
         else if(rank0 == 4)
         {
            max0 = 0.28;
         }
         else if(rank0 == 5)
         {
            max0 = 0.26;
         }
         return max0;
      }
      
      public function getDpsFator() : Number
      {
         return 7 / 3.5;
      }
      
      public function getLifeFator() : Number
      {
         var diff0:Number = this.diff;
         return diff0 * 2 * 28 / 3.5;
      }
      
      public function getEnemyLv() : int
      {
         return 95;
      }
   }
}

