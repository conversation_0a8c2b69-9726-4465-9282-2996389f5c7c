package dataAll._app.tower
{
   import dataAll.level.define.LevelDefine;
   
   public class TowerDefineCtrl
   {
      
      private var arr:Array = [];
      
      private var obj:Object = {};
      
      public function TowerDefineCtrl()
      {
         super();
      }
      
      public static function getDealFun(d0:TowerDefine) : Function
      {
         var name0:String = d0.getName();
         var fun0:Function = TowerDefineCtrl["f" + name0];
         if(fun0 is Function)
         {
            return fun0;
         }
         return null;
      }
      
      private static function f7(d0:TowerDefine, ld0:LevelDefine, diff0:int) : void
      {
         ld0.info.noAll();
      }
      
      private static function f8(d0:TowerDefine, ld0:LevelDefine, diff0:int) : void
      {
         ld0.info.sightCover = 1;
      }
      
      private static function f9(d0:TowerDefine, ld0:LevelDefine, diff0:int) : void
      {
         ld0.info.noAll();
         ld0.info.tm = 80 + diff0 * 10;
      }
      
      private static function f23(d0:TowerDefine, ld0:LevelDefine, diff0:int) : void
      {
         ld0.info.tm = 70 + diff0 * 5;
      }
      
      private static function f33(d0:TowerDefine, ld0:LevelDefine, diff0:int) : void
      {
         ld0.info.tm = 110 + diff0 * 10;
      }
      
      private static function f51(d0:TowerDefine, ld0:LevelDefine, diff0:int) : void
      {
         ld0.info.toOne();
      }
      
      private static function f52(d0:TowerDefine, ld0:LevelDefine, diff0:int) : void
      {
         ld0.info.toOne();
      }
      
      private static function f53(d0:TowerDefine, ld0:LevelDefine, diff0:int) : void
      {
         ld0.info.toOne();
      }
      
      private static function f54(d0:TowerDefine, ld0:LevelDefine, diff0:int) : void
      {
         ld0.info.toOne();
      }
      
      public function init() : void
      {
         TowerDefine.staticInit();
         var d0:TowerDefine = null;
         d0 = new TowerDefine(1);
         d0.add(new TowerBossDefine("年兽",0.05,0.5,["weaponNo"]));
         d0.giftStr = "things;demStone;15";
         d0.map = "XiaSha";
         this.addDefine(d0);
         d0 = new TowerDefine(2);
         d0.add(new TowerBossDefine("黑暗雷尔",0.1,0.5,[]));
         d0.giftStr = "things;madheart;10";
         d0.map = "BaoLun";
         this.addDefine(d0);
         d0 = new TowerDefine(3);
         d0.add(new TowerBossDefine("守望者",0.15,0.6,[]));
         d0.giftStr = "things;demStone;15";
         d0.map = "BaoLun";
         this.addDefine(d0);
         d0 = new TowerDefine(4);
         d0.add(new TowerBossDefine("运毒尸",0.2,0.6,["underToLand","weaponDefence"]));
         d0.giftStr = "things;madheart;10";
         d0.map = "BaWang";
         this.addDefine(d0);
         d0 = new TowerDefine(5);
         d0.add(new TowerBossDefine("多肉",0.25,0.7,["weaponDefence"]));
         d0.giftStr = "parts;hardeningParts_1;1";
         d0.map = "BaWang";
         this.addDefine(d0);
         d0 = new TowerDefine(6);
         d0.add(new TowerBossDefine("电棍僵尸",0.3,0.7,[]));
         d0.giftStr = "things;demStone;15";
         d0.map = "QingSha";
         this.addDefine(d0);
         d0 = new TowerDefine(7);
         d0.oneB = true;
         d0.add(new TowerBossDefine("掘地兽",0.35,0.8,["enemyToMe"]));
         d0.giftStr = "parts;shockParts_1;1";
         d0.map = "QingSha";
         this.addDefine(d0);
         d0 = new TowerDefine(8);
         d0.oneB = true;
         d0.add(new TowerBossDefine("编织者",0.5,0.5,["weaponDefence"]));
         d0.giftStr = "things;madheart;10";
         d0.map = "QingSha";
         this.addDefine(d0);
         d0 = new TowerDefine(9);
         d0.oneB = true;
         d0.add(new TowerBossDefine("决斗者",0.7,0.9,["enemyToMe","State_InvincibleThrough","killMeTimeOver","teleport_enemy"]));
         d0.map = "WoTu";
         d0.giftStr = "parts;speedParts_1;1";
         this.addDefine(d0);
         d0 = new TowerDefine(10);
         d0.add(new TowerBossDefine("极速守卫",1,1,["underToLand"]));
         d0.giftStr = "parts;twoShootParts_1;1";
         d0.map = "QingSha";
         d0.add(new TowerBossDefine("决斗者",1,1,["weaponNo"]));
         d0.add(new TowerBossDefine("编织者",1,1,["weaponNo"]));
         this.addDefine(d0);
         d0 = new TowerDefine(11);
         d0.add(new TowerBossDefine("虚晶蝎",1.5,1.2,["killAllSummon","bladeShield"]));
         d0.giftStr = "things;demStone;15";
         d0.map = "ShaJiang";
         this.addDefine(d0);
         d0 = new TowerDefine(12);
         d0.add(new TowerBossDefine("炸裂者",2,1.5,["invisibilityEver","underToLand","lockLife","godShield"]));
         d0.giftStr = "things;madheart;10";
         d0.map = "BaiSha";
         this.addDefine(d0);
         d0 = new TowerDefine(13);
         d0.add(new TowerBossDefine("狂野收割者",0.05,0.2,["petSensitive"]));
         d0.giftStr = "parts;shockParts_1;1";
         d0.map = "LangXue";
         this.addDefine(d0);
         d0 = new TowerDefine(14);
         d0.add(new TowerBossDefine("骷髅巫师",3,2,["everSilenceEnemy","weaponNo","godShield"]));
         d0.giftStr = "parts;speedParts_1;1";
         d0.map = "JungleOutside";
         this.addDefine(d0);
         d0 = new TowerDefine(15);
         d0.oneB = true;
         d0.add(new TowerBossDefine("王者兔",1,1,["State_SpellImmunity","deadlyArrow","killAllSummon","enemyEmp","noRocket"]));
         d0.giftStr = "parts;downSpeedParts_1;1";
         d0.map = "QingMing";
         this.addDefine(d0);
         d0 = new TowerDefine(16);
         d0.add(new TowerBossDefine("野帝",4,3,["bladeShield"]));
         d0.giftStr = "things;strengthenStone;100";
         d0.map = "JungleDeep";
         this.addDefine(d0);
         d0 = new TowerDefine(17);
         d0.add(new TowerBossDefine("狂人机器",5,3,["godShield"]));
         d0.giftStr = "parts;redArmsParts_1;1";
         d0.map = "JungleDeep";
         this.addDefine(d0);
         d0 = new TowerDefine(18);
         d0.add(new TowerBossDefine("狩猎者",6,3,["demCloned","bladeShield","ruleRange","noUnderLaser","State_SpellImmunity","noUnderFlyHit","through_hero_6","moreBullet","lightningFloor"]));
         d0.giftStr = "parts;twoShootParts_1;1";
         d0.map = "PrisonDoor";
         this.addDefine(d0);
         d0 = new TowerDefine(19);
         d0.add(new TowerBossDefine("巨毒尸",6,1,["spellImmunityMax","KingRabbitTreater"]));
         d0.giftStr = "parts;speedParts_1;1";
         d0.map = "JungleDeep";
         d0.add(new TowerBossDefine("霸王毒蛛",6,1,["weaponNo","spellImmunityMax"]));
         d0.add(new TowerBossDefine("暴君",6,1,["weaponNo","KingRabbitTreater"]));
         this.addDefine(d0);
         d0 = new TowerDefine(20);
         d0.add(new TowerBossDefine("阿巴达斯",6,3,["lockLife"]));
         d0.giftStr = "parts;downSpeedParts_1;1";
         d0.map = "JungleDeep";
         this.addDefine(d0);
         d0 = new TowerDefine(21);
         d0.add(new TowerBossDefine("毒魔",7,3,["weaponDefence","shortShootRangeHole","screwBall"]));
         d0.giftStr = "things;strengthenStone;100";
         d0.map = "DuKu";
         this.addDefine(d0);
         d0 = new TowerDefine(22);
         d0.add(new TowerBossDefine("斩之使者",0.6,3,["redArmsSensitive","everSilenceEnemy"]));
         d0.giftStr = "parts;uidpsParts_1;1";
         d0.map = "DuKu";
         this.addDefine(d0);
         d0 = new TowerDefine(23);
         d0.add(new TowerBossDefine("文杰表哥",10,3,["State_AddMove","killMeTimeOver","summonedSpider_spiderKing","findHide","FoggyDefence","moreBullet","meteoriteRain","lightningFloor","fightBackBullet","globalSpurting_enemy","offAllSkill"]));
         d0.giftStr = "things;demStone;15";
         d0.map = "DuKu";
         this.addDefine(d0);
         d0 = new TowerDefine(24);
         d0.add(new TowerBossDefine("无疆骑士",3,2,["invincibleHole","weaponNo","killAllSummon"]));
         d0.giftStr = "parts;speedParts_1;1";
         d0.map = "DuKu";
         d0.add(new TowerBossDefine("飓风巫尸",4,2));
         d0.add(new TowerBossDefine("钢铁僵尸王",4,2));
         this.addDefine(d0);
         d0 = new TowerDefine(25);
         d0.add(new TowerBossDefine("异猛象",10,3,["everSilenceEnemy"]));
         d0.giftStr = "parts;downSpeedParts_1;1";
         d0.map = "DuKu";
         this.addDefine(d0);
         d0 = new TowerDefine(26);
         d0.add(new TowerBossDefine("古飙",15,3.5,["demCloned","State_SpellImmunity","KingRabbitTreater","skillGift_enemy"]));
         d0.giftStr = "parts;uidpsParts_1;1";
         d0.map = "LuYu";
         this.addDefine(d0);
         d0 = new TowerDefine(27);
         d0.add(new TowerBossDefine("看门狗",15,3.5,["MeatySkillBack"]));
         d0.giftStr = "things;strengthenStone;100";
         d0.map = "Hospital5";
         this.addDefine(d0);
         d0 = new TowerDefine(28);
         d0.add(new TowerBossDefine("轰天雷",15,3.5,["through_enemy","screwBall","globalSpurting_enemy","moreBullet","demCloned","clearAttackIron","sacrifice_equip","godHand_equip","ruleRange","recoveryHalo_enemy","WarriorShield"]));
         d0.giftStr = "parts;shockParts_1;1";
         d0.map = "LuYu";
         this.addDefine(d0);
         d0 = new TowerDefine(29);
         d0.add(new TowerBossDefine("嗜血尸狼",2.5,1,["rifleSensitive"]));
         d0.giftStr = "parts;twoShootParts_1;1";
         d0.map = "LuYu";
         d0.add(new TowerBossDefine("火炮尸狼",2.5,1,["sniperSensitive"]));
         d0.add(new TowerBossDefine("狂战狼",2.5,1,["shotgunSensitive"]));
         this.addDefine(d0);
         d0 = new TowerDefine(30);
         d0.oneB = true;
         d0.add(new TowerBossDefine("幻影Z",10,3.5,["reflectiveShell"]));
         d0.giftStr = "parts;downSpeedParts_1;1";
         d0.map = "LuYu";
         this.addDefine(d0);
         d0 = new TowerDefine(31);
         d0.add(new TowerBossDefine("防毒僵尸",8,3.5,["weaponNo"]));
         d0.giftStr = "parts;redArmsParts_1;1";
         d0.map = "PrisonOutside";
         this.addDefine(d0);
         d0 = new TowerDefine(32);
         d0.add(new TowerBossDefine("末日坦克",12,3.5,["liveReplace_enemy","weaponNo"]));
         d0.giftStr = "things;strengthenStone;100";
         d0.map = "PrisonOutside";
         this.addDefine(d0);
         d0 = new TowerDefine(33);
         d0.add(new TowerBossDefine("屠刀僵尸",1000,3.5,["findHide","killMeTimeOver","OreWormAI_sum","offAllSkill","noPurgoldArms","State_SpellImmunity","toLand","anionSkin_equip"]));
         d0.giftStr = "parts;speedParts_1;1";
         d0.map = "PrisonOutside";
         this.addDefine(d0);
         d0 = new TowerDefine(34);
         d0.add(new TowerBossDefine("窃听者",20,2,["weaponNo","reflectiveShell"]));
         d0.giftStr = "things;demBall;50";
         d0.map = "PrisonOutside";
         d0.add(new TowerBossDefine("伏地尸",20,2,["weaponDefence"]));
         d0.add(new TowerBossDefine("狂野收割者",20,2,["bladeShield"]));
         this.addDefine(d0);
         d0 = new TowerDefine(35);
         d0.add(new TowerBossDefine("铁犬",7,3,["screwBall","killAllSummon"]));
         d0.giftStr = "parts;eleParts_1;1";
         d0.map = "PrisonOutside";
         this.addDefine(d0);
         d0 = new TowerDefine(36);
         d0.add(new TowerBossDefine("异火龙",20,4,["rebirth_enemy","godHand_equip","State_SpellImmunity","acidRain_SpiderKing","KingRabbitTreater","killAllSummon"]));
         d0.giftStr = "things;madheart;10";
         d0.map = "HanGuang2";
         this.addDefine(d0);
         d0 = new TowerDefine(37);
         d0.add(new TowerBossDefine("古惑僵尸",8,4,["everSilenceEnemy","reflectiveShell"]));
         d0.giftStr = "parts;twoShootParts_1;1";
         d0.map = "HanGuang2";
         this.addDefine(d0);
         d0 = new TowerDefine(38);
         d0.add(new TowerBossDefine("天鹰小美",20,4,["through_enemy","deadlyArrow","pioneerDemon","fastForward_enemy","findHide","extendCd","hammer_enemy"]));
         d0.giftStr = "parts;shockParts_1;1";
         d0.map = "HanGuang2";
         this.addDefine(d0);
         d0 = new TowerDefine(39);
         d0.add(new TowerBossDefine("僵尸王",8,2,["pistolSensitive","weaponNo","KingRabbitTreater"]));
         d0.giftStr = "things;demStone;15";
         d0.map = "HanGuang2";
         d0.add(new TowerBossDefine("游尸王",8,2,["rocketSensitive","weaponDefence","KingRabbitTreater","State_SpellImmunity"]));
         d0.add(new TowerBossDefine("狂战尸",8,2,["flamerSensitive","bladeShield","State_SpellImmunity"]));
         this.addDefine(d0);
         d0 = new TowerDefine(40);
         d0.add(new TowerBossDefine("虚洪螈",10,4,["hiding_enemy","teleport_enemy"]));
         d0.giftStr = "parts;eleParts_1;1";
         d0.map = "HanGuang2";
         this.addDefine(d0);
         d0 = new TowerDefine(41);
         d0.add(new TowerBossDefine("球形闪电",25,4,["weaponDefence","magneticField_enemy","screwBall","electricBoom_enemy","reverseHurt_enemy","feedback_enemy","KingRabbitTreater"]));
         d0.giftStr = "parts;shockParts_1;1";
         d0.map = "BingKu";
         this.addDefine(d0);
         d0 = new TowerDefine(42);
         d0.oneB = true;
         d0.add(new TowerBossDefine("肥胖僵尸",5,0.5,["vehicleSensitive","everSilenceEnemy","State_SpellImmunity","meltFlamerPurgold"]));
         d0.giftStr = "things;madheart;10";
         d0.map = "BingKu";
         this.addDefine(d0);
         d0 = new TowerDefine(43);
         d0.add(new TowerBossDefine("冥王",25,1,["electricBoom_enemy","extendCd","offAllSkill","cmldef3_enemy","State_AddMove","hammer_enemy","screaming_enemy","slowMove_enemy"]));
         d0.giftStr = "parts;twoShootParts_1;1";
         d0.map = "BingKu";
         this.addDefine(d0);
         d0 = new TowerDefine(44);
         d0.add(new TowerBossDefine("战神",15,3,["demCloned","reflectiveShell","godShield","meltFlamerPurgold"]));
         d0.giftStr = "things;demBall;50";
         d0.map = "BingKu";
         this.addDefine(d0);
         d0 = new TowerDefine(45);
         d0.add(new TowerBossDefine("爆骷S",25,4,["weaponDefence","everSilenceEnemy","moreBullet","deadlyArrow","enemyEmp","noRocket"]));
         d0.giftStr = "parts;penbodyParts_1;1";
         d0.map = "BingKu";
         this.addDefine(d0);
         d0 = new TowerDefine(46);
         d0.add(new TowerBossDefine("异角龙",15,2,["meltFlamerPurgold","weaponDefence","invincibleHole"]));
         d0.add(new TowerBossDefine("冥刃游尸",25,4,["meltFlamerPurgold","weaponDefence"]));
         d0.giftStr = "";
         d0.map = "QingMing";
         this.addDefine(d0);
         d0 = new TowerDefine(47);
         d0.add(new TowerBossDefine("异祖龙",10,2,["spellImmunityMax","invincibleHole"]));
         d0.add(new TowerBossDefine("独眼僵尸",25,4,["spellImmunityMax"]));
         d0.ldiy = "TFlyDragon";
         d0.giftStr = "";
         d0.map = "QingMing";
         this.addDefine(d0);
         d0 = new TowerDefine(48);
         d0.add(new TowerBossDefine("异齿虎",15,2,["meltFlamerPurgold","weaponDefence"]));
         d0.add(new TowerBossDefine("星锤干尸",25,4,["meltFlamerPurgold","weaponDefence","verShield"]));
         d0.giftStr = "";
         d0.map = "QingMing";
         this.addDefine(d0);
         d0 = new TowerDefine(49);
         d0.add(new TowerBossDefine("虚空客",15,2,["spellImmunityMax","invincibleHole","verShield"]));
         d0.add(new TowerBossDefine("银锤",25,4,["spellImmunityMax"]));
         d0.giftStr = "";
         d0.map = "QingMing";
         this.addDefine(d0);
         d0 = new TowerDefine(50);
         d0.add(new TowerBossDefine("虚炎狼",15,2,["meltFlamerPurgold","weaponDefence","verShield"]));
         d0.add(new TowerBossDefine("关东尸",25,4,["meltFlamerPurgold","weaponDefence","verShield"]));
         d0.ldiy = "TFireWolf";
         d0.giftStr = "";
         d0.map = "QingMing";
         this.addDefine(d0);
         d0 = new TowerDefine(51);
         d0.add(new TowerBossDefine("财宝僵尸",20,4,["State_AddMove","State_AddMove50","strollCard","meltFlamerPurgold","weaponNo","State_SpellImmunity"]));
         d0.map = "ShuangTa";
         this.addDefine(d0);
         d0 = new TowerDefine(52);
         d0.add(new TowerBossDefine("雪人",100,4,["screwBall"]));
         d0.map = "YouLing";
         this.addDefine(d0);
         d0 = new TowerDefine(53);
         d0.add(new TowerBossDefine("赤鼬导弹发射器",30,0.5,["rebirth_enemy","strollCard","meltFlamerPurgold","moreBullet","invincibleEmp","demCloned","godShield"]));
         d0.map = "YouLing";
         this.addDefine(d0);
         d0 = new TowerDefine(54);
         d0.add(new TowerBossDefine("异龙蛋",30,4,["blackHoleDemon","noBulletHurt","lockLife","rebirth_enemy","midLightning","madCloseLightning","fightBackBullet"]));
         d0.map = "YouLing";
         this.addDefine(d0);
         d0 = new TowerDefine(55);
         d0.add(new TowerBossDefine("巨毒尸",10,2,["gmMask1","deadlyArrow","deadlyGhost","meltFlamerPurgold"]));
         d0.add(new TowerBossDefine("狂战尸",10,2,["gmMask2","deadlyArrow","deadlyGhost","meltFlamerPurgold"]));
         d0.add(new TowerBossDefine("掘金尸",10,2,["gmMask3","deadlyArrow","deadlyGhost","meltFlamerPurgold"]));
         d0.map = "XiFeng";
         this.addDefine(d0);
         d0 = new TowerDefine(56);
         d0.add(new TowerBossDefine("末日坦克",50,4,["bigScale"]));
         d0.map = "PrisonOutside";
         this.addDefine(d0);
         d0 = new TowerDefine(57);
         d0.add(new TowerBossDefine("吸血蝙蝠",20,4,["smallScale","strollCard","speedDrug","State_SpellImmunity","meltFlamerPurgold"]));
         d0.map = "XiFeng";
         this.addDefine(d0);
         d0 = new TowerDefine(58);
         d0.add(new TowerBossDefine("防毒僵尸",50,4,["world180"]));
         d0.map = "XiFeng";
         this.addDefine(d0);
         d0 = new TowerDefine(59);
         d0.add(new TowerBossDefine("肥胖僵尸",20,4,["phantomDemon","summonedSpider_spiderKing_extra","demCloned2","demCloned","summSentry","changeToSaberTiger"]));
         d0.map = "XiFeng";
         this.addDefine(d0);
         d0 = new TowerDefine(60);
         d0.add(new TowerBossDefine("矿工僵尸",50,4,["vacuumJie_10","zeroGravityCard","fastCd"]));
         d0.map = "XiFeng";
         this.addDefine(d0);
         d0 = new TowerDefine(61);
         d0.oneB = true;
         d0.add(new TowerBossDefine("战斗僵尸",0.2,0.3,["handSensitive","everSilenceEnemy","worldBlack"]));
         d0.map = "HanGuang3";
         this.addDefine(d0);
         d0 = new TowerDefine(62);
         d0.oneB = true;
         d0.add(new TowerBossDefine("僵尸炮兵总管",0.2,0.3,["handSensitive","everSilenceEnemy","worldBlack"]));
         d0.map = "HanGuang3";
         this.addDefine(d0);
         d0 = new TowerDefine(63);
         d0.add(new TowerBossDefine("奇皇博士",80,3,[]));
         d0.add(new TowerBossDefine("亚瑟",80,3,[]));
         d0.add(new TowerBossDefine("鬼爵",80,3,[]));
         d0.map = "NanTang";
         this.addDefine(d0);
         d0 = new TowerDefine(64);
         d0.add(new TowerBossDefine("潜影者",50,2,["spellImmunityMax"]));
         d0.add(new TowerBossDefine("大鹏",50,2,["spellImmunityMax"]));
         d0.add(new TowerBossDefine("惩戒者",50,2,["spellImmunityMax"]));
         d0.map = "BeiDou";
         this.addDefine(d0);
         d0 = new TowerDefine(65);
         d0.add(new TowerBossDefine("战神",10,1.5,[]));
         d0.add(new TowerBossDefine("黄金隼",10,1.5,[]));
         d0.add(new TowerBossDefine("铁犬",10,1.5,[]));
         d0.map = "GanymedeCave";
         this.addDefine(d0);
         d0 = new TowerDefine(66);
         d0.add(new TowerBossDefine("矿工僵尸",30,2,["invincibleHole","rebirth_enemy","meltFlamerPurgold"]));
         d0.add(new TowerBossDefine("掘金尸",20,2,["demCloned2"]));
         d0.add(new TowerBossDefine("水管僵尸",20,2,["demCloned2","demCloned","spellImmunityMax","teleport_skeleton"]));
         d0.map = "XiShan2";
         this.addDefine(d0);
         d0 = new TowerDefine(67);
         d0.add(new TowerBossDefine("僵尸治疗兵",20,2,["invincibleHole","rebirth_enemy","meltFlamerPurgold"]));
         d0.add(new TowerBossDefine("科研僵尸",15,2,["demCloned2","demCloned","spellImmunityMax","teleport_skeleton"]));
         d0.add(new TowerBossDefine("哨兵",15,2,["demCloned2","demCloned","spellImmunityMax"]));
         d0.map = "XiShan2";
         this.addDefine(d0);
         d0 = new TowerDefine(68);
         d0.add(new TowerBossDefine("利爪僵尸",30,2,["invincibleHole","rebirth_enemy","meltFlamerPurgold"]));
         d0.add(new TowerBossDefine("斩之使者",20,2,["demCloned"]));
         d0.add(new TowerBossDefine("女爵尸",20,2,["demCloned2","spellImmunityMax"]));
         d0.map = "XiShan2";
         this.addDefine(d0);
         d0 = new TowerDefine(69);
         d0.add(new TowerBossDefine("双头僵尸",20,2,["invincibleHole"]));
         d0.add(new TowerBossDefine("关东尸",20,2,["demCloned2","demCloned","spellImmunityMax","teleport_skeleton"]));
         d0.add(new TowerBossDefine("伏地尸",20,2,["demCloned2","demCloned","spellImmunityMax","teleport_skeleton"]));
         d0.map = "XiShan2";
         this.addDefine(d0);
         d0 = new TowerDefine(70);
         d0.add(new TowerBossDefine("氩星吞噬者",15,2,[]));
         d0.add(new TowerBossDefine("蛇型采矿机",15,2,["demCloned2"]));
         d0.add(new TowerBossDefine("采矿虫",15,2,["demCloned2"]));
         d0.map = "XiShan2";
         this.addDefine(d0);
      }
      
      private function addDefine(d0:TowerDefine) : void
      {
         d0.init();
         this.arr.push(d0);
         d0.sort = this.arr.length;
         this.obj[d0.getName()] = d0;
      }
      
      public function getDefine(name0:String) : TowerDefine
      {
         return this.obj[name0];
      }
      
      public function getUIArr() : Array
      {
         return this.arr;
      }
   }
}

