package dataAll._app.task.define
{
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.task.TaskDataGroup;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   
   public class TaskOpenDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var mustB:Boolean = false;
      
      public var closeAffterCompleteB:Boolean = false;
      
      public var must:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public var start:String = "";
      
      public var end:String = "";
      
      public var tk:Array = [];
      
      public function TaskOpenDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.must.inData_byXML(xml0.must);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function getMustDefine() : MustDefine
      {
         return this.must.getMustDefine();
      }
      
      public function timeLimitB() : Boolean
      {
         return this.start != "" || this.end != "";
      }
      
      public function timePanB(nowTimeDa0:StringDate) : Boolean
      {
         if(this.timeLimitB())
         {
            return nowTimeDa0.betweenIn(this.start,this.end) == 0;
         }
         return true;
      }
      
      public function taskPanB(tg0:TaskDataGroup) : Boolean
      {
         var name0:String = null;
         if(this.tk.length == 0)
         {
            return true;
         }
         for each(name0 in this.tk)
         {
            if(tg0.isRoleCompleteB(name0))
            {
               return true;
            }
         }
         return false;
      }
   }
}

