package dataAll.pro.dataList
{
   import com.sounto.cf.FixedEncryp;
   import com.sounto.utils.NumberMethod;
   import dataAll._base.NormalDefine;
   
   public class DataListDefine extends NormalDefine
   {
      
      public static var pro_arr:Array = null;
      
      private var fixedEncryp:FixedEncryp = new FixedEncryp();
      
      public var len:int = 0;
      
      public var fixed:int = 0;
      
      public var noEncodeB:Boolean = false;
      
      private var valueArr:Array = [];
      
      private var trueValueArr:Array = [];
      
      public function DataListDefine()
      {
         super();
      }
      
      override public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         var s0:String = null;
         var len0:int = 0;
         var i:int = 0;
         var v0:Number = NaN;
         var ev0:Number = NaN;
         super.inData_byXML(xml0,father0);
         var setFixedB0:Boolean = Boolean(xml0.hasOwnProperty("@fixed"));
         var str0:String = xml0;
         var strArr0:Array = str0.split("\r\n");
         if(!this.noEncodeB)
         {
            for each(s0 in strArr0)
            {
               v0 = NumberMethod.perStringToNumber(s0);
               if(!setFixedB0)
               {
                  if(v0 < 1 && v0 > 0)
                  {
                     this.fixed = 2;
                  }
               }
               this.trueValueArr.push(v0);
            }
            this.valueArr.length = 0;
            this.fixedEncryp.setFixed(this.fixed);
            len0 = int(this.trueValueArr.length);
            for(i = 0; i < len0; i++)
            {
               v0 = Number(this.trueValueArr[i]);
               ev0 = this.fixedEncryp.encode(v0);
               this.valueArr.push(ev0);
            }
            if(len0 != this.len)
            {
               INIT.showError("DataListDefine：" + name + "，valueArr.length:" + this.valueArr.length + " != len:" + this.len);
            }
         }
         else
         {
            this.valueArr = strArr0;
            this.trueValueArr = strArr0;
         }
      }
      
      public function getSumCode() : Number
      {
         var v0:Number = NaN;
         var sum0:Number = 0;
         for each(v0 in this.valueArr)
         {
            sum0 += v0;
         }
         return sum0;
      }
      
      private function checkData() : void
      {
         var n:* = undefined;
         var arr0:Array = this.getValueArr();
         for(n in arr0)
         {
            if(arr0[n] != this.trueValueArr[n])
            {
               INIT.showError("DataListDefine错误：" + arr0[n] + "!=" + this.trueValueArr[n]);
            }
         }
      }
      
      override protected function getBaseClassProArr() : Array
      {
         return pro_arr;
      }
      
      public function getValue(lv0:int) : Number
      {
         var arr0:Array = this.valueArr;
         var index0:int = lv0 - 1;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         var v0:Number = Number(arr0[index0]);
         return this.fixedEncryp.decode(v0);
      }
      
      public function getSum(lv0:int) : Number
      {
         var v0:Number = 0;
         for(var i:int = 1; i <= lv0; i++)
         {
            v0 += this.getValue(i);
         }
         return v0;
      }
      
      public function getValueArr() : Array
      {
         var v0:Number = NaN;
         var arr0:Array = [];
         for each(v0 in this.valueArr)
         {
            arr0.push(this.fixedEncryp.decode(v0));
         }
         return arr0;
      }
      
      public function getTrueValueArr() : Array
      {
         return this.trueValueArr;
      }
      
      public function getTrueValueObj() : Object
      {
         var str0:String = null;
         var f0:int = 0;
         var id0:String = null;
         var name0:String = null;
         var obj0:Object = {};
         var strArr0:Array = this.getTrueValueArr();
         for each(str0 in strArr0)
         {
            if(str0 != "")
            {
               f0 = int(str0.indexOf(":"));
               id0 = str0.substring(0,f0);
               name0 = str0.substring(f0 + 1);
               if(id0 != "" && name0 != "")
               {
                  obj0[id0] = name0;
               }
            }
         }
         return obj0;
      }
      
      public function findLv(value0:Number) : int
      {
         var v0:Number = NaN;
         var len0:int = int(this.valueArr.length);
         for(var lv0:int = 1; lv0 <= len0; lv0++)
         {
            v0 = this.getValue(lv0);
            if(value0 <= v0)
            {
               return lv0;
            }
         }
         return len0 + 1;
      }
      
      public function testAllData() : String
      {
         var v0:Number = NaN;
         var str0:String = cnName + " ------------------------ ";
         var lv0:int = 0;
         for each(v0 in this.trueValueArr)
         {
            lv0++;
            str0 += "\n" + lv0 + "：" + v0;
         }
         return str0;
      }
   }
}

