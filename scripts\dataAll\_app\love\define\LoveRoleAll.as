package dataAll._app.love.define
{
   import com.sounto.oldUtils.ComMethod;
   
   public class LoveRoleAll
   {
      
      public var role:String = "";
      
      private var obj:Object = {};
      
      private var nameObj:Object = {};
      
      private var typeNumObj:Object = {};
      
      private var likeObj:Object = {};
      
      private var hateObj:Object = {};
      
      public var levelNameArr:Array = [];
      
      private var levelArr:Array = [];
      
      private var levelObj:Object = {};
      
      public var loveTalkLvRangeArr:Array = [];
      
      public function LoveRoleAll()
      {
         super();
      }
      
      public function inLevelData_byXML(xml0:XML) : void
      {
         var j:* = undefined;
         var x0:XML = null;
         var d0:LoveLevelDefine = null;
         var xl0:XMLList = xml0.level;
         var beforeD0:LoveLevelDefine = null;
         for(j in xl0)
         {
            x0 = xl0[j];
            d0 = new LoveLevelDefine();
            d0.inData_byXML(x0);
            this.levelArr.push(d0);
            this.levelObj[d0.name] = d0;
            this.levelNameArr.push(d0.name);
            if(Boolean(beforeD0))
            {
               beforeD0.max = d0.must - 1;
            }
            beforeD0 = d0;
         }
      }
      
      public function getLevelDefineByValue(v0:Number) : LoveLevelDefine
      {
         var d0:LoveLevelDefine = null;
         for(var i:int = this.levelArr.length - 1; i >= 0; i--)
         {
            d0 = this.levelArr[i];
            if(v0 >= d0.must)
            {
               return d0;
            }
         }
         return this.levelArr[0];
      }
      
      public function getLevelDefineArrByValue(v0:Number) : Array
      {
         var d0:LoveLevelDefine = null;
         var arr0:Array = [];
         for each(d0 in this.levelArr)
         {
            if(v0 >= d0.must)
            {
               arr0.push(d0);
            }
         }
         return arr0;
      }
      
      public function getLevelDefineByName(name0:String) : LoveLevelDefine
      {
         return this.levelObj[name0];
      }
      
      public function inTalkData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var x0:XML = null;
         var xl2:XMLList = null;
         var type0:String = null;
         var j:* = undefined;
         var x2:XML = null;
         var d0:LoveTalkDefine = null;
         var xl0:XMLList = xml0.father;
         for(n in xl0)
         {
            x0 = xl0[n];
            xl2 = x0.talk;
            type0 = String(x0.@type);
            for(j in xl2)
            {
               x2 = xl2[j];
               d0 = new LoveTalkDefine();
               d0.inData_byXML(x2,type0);
               this.addTalkDefine(d0);
            }
         }
      }
      
      private function addTalkDefine(d0:LoveTalkDefine) : void
      {
         var lv0:int = 0;
         if(!this.typeNumObj.hasOwnProperty(d0.type))
         {
            this.typeNumObj[d0.type] = 0;
         }
         ++this.typeNumObj[d0.type];
         d0.name = d0.type + "_" + this.typeNumObj[d0.type];
         if(!this.nameObj.hasOwnProperty(d0.type))
         {
            this.nameObj[d0.type] = [];
         }
         this.nameObj[d0.type].push(d0.name);
         this.obj[d0.name] = d0;
         if(d0.type == "like")
         {
            if(!this.likeObj.hasOwnProperty(d0.things))
            {
               this.likeObj[d0.things] = [];
            }
            this.likeObj[d0.things].push(d0.name);
         }
         if(d0.type == "hate")
         {
            if(!this.hateObj.hasOwnProperty(d0.things))
            {
               this.hateObj[d0.things] = [];
            }
            this.hateObj[d0.things].push(d0.name);
         }
         if(d0.type.indexOf("lv") >= 0)
         {
            lv0 = int(d0.type.substr(3));
            if(this.loveTalkLvRangeArr.indexOf(lv0) == -1)
            {
               this.loveTalkLvRangeArr.push(lv0);
            }
         }
      }
      
      public function getTalkDefineByName(name0:String) : LoveTalkDefine
      {
         return this.obj[name0];
      }
      
      public function getRandomNameArrByType(type0:String, num0:int) : Array
      {
         if(!this.nameObj.hasOwnProperty(type0))
         {
            return [];
         }
         var arr0:Array = this.nameObj[type0];
         return ComMethod.getRandomArray(arr0,num0);
      }
      
      public function getTalkDefineArrByNameArr(nameArr0:Array) : Array
      {
         var name0:String = null;
         var d0:LoveTalkDefine = null;
         var arr0:Array = [];
         for each(name0 in nameArr0)
         {
            d0 = this.getTalkDefineByName(name0);
            arr0.push(d0);
         }
         return arr0;
      }
      
      public function getLikeHateRandom(num0:int) : Array
      {
         var likeNameArr0:Array = this.getRandomInLikeHateObj(this.likeObj,num0,[]);
         var likeThingsArr0:Array = this.getThingsArrByNameArr(likeNameArr0);
         var hateNameArr0:Array = this.getRandomInLikeHateObj(this.hateObj,num0,likeThingsArr0);
         var hateThingsArr0:Array = this.getThingsArrByNameArr(hateNameArr0);
         return [likeNameArr0,hateNameArr0];
      }
      
      private function getThingsArrByNameArr(arr0:Array) : Array
      {
         var name0:String = null;
         var d0:LoveTalkDefine = null;
         var tArr0:Array = [];
         for each(name0 in arr0)
         {
            d0 = this.getTalkDefineByName(name0);
            tArr0.push(d0.things);
         }
         return tArr0;
      }
      
      private function getRandomInLikeHateObj(obj0:Object, num0:int, noThingsArr0:Array) : Array
      {
         var n:* = undefined;
         var likeRanArr0:Array = null;
         var nameArr0:Array = null;
         var arr2:Array = null;
         var arr0:Array = null;
         var likeArr0:Array = [];
         for(n in obj0)
         {
            arr0 = obj0[n];
            if(noThingsArr0.indexOf(n) == -1)
            {
               likeArr0.push(arr0);
            }
         }
         likeRanArr0 = ComMethod.getRandomArray(likeArr0,num0);
         nameArr0 = [];
         for each(arr2 in likeRanArr0)
         {
            nameArr0.push(arr2[int(Math.random() * arr2.length)]);
         }
         return nameArr0;
      }
   }
}

