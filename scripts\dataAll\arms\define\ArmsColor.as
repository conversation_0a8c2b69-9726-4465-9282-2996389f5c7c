package dataAll.arms.define
{
   import dataAll.arms.ArmsData;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.define.EquipColor;
   
   public class ArmsColor extends EquipColor
   {
      
      public static const canRefiningArr:Array = [RED,BLACK];
      
      public static const filterArr:Array = [WHIT<PERSON>,GRE<PERSON>,BLUE,PURPLE,ORANGE,RED,BLACK];
      
      private static const skin2Arr:Array = ["sniperCicada","rifleHornet","shotgunSkunk"];
      
      private static const skin1:Array = ["Gold"];
      
      private static const skin2:Array = ["Gold","Gold2"];
      
      public static const madbossArmsArr:Array = ["extremeLaser","extremeLightning","extremeRocket"];
      
      public function ArmsColor()
      {
         super();
      }
      
      public static function getUIDpsMul(color0:String) : Number
      {
         if(moreBlackB(color0))
         {
            return 1.3;
         }
         return 1;
      }
      
      public static function getRanGodSkillMax(color0:String) : int
      {
         if(moreBlackB(color0))
         {
            return 3;
         }
         if(color0 == RED)
         {
            return 2;
         }
         return 0;
      }
      
      private static function getSkinArr(d0:ArmsDefine) : Array
      {
         if(skin2Arr.indexOf(d0.name) >= 0)
         {
            return skin2;
         }
         return skin1;
      }
      
      public static function swapSkin(d0:ArmsDefine, now0:String) : String
      {
         var last0:String = null;
         var f0:int = 0;
         var skinArr0:Array = getSkinArr(d0);
         var first0:String = d0.bodyImgRange[0];
         if(now0 != "")
         {
            last0 = now0.replace(first0,"");
            f0 = skinArr0.indexOf(last0) + 1;
            if(f0 > skinArr0.length - 1)
            {
               now0 = "";
            }
            else
            {
               now0 = first0 + skinArr0[f0];
            }
         }
         else
         {
            now0 = first0 + skinArr0[0];
         }
         return now0;
      }
      
      public static function dealMadbossArms(da0:ArmsData) : ArmsRangeDefine
      {
         var s0:ArmsSave = null;
         s0 = da0.save;
         s0.color = "white";
         s0.name = "ak47";
         s0.cnName = "光辉烈焰";
         s0.armsImgLabel = "texture$t13_sniper1$body_ak$barrel5_ak$grip_ak$bullet_xm8$stock_0";
         s0.hurtRatio = 1000;
         s0.penetrationGap = 0;
         s0.penetrationNum = 0;
         s0.skillArr = [];
         s0.godSkillArr = [];
         return Gaming.defineGroup.bullet.getArmsRangeDefine(s0.name);
      }
   }
}

