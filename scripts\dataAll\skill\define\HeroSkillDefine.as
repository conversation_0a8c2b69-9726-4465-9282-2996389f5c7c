package dataAll.skill.define
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldCodeCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.define.IO_ResolveItemsDefine;
   import dataAll.must.define.MustDefine;
   import dataAll.pro.NormalPropertyArrayDefineGroup;
   import dataAll.skill.define.add.SkillAddDefine;
   
   public class HeroSkillDefine extends SkillDefine implements IO_ResolveItemsDefine
   {
      
      public static var pro_arr:Array = [];
      
      private var _maxLevel:int = 0;
      
      private var codeCF:OldCodeCF = new OldCodeCF();
      
      public var growth:XMLList = null;
      
      public var lv:int = 1;
      
      public var changeText:String = "";
      
      public var iconUrl:String = "";
      
      public var studyMustMul:Number = 1;
      
      public var upgradeMustMul:Number = 1;
      
      public var coinMustMul:Number = 1;
      
      public var moneyB:Boolean = false;
      
      public var addD:SkillAddDefine = new SkillAddDefine();
      
      public var noRebuildB:Boolean = false;
      
      public var noNeedEquipB:Boolean = false;
      
      public function HeroSkillDefine()
      {
         super();
         this.studyMustGiftStr = "";
      }
      
      public static function getStudyMust(mustLv0:int, studyMul0:Number = 1, coinMul0:Number = 1) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         d0.coin = Math.ceil(getStudyCoinNum(mustLv0) * coinMul0);
         d0.lv = mustLv0;
         var skillStoneNum0:int = Math.ceil(getStudyStoneNum(mustLv0) * studyMul0);
         d0.inThingsDataByArr(["skillStone;" + skillStoneNum0]);
         return d0;
      }
      
      public static function getStudyMustLv(skillNum0:int) : int
      {
         var lv0:int = 0;
         if(skillNum0 == 0)
         {
            return 5;
         }
         lv0 = skillNum0 * 3 + 5;
         if(lv0 > 99)
         {
            lv0 = 99;
         }
         return lv0;
      }
      
      private static function getStudyStoneNum(mustLv0:int) : int
      {
         var lv0:int = mustLv0 - 5;
         if(lv0 < 1)
         {
            return 1;
         }
         return Math.ceil(Math.pow(lv0,1.8) * 0.1 * 6);
      }
      
      private static function getStudyCoinNum(mustLv0:int) : Number
      {
         var nd0:NormalPropertyArrayDefineGroup = Gaming.defineGroup.normal;
         if(mustLv0 <= 6)
         {
            return 1;
         }
         return Math.ceil(nd0.getPriceMul(mustLv0) * nd0.getPlayerCoinIncome(mustLv0) * 0.2);
      }
      
      public static function getUpgradeMustLv(studyBodyLv0:int, skillNowLv0:int) : int
      {
         return studyBodyLv0 + skillNowLv0 * 2;
      }
      
      public static function getUpgradeStoneNum(mustLv0:int) : int
      {
         var lv0:int = mustLv0 - 5;
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         return Math.ceil(Math.pow(lv0,1.8) * 0.1 * 0.5 * 6);
      }
      
      public static function getUpgradeCoinNum(mustLv0:int) : Number
      {
         return Math.ceil(getStudyCoinNum(mustLv0) / 2);
      }
      
      public function get studyMustGiftStr() : String
      {
         return this.codeCF.getAttribute("studyMustGiftStr");
      }
      
      public function set studyMustGiftStr(str0:String) : void
      {
         this.codeCF.setAttribute("studyMustGiftStr",str0);
      }
      
      override public function inData_byXML(xml0:XML, father0:String, inXmlB:Boolean = true) : void
      {
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         super.inData_byXML(xml0,father0,false);
         this.growth = xml0.growth;
         this._maxLevel = this.growth.skill.length();
         this.addD.inData_byXML(xml0.addD[0]);
         if(this.iconUrl == "")
         {
            this.iconUrl = "SkillIcon/" + name;
         }
         if(iconUrl36 == "")
         {
            iconUrl36 = this.iconUrl + "_36";
         }
      }
      
      override protected function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function copy() : HeroSkillDefine
      {
         var d0:HeroSkillDefine = new HeroSkillDefine();
         d0.inData_byObj(this);
         d0.obj = ClassProperty.copyObj(obj);
         return d0;
      }
      
      public function getGrowthArr() : Array
      {
         var n:* = undefined;
         var skill_xml0:XML = null;
         var d0:HeroSkillDefine = null;
         this._maxLevel = 0;
         var arr2:Array = [];
         var xml_list0:XMLList = this.growth.skill;
         for(n in xml_list0)
         {
            skill_xml0 = xml_list0[n];
            d0 = this.copy();
            d0.inData_byXML(skill_xml0,father);
            d0.lv = int(int(n) + 1);
            d0.name = name + "_" + d0.lv;
            d0.baseLabel = name;
            arr2.push(d0);
            ++this._maxLevel;
         }
         return arr2;
      }
      
      public function getMaxLevel() : int
      {
         var d0:HeroSkillDefine = null;
         if(baseLabel == name)
         {
            return this._maxLevel;
         }
         d0 = Gaming.defineGroup.skill.getDefine(baseLabel) as HeroSkillDefine;
         if(Boolean(d0))
         {
            return d0._maxLevel;
         }
         return 0;
      }
      
      public function getMaxLevelDefine() : HeroSkillDefine
      {
         if(this._maxLevel > 1)
         {
            return Gaming.defineGroup.skill.getDefine(baseLabel + "_" + this._maxLevel) as HeroSkillDefine;
         }
         return this;
      }
      
      public function getNextDefine() : HeroSkillDefine
      {
         return Gaming.defineGroup.skill.getDefine(baseLabel + "_" + (this.lv + 1)) as HeroSkillDefine;
      }
      
      override public function getIconImgUrl(w0:int, h0:int) : String
      {
         if(w0 < 50 && w0 >= 36)
         {
            return iconUrl36;
         }
         return this.iconUrl;
      }
      
      public function getResolveGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         if(this.canResolveB())
         {
            g0 = new GiftAddDefineGroup();
            g0.addGiftByStr(this.studyMustGiftStr);
            return g0;
         }
         return null;
      }
      
      public function canResolveB() : Boolean
      {
         return this.studyMustGiftStr != "";
      }
      
      override protected function fleshDescription() : Array
      {
         var arr0:Array = super.fleshDescription();
         if(Boolean(arr0))
         {
            if(noCdMulB)
            {
               _des += "\n" + ComMethod.color("套装的技能恢复速度加成对该技能无效。","#FF66FF");
            }
            this.changeText = swapProText(arr0,this.changeText);
         }
         return arr0;
      }
      
      public function getNowChangeText() : String
      {
         if(!desFleshB)
         {
            this.fleshDescription();
         }
         return this.changeText;
      }
      
      public function getUIInfoText(firstStr0:String) : String
      {
         return ComMethod.color(firstStr0 + "第" + this.lv + "级","#FF9900") + "\n" + this.getNowChangeText();
      }
      
      public function getGatherTip() : String
      {
         var str0:String = "";
         if(isActiveB())
         {
            str0 += "<green 主动技能/>";
            str0 += "\n<blue 冷却时间：" + cd + "秒/>";
         }
         else
         {
            str0 += "<yellow 被动技能/>";
         }
         return str0 + ("\n<white " + getDescription() + "/>");
      }
      
      public function getProjectText() : String
      {
         var n:* = undefined;
         var d0:HeroSkillDefine = null;
         var str0:String = cnName + "（" + (isActiveB() ? "主动技能" : "被动技能") + "）";
         str0 += "\n" + getDescription(true,false);
         var d_arr0:Array = Gaming.defineGroup.skill.getHeroDefineArr(baseLabel);
         for(n in d_arr0)
         {
            d0 = d_arr0[n];
            str0 += "\n第" + (n + 1) + "级——" + d0.getNowChangeText().replace("\n","，");
         }
         if(isActiveB())
         {
            str0 += "\n技能冷却时间：" + cd + "秒";
         }
         return str0;
      }
      
      public function getLevelGatherTip(nowLv0:int = 0) : String
      {
         var d0:HeroSkillDefine = null;
         var lv0:int = 0;
         var s0:String = null;
         var v0:String = null;
         var color0:String = null;
         var str0:String = this.getGatherTip();
         var d_arr0:Array = Gaming.defineGroup.skill.getHeroDefineArr(baseLabel);
         for each(d0 in d_arr0)
         {
            lv0 = d0.lv;
            s0 = ComMethod.color("lv." + lv0 + "  ","#FFFF00");
            v0 = d0.getNowChangeText().replace("\n","、");
            color0 = nowLv0 >= lv0 ? "#00CC99" : "#666666";
            str0 += "\n" + s0 + ComMethod.color(v0,color0);
         }
         return str0;
      }
      
      public function getAllLv() : int
      {
         var studyStr0:String = this.studyMustGiftStr;
         if(studyStr0 == "")
         {
            return 0;
         }
         return 5;
      }
      
      public function getThisStudyMust(mustLv0:int) : MustDefine
      {
         var g0:GiftAddDefineGroup = null;
         var d2:MustDefine = null;
         var d0:MustDefine = null;
         var studyStr0:String = this.studyMustGiftStr;
         if(studyStr0 != "")
         {
            g0 = new GiftAddDefineGroup();
            g0.addGiftByStr(studyStr0);
            d2 = g0.getMustDefine();
            d2.lv = this.getAllLv();
            return d2;
         }
         d0 = getStudyMust(mustLv0,this.studyMustMul,this.coinMustMul);
         if(this.moneyB)
         {
            d0.money = 30;
            d0.coin = 0;
         }
         return d0;
      }
      
      public function haveProfiB() : Boolean
      {
         return this.getMaxLevel() >= 11;
      }
   }
}

