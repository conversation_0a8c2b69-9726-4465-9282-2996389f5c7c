package dataAll._app.task.save
{
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.task.TaskState;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import gameAll.level._diy.spread.LevelDiy_MadFly;
   
   public class TaskSave
   {
      
      public static var pro_arr:Array = [];
      
      private static const ZERO:TaskSave = new TaskSave();
      
      public var name:String = "";
      
      public var state:String = "no";
      
      private var _c:String = "";
      
      private var _diff:String = "";
      
      public var map:String = "";
      
      public var lev:String = "";
      
      private var _lv:String = "";
      
      private var _cN:String = "";
      
      private var _aN:String = "";
      
      private var _sN:String = "";
      
      public var openB:Boolean = false;
      
      public var di:Boolean = false;
      
      public function TaskSave()
      {
         super();
         this.c = 0;
         this.diff = 0;
         this.lv = 0;
         this.cN = 0;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         var d0:TaskDefine = null;
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.oldToNow(obj0,"collectNum","c");
         this.oldToNow(obj0,"nowDiff","diff");
         this.oldToNow(obj0,"randomWorldMapId","map");
         this.oldToNow(obj0,"fixedNormalLevelId","lev");
         this.oldToNow(obj0,"completeNum","cN");
         this.map = WorldMapDefine.dealMapName(this.map);
         if(LevelDiy_MadFly.bugTaskArr.indexOf(this.name) >= 0)
         {
            d0 = Gaming.defineGroup.task.getOneDefine(this.name);
            if(this.diff > d0.getGrowthNum() - 1)
            {
               this.diff = d0.getGrowthNum() - 1;
            }
         }
      }
      
      private function oldToNow(obj0:Object, old0:String, now0:String) : void
      {
         if(obj0.hasOwnProperty(old0))
         {
            this[now0] = obj0[old0];
         }
      }
      
      public function dayInit() : void
      {
         this.state = TaskState.no;
         this.c = 0;
         this.lv = 0;
         this.map = "";
         this.lev = "";
         this.cN = 0;
         this.openB = false;
         this.di = true;
      }
      
      public function toOverIfComplete() : void
      {
         if(this.state == TaskState.complete)
         {
            this.state = TaskState.over;
         }
      }
      
      public function haveDayInitB() : Boolean
      {
         return this.di;
      }
      
      public function noDayInit() : void
      {
         this.di = false;
      }
      
      public function getSaveObj() : Object
      {
         var n0:String = null;
         var now0:* = undefined;
         var zero0:* = undefined;
         var newO:Object = {};
         for each(n0 in pro_arr)
         {
            now0 = this[n0];
            zero0 = ZERO[n0];
            if(now0 != zero0)
            {
               newO[n0] = now0;
            }
         }
         return newO;
      }
      
      public function clearNum() : void
      {
         this.c = 0;
      }
      
      public function set c(v0:Number) : void
      {
         this._c = Sounto64.encode(String(v0));
      }
      
      public function get c() : Number
      {
         return Number(Sounto64.decode(this._c));
      }
      
      public function set diff(v0:Number) : void
      {
         this._diff = Sounto64.encode(String(v0));
      }
      
      public function get diff() : Number
      {
         return Number(Sounto64.decode(this._diff));
      }
      
      public function set lv(v0:Number) : void
      {
         this._lv = Sounto64.encode(String(v0));
      }
      
      public function get lv() : Number
      {
         return Number(Sounto64.decode(this._lv));
      }
      
      public function set cN(v0:Number) : void
      {
         this._cN = Sounto64.encode(String(v0));
      }
      
      public function get cN() : Number
      {
         return Number(Sounto64.decode(this._cN));
      }
      
      public function set aN(v0:Number) : void
      {
         this._aN = Sounto64.encode(String(v0));
      }
      
      public function get aN() : Number
      {
         return Number(Sounto64.decode(this._aN));
      }
      
      public function set sN(v0:Number) : void
      {
         this._sN = Sounto64.encode(String(v0));
      }
      
      public function get sN() : Number
      {
         return Number(Sounto64.decode(this._sN));
      }
   }
}

