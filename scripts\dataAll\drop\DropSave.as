package dataAll.drop
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll.things.define.ThingsDefine;
   
   public class DropSave
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var weekObj:NumberEncodeObj = new NumberEncodeObj();
      
      public var dayObj:NumberEncodeObj = new NumberEncodeObj();
      
      public var dayAll:NumberEncodeObj = new NumberEncodeObj();
      
      public function DropSave()
      {
         super();
         this.ironChiefBook = 0;
         this.refiningBlackNum = 0;
         this.deviceNum = 0;
         this.normalChestNum = 0;
         this.weaponNum = 0;
         this.keyNum = 0;
         this.bloodStoneNum = 0;
         this.magicChestNum = 0;
         this.blackChipNum = 0;
         this.allBlackChipNum = 0;
         this.armsBlackChipNum = 0;
         this.allArmsBlackChipNum = 0;
         this.echelon1 = 0;
         this.partsNum = 0;
         this.equipGem = 0;
         this.chrisGun = 0;
         this.nintyArmsBlackNum = 0;
         this.nintyEquipBlackNum = 0;
         this.allNintyArmsBlackNum = 0;
         this.allNintyEquipBlackNum = 0;
         this.dragonChestNum = 0;
      }
      
      public function get deviceNum() : Number
      {
         return this.CF.getAttribute("deviceNum");
      }
      
      public function set deviceNum(v0:Number) : void
      {
         this.CF.setAttribute("deviceNum",v0);
      }
      
      public function get weaponNum() : Number
      {
         return this.CF.getAttribute("weaponNum");
      }
      
      public function set weaponNum(v0:Number) : void
      {
         this.CF.setAttribute("weaponNum",v0);
      }
      
      public function get normalChestNum() : Number
      {
         return this.CF.getAttribute("normalChestNum");
      }
      
      public function set normalChestNum(v0:Number) : void
      {
         this.CF.setAttribute("normalChestNum",v0);
      }
      
      public function get magicChestNum() : Number
      {
         return this.CF.getAttribute("magicChestNum");
      }
      
      public function set magicChestNum(v0:Number) : void
      {
         this.CF.setAttribute("magicChestNum",v0);
      }
      
      public function get tigerChest() : Number
      {
         return this.CF.getAttribute("tigerChest");
      }
      
      public function set tigerChest(v0:Number) : void
      {
         this.CF.setAttribute("tigerChest",v0);
      }
      
      public function get keyNum() : Number
      {
         return this.CF.getAttribute("keyNum");
      }
      
      public function set keyNum(v0:Number) : void
      {
         this.CF.setAttribute("keyNum",v0);
      }
      
      public function get bloodStoneNum() : Number
      {
         return this.CF.getAttribute("bloodStoneNum");
      }
      
      public function set bloodStoneNum(v0:Number) : void
      {
         this.CF.setAttribute("bloodStoneNum",v0);
      }
      
      public function get blackChipNum() : Number
      {
         return this.CF.getAttribute("blackChipNum");
      }
      
      public function set blackChipNum(v0:Number) : void
      {
         this.CF.setAttribute("blackChipNum",v0);
      }
      
      public function get allBlackChipNum() : Number
      {
         return this.CF.getAttribute("allBlackChipNum");
      }
      
      public function set allBlackChipNum(v0:Number) : void
      {
         this.CF.setAttribute("allBlackChipNum",v0);
      }
      
      public function get armsBlackChipNum() : Number
      {
         return this.CF.getAttribute("armsBlackChipNum");
      }
      
      public function set armsBlackChipNum(v0:Number) : void
      {
         this.CF.setAttribute("armsBlackChipNum",v0);
      }
      
      public function get allArmsBlackChipNum() : Number
      {
         return this.CF.getAttribute("allArmsBlackChipNum");
      }
      
      public function set allArmsBlackChipNum(v0:Number) : void
      {
         this.CF.setAttribute("allArmsBlackChipNum",v0);
      }
      
      public function get partsNum() : Number
      {
         return this.CF.getAttribute("partsNum");
      }
      
      public function set partsNum(v0:Number) : void
      {
         this.CF.setAttribute("partsNum",v0);
      }
      
      public function get equipGem() : Number
      {
         return this.CF.getAttribute("equipGem");
      }
      
      public function set equipGem(v0:Number) : void
      {
         this.CF.setAttribute("equipGem",v0);
      }
      
      public function get ironChiefBook() : Number
      {
         return this.CF.getAttribute("ironChiefBook");
      }
      
      public function set ironChiefBook(v0:Number) : void
      {
         this.CF.setAttribute("ironChiefBook",v0);
      }
      
      public function get chrisGun() : Number
      {
         return this.CF.getAttribute("chrisGun");
      }
      
      public function set chrisGun(v0:Number) : void
      {
         this.CF.setAttribute("chrisGun",v0);
      }
      
      public function get madheart() : Number
      {
         return this.CF.getAttribute("madheart");
      }
      
      public function set madheart(v0:Number) : void
      {
         this.CF.setAttribute("madheart",v0);
      }
      
      public function get mhA() : Number
      {
         return this.CF.getAttribute("mhA");
      }
      
      public function set mhA(v0:Number) : void
      {
         this.CF.setAttribute("mhA",v0);
      }
      
      public function get nintyEquipBlackNum() : Number
      {
         return this.CF.getAttribute("nintyEquipBlackNum");
      }
      
      public function set nintyEquipBlackNum(v0:Number) : void
      {
         this.CF.setAttribute("nintyEquipBlackNum",v0);
      }
      
      public function get allNintyEquipBlackNum() : Number
      {
         return this.CF.getAttribute("allNintyEquipBlackNum");
      }
      
      public function set allNintyEquipBlackNum(v0:Number) : void
      {
         this.CF.setAttribute("allNintyEquipBlackNum",v0);
      }
      
      public function get nintyArmsBlackNum() : Number
      {
         return this.CF.getAttribute("nintyArmsBlackNum");
      }
      
      public function set nintyArmsBlackNum(v0:Number) : void
      {
         this.CF.setAttribute("nintyArmsBlackNum",v0);
      }
      
      public function get allNintyArmsBlackNum() : Number
      {
         return this.CF.getAttribute("allNintyArmsBlackNum");
      }
      
      public function set allNintyArmsBlackNum(v0:Number) : void
      {
         this.CF.setAttribute("allNintyArmsBlackNum",v0);
      }
      
      public function get refiningBlackNum() : Number
      {
         return this.CF.getAttribute("refiningBlackNum");
      }
      
      public function set refiningBlackNum(v0:Number) : void
      {
         this.CF.setAttribute("refiningBlackNum",v0);
      }
      
      public function get dragonChestNum() : Number
      {
         return this.CF.getAttribute("dragonChestNum");
      }
      
      public function set dragonChestNum(v0:Number) : void
      {
         this.CF.setAttribute("dragonChestNum",v0);
      }
      
      public function get echelon1() : Number
      {
         return this.CF.getAttribute("echelon1");
      }
      
      public function set echelon1(v0:Number) : void
      {
         this.CF.setAttribute("echelon1",v0);
      }
      
      public function get pumpkin20() : Number
      {
         return this.CF.getAttribute("pumpkin20");
      }
      
      public function set pumpkin20(v0:Number) : void
      {
         this.CF.setAttribute("pumpkin20",v0);
      }
      
      public function get pumpkin20All() : Number
      {
         return this.CF.getAttribute("pumpkin20All");
      }
      
      public function set pumpkin20All(v0:Number) : void
      {
         this.CF.setAttribute("pumpkin20All",v0);
      }
      
      public function get dongzhi23() : Number
      {
         return this.CF.getAttribute("dongzhi23");
      }
      
      public function set dongzhi23(v0:Number) : void
      {
         this.CF.setAttribute("dongzhi23",v0);
      }
      
      public function get wuyiChip2021() : Number
      {
         return this.CF.getAttribute("wuyiChip2021");
      }
      
      public function set wuyiChip2021(v0:Number) : void
      {
         this.CF.setAttribute("wuyiChip2021",v0);
      }
      
      public function get redBag() : Number
      {
         return this.CF.getAttribute("redBag");
      }
      
      public function set redBag(v0:Number) : void
      {
         this.CF.setAttribute("redBag",v0);
      }
      
      public function get redBagAll() : Number
      {
         return this.CF.getAttribute("redBagAll");
      }
      
      public function set redBagAll(v0:Number) : void
      {
         this.CF.setAttribute("redBagAll",v0);
      }
      
      public function get arbor21() : Number
      {
         return this.CF.getAttribute("arbor21");
      }
      
      public function set arbor21(v0:Number) : void
      {
         this.CF.setAttribute("arbor21",v0);
      }
      
      public function get arbor21All() : Number
      {
         return this.CF.getAttribute("arbor21All");
      }
      
      public function set arbor21All(v0:Number) : void
      {
         this.CF.setAttribute("arbor21All",v0);
      }
      
      public function get timeCMore() : Number
      {
         return this.CF.getAttribute("timeCMore");
      }
      
      public function set timeCMore(v0:Number) : void
      {
         this.CF.setAttribute("timeCMore",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newWeek() : void
      {
         this.weekObj.clearData();
      }
      
      public function newDayCtrl() : void
      {
         this.dayObj.clearData();
         this.pumpkin20 = 0;
         this.ironChiefBook = 0;
         this.refiningBlackNum = 0;
         this.nintyArmsBlackNum = 0;
         this.nintyEquipBlackNum = 0;
         this.dragonChestNum = 0;
         this.echelon1 = 0;
         this.partsNum = 0;
         this.equipGem = 0;
         this.chrisGun = 0;
         this.deviceNum = 0;
         this.weaponNum = 0;
         this.keyNum = 0;
         this.bloodStoneNum = 0;
         this.blackChipNum = 0;
         this.armsBlackChipNum = 0;
         this.wuyiChip2021 = 0;
         this.redBag = 0;
         this.arbor21 = 0;
         this.dongzhi23 = 0;
         this.madheart = 0;
      }
      
      public function getKeyDropPro(diff0:int, mapMode0:String) : Number
      {
         var pro0:Number = (2 + diff0) * 0.1;
         if(mapMode0 == MapMode.DEMON)
         {
            pro0 = 0.7;
         }
         if(this.keyNum > 10)
         {
            pro0 *= 0.5;
         }
         return pro0;
      }
      
      public function getBlackDropPro(diff0:int, add0:Number) : Number
      {
         var diffArr0:Array = [0.6,0.7,0.85,1,1.2,1.5,1.8,2.2];
         var diffMul0:Number = ArrayMethod.getElementLimit(diffArr0,diff0) as Number;
         var num0:int = this.blackChipNum;
         var v0:Number = 0.01;
         if(num0 < this.getEquipBlackDropMax(add0))
         {
            v0 = 0.7 * 5 * 3;
         }
         return v0 * diffMul0;
      }
      
      public function getEquipBlackDropMax(add0:Number) : int
      {
         return 350 * (1 + add0);
      }
      
      public function getArmsBlackDropPro(diff0:int, add0:Number) : Number
      {
         var diffArr0:Array = [0.6,0.7,0.85,1,1.2,1.5,1.8,2.2];
         var diffMul0:Number = ArrayMethod.getElementLimit(diffArr0,diff0) as Number;
         var num0:int = this.armsBlackChipNum;
         var v0:Number = 0.01;
         if(num0 < this.getArmsBlackDropMax(add0))
         {
            v0 = 0.7 * 5 * 3;
         }
         return v0 * diffMul0;
      }
      
      public function getArmsBlackDropMax(add0:Number) : int
      {
         return 350 * (1 + add0);
      }
      
      public function getBloodStoneDropPro(diff0:int, mapMode0:String) : Number
      {
         var pro0:Number = (3.5 + diff0) * 0.1;
         if(mapMode0 == MapMode.DEMON)
         {
            pro0 = 0.85;
         }
         if(this.bloodStoneNum > 40)
         {
            pro0 *= 0.01;
         }
         return pro0;
      }
      
      public function getEquipBlackDropPro86(diff0:int, add0:Number, lv0:int) : Number
      {
         var diffArr0:Array = [0.8,0.9,1.1,1.4,1.7,2,2.4,2.8,3.2,3.6];
         var diffMul0:Number = ArrayMethod.getElementLimit(diffArr0,diff0) as Number;
         var num0:int = this.nintyEquipBlackNum;
         var v0:Number = 0.01;
         if(num0 < this.getEquipBlackDrop86Max_0(add0))
         {
            v0 = 0.6;
         }
         else if(num0 < this.getEquipBlackDrop86Max(add0))
         {
            v0 = 0.6 * 1;
         }
         else
         {
            v0 = 0;
         }
         if(lv0 >= 96)
         {
            v0 *= 0.7;
         }
         return v0 * diffMul0 * 3;
      }
      
      public function getEquipBlackDrop86Max_0(add0:Number) : int
      {
         return 50 * (1 + add0);
      }
      
      public function getEquipBlackDrop86Max(add0:Number) : int
      {
         return 75 * (1 + add0);
      }
      
      public function getArmsBlackDropPro86(diff0:int, add0:Number, lv0:int) : Number
      {
         var diffArr0:Array = [0.8,0.9,1.1,1.4,1.7,2,2.4,2.8,3.2,3.6];
         var diffMul0:Number = ArrayMethod.getElementLimit(diffArr0,diff0) as Number;
         var num0:int = this.nintyArmsBlackNum;
         var v0:Number = 0.01;
         if(num0 < this.getArmsBlackDropPro86Max_0(add0))
         {
            v0 = 0.85;
         }
         else if(num0 < this.getArmsBlackDropPro86Max(add0))
         {
            v0 = 0.85 * 1;
         }
         else
         {
            v0 = 0;
         }
         if(lv0 == 97 || lv0 == 98)
         {
            v0 *= 0.7;
         }
         return v0 * diffMul0 * 3;
      }
      
      public function getArmsBlackDropPro86Max_0(add0:Number) : int
      {
         return 50 * (1 + add0);
      }
      
      public function getArmsBlackDropPro86Max(add0:Number) : int
      {
         return 75 * (1 + add0);
      }
      
      public function giftThingsAdd(d0:ThingsDefine, num0:int) : void
      {
         if(d0.isBlackChip())
         {
            this.allBlackChipNum += num0;
         }
      }
      
      public function getPartsDropPro(diff0:int, add0:Number) : Number
      {
         var diffArr0:Array = [0.6,0.8,0.9,1,1.1,1.3,1.5,1.7,1.9,2.1];
         var diffMul0:Number = ArrayMethod.getElementLimit(diffArr0,diff0) as Number;
         var num0:int = this.partsNum;
         var v0:Number = 0.01;
         if(num0 <= 50 * (1 + add0))
         {
            v0 = 1;
         }
         else if(num0 < this.getPartsDropMax(add0))
         {
            v0 = 1;
         }
         else
         {
            v0 = 0;
         }
         return v0 * diffMul0 * 3;
      }
      
      public function getPartsDropMax(add0:Number) : int
      {
         return 70 * (1 + add0);
      }
      
      public function getGemDropPro(is96B:Boolean, diff0:int, add0:Number) : Number
      {
         var diffArr0:Array = [1.2,1.6,1.9,2.2,2.6,3,3.5,3.8,4.1,4.3];
         var diffMul0:Number = ArrayMethod.getElementLimit(diffArr0,diff0) as Number;
         var num0:int = this.equipGem;
         var v0:Number = 0.01;
         if(num0 < this.getGemDropMax(add0))
         {
            v0 = 1;
         }
         else
         {
            v0 = 0;
         }
         return v0 * diffMul0 * 2;
      }
      
      public function getEquipGemDropPro(diff0:int, add0:Number) : Number
      {
         var diffArr0:Array = [0.8,1,1.2,1.4,1.7,2,2.5,3,3.4,3.8];
         var diffMul0:Number = ArrayMethod.getElementLimit(diffArr0,diff0) as Number;
         var num0:int = this.equipGem;
         var v0:Number = 0.01;
         if(num0 < this.getGemDropMax(add0))
         {
            v0 = 1;
         }
         else
         {
            v0 = 0;
         }
         return v0 * diffMul0 * 2;
      }
      
      public function getGemDropMax(add0:Number) : int
      {
         return 100 * (1 + add0);
      }
      
      public function getPetIronChiefBookDrop(diff0:int, add0:Number) : Number
      {
         var diffArr0:Array = [0.7,1,1.2,1.4,1.6,1.9,2.2,2.4,2.6,2.8];
         var diffMul0:Number = ArrayMethod.getElementLimit(diffArr0,diff0) as Number;
         var num0:int = this.ironChiefBook;
         var v0:Number = 0.01;
         if(num0 <= this.getPetIronChiefBookMax(add0))
         {
            v0 = 1;
         }
         else
         {
            v0 = 0;
         }
         return v0 * diffMul0;
      }
      
      public function getPetIronChiefBookMax(add0:Number) : int
      {
         return 24 * (1 + add0);
      }
      
      public function getDragonChestPro(diff0:int) : Number
      {
         var diffArr0:Array = [0.1,0.2,0.3,0.5,1,1,1,1];
         var diffMul0:Number = ArrayMethod.getElementLimit(diffArr0,diff0) as Number;
         if(this.dragonChestNum >= 2)
         {
            return 0;
         }
         return 0.3 * diffMul0;
      }
      
      public function addWeekDropNum(name0:String, v0:int, inAllB0:Boolean = false) : void
      {
         this.weekObj.addNum(name0,v0);
         if(inAllB0)
         {
            this.dayAll.addNum(name0,v0);
         }
      }
      
      public function getWeekDropNum(name0:String) : int
      {
         return this.weekObj.getAttribute(name0);
      }
      
      public function getWeekDropAll(name0:String) : int
      {
         return this.getDayDropAll(name0);
      }
      
      public function addDayDropNum(name0:String, v0:int) : void
      {
         this.dayObj.addNum(name0,v0);
         this.dayAll.addNum(name0,v0);
      }
      
      public function getDayDropNum(name0:String) : int
      {
         return this.dayObj.getAttribute(name0);
      }
      
      public function getDayDropAll(name0:String) : int
      {
         var v0:Number = this.dayAll.getAttribute(name0);
         if(name0 == "timeCapsule_1")
         {
            v0 += this.timeCMore;
         }
         return v0;
      }
   }
}

