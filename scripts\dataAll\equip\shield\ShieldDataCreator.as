package dataAll.equip.shield
{
   import dataAll._player.PlayerData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.must.define.MustDefine;
   import dataAll.things.define.ThingsDefine;
   
   public class ShieldDataCreator
   {
      
      public function ShieldDataCreator()
      {
         super();
      }
      
      public static function getSave(name0:String, num0:int) : ShieldSave
      {
         var d0:ShieldDefine = Gaming.defineGroup.shield.getDefine(name0);
         if(!(d0 is ShieldDefine))
         {
            INIT.showError("找不到定义ShieldDefine：" + name0);
         }
         return getSaveByDefine(d0,num0);
      }
      
      public static function getSaveByDefine(d0:ShieldDefine, num0:int) : ShieldSave
      {
         var s0:ShieldSave = new ShieldSave();
         s0.inDataByDefine(d0);
         s0.nowNum = num0;
         return s0;
      }
      
      public static function getTempData(d0:ShieldDefine, pd0:PlayerData) : ShieldData
      {
         var s0:ShieldSave = getSaveByDefine(d0,1);
         var da0:ShieldData = new ShieldData();
         da0.inData_bySave(s0,pd0,null);
         return da0;
      }
      
      public static function getUpgradeMust(shieldDefine0:ShieldDefine, lv0:int, placeType0:String = "") : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var num0:int = getUpgradeMustThingsNum(lv0,shieldDefine0);
         var thingsMust0:String = shieldDefine0.getUpgradeMustThingsName();
         var thingsD0:ThingsDefine = Gaming.defineGroup.things.getDefine(thingsMust0);
         if(Boolean(thingsD0))
         {
            d0.inThingsDataByArr([thingsMust0 + ";" + num0]);
         }
         else
         {
            num0 = 1;
            if(placeType0 == ItemsDataGroup.PLACE_BAG)
            {
               num0++;
            }
            d0.inThingsDataByArr([thingsMust0 + "_1;" + num0],MustDefine.NUM_EQUIP);
         }
         d0.coin = 20000;
         return d0;
      }
      
      private static function getUpgradeMustThingsNum(lv0:int, shieldDefine0:ShieldDefine) : int
      {
         return int(shieldDefine0.composeMustNum);
      }
   }
}

