package com.sounto.image
{
   import flash.geom.ColorTransform;
   
   public class AnimationImage extends MultipleImage
   {
      
      public var order_arr:Array = [];
      
      protected var endCtrl:String = "play";
      
      protected var affter_label:String = "";
      
      protected var colorF:ColorTransform = new ColorTransform();
      
      protected var colorF2:ColorTransform = null;
      
      private var hurtTime:int = 0;
      
      private var hurt_t:int = 0;
      
      public function AnimationImage()
      {
         super();
      }
      
      public function setOrder(arr0:Array, _breakB:Boolean = true, _endCtrl:String = "play") : void
      {
         if(_breakB)
         {
            this.show(arr0[0]);
            arr0.shift();
         }
         this.order_arr = arr0;
         this.endCtrl = _endCtrl;
      }
      
      public function getAffterLAbel() : String
      {
         return this.affter_label;
      }
      
      override protected function show(label0:String) : Boolean
      {
         var bb0:Boolean = super.show(label0);
         this.affter_label = "";
         return bb0;
      }
      
      public function play(label0:String, similarPan:Boolean = false) : void
      {
         if(nowLabel.indexOf(label0) == -1 || !similarPan)
         {
            this.order_arr.length = 0;
            this.show(label0);
         }
      }
      
      public function gotoPlayFrame(frame0:int) : void
      {
         if(Boolean(nowMc))
         {
            nowMc.gotoAndPlay(frame0);
         }
      }
      
      public function toPlay(label0:String, similarPan:Boolean = false) : void
      {
         var le:String = null;
         if(nowLabel.indexOf(label0) == -1 && this.affter_label != label0 || !similarPan)
         {
            le = this.getToLabel(nowLabel,label0);
            if(le != "")
            {
               this.setOrder([le,label0]);
               this.affter_label = label0;
            }
            else
            {
               this.order_arr.length = 0;
               this.show(label0);
               this.affter_label = "";
            }
         }
      }
      
      public function getToLabel(label0:String, label1:String) : String
      {
         var le0:String = label0 + "__";
         var le1:String = label0 + "__" + label1;
         var le2:String = "__" + label1;
         var le:String = "";
         if(haveLabel(le0))
         {
            le = le0;
         }
         else if(haveLabel(le1))
         {
            le = le1;
         }
         else if(haveLabel(le2))
         {
            le = le2;
         }
         return le;
      }
      
      public function setToPlay(toLabel0:String, label0:String) : void
      {
         this.affter_label = label0;
         this.setOrder([toLabel0,label0]);
      }
      
      public function nowHaveTo(label0:String) : Boolean
      {
         return nowLabel.indexOf("__") >= 0 && nowLabel.indexOf(label0) >= 0;
      }
      
      override public function FTimer(timeStopB0:Boolean = false) : void
      {
         if(enabled)
         {
            if(timeStopB0 == false)
            {
               if(this.order_arr.length > 0)
               {
                  if(nowMc.currentFrame >= nowMc.totalFrames)
                  {
                     this.show(this.order_arr[0]);
                     this.order_arr.shift();
                     if(this.order_arr.length == 0)
                     {
                        nowMc[this.endCtrl]();
                     }
                  }
               }
            }
            super.FTimer(timeStopB0);
         }
      }
   }
}

