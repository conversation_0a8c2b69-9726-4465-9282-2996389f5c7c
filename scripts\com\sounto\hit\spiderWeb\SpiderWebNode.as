package com.sounto.hit.spiderWeb
{
   public class SpiderWebNode
   {
      
      public static const JUMP:String = "jump";
      
      public var id:String = "";
      
      public var arr:Array = [];
      
      public var type:String = "";
      
      public var jumpRa:Number = 0;
      
      public var x:int = 0;
      
      public var y:int = 0;
      
      public var G:int = 0;
      
      public var H:int = 0;
      
      public var F:int = 0;
      
      public var father:SpiderWebNode = null;
      
      public function SpiderWebNode()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         this.x = obj0.x;
         this.y = obj0.y;
         this.id = obj0.id;
         this.arr = obj0.arr;
         this.type = obj0.type;
         this.jumpRa = obj0.jumpRa;
      }
      
      public function toString() : String
      {
         return this.id;
      }
      
      public function clear() : void
      {
         this.G = 0;
         this.H = 0;
         this.F = 0;
         this.father = null;
      }
   }
}

