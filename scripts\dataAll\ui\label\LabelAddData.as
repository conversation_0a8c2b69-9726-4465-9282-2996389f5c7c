package dataAll.ui.label
{
   public class LabelAddData
   {
      
      public var index:int = 0;
      
      public var label:String = "";
      
      public var cnName:String = "";
      
      public var imgType:String = "";
      
      public var childArr:Array = [];
      
      public var setSmallIconB:Boolean = false;
      
      public var childIsGroupB:Boolean = false;
      
      public function LabelAddData()
      {
         super();
      }
      
      public function inDataOther(imgType0:String, labelArr0:Array, cnNameArr0:Array) : void
      {
         var n:* = undefined;
         var label0:String = null;
         var cnName0:String = null;
         var da0:LabelAddData = null;
         this.imgType = imgType0;
         for(n in labelArr0)
         {
            label0 = labelArr0[n];
            cnName0 = cnNameArr0[n];
            da0 = new LabelAddData();
            da0.inDataOne(label0,cnName0,imgType0,n);
            this.childArr.push(da0);
         }
      }
      
      public function inDataOne(label0:String, cnName0:String, imgType0:String, index0:int) : void
      {
         this.label = label0;
         this.cnName = cnName0;
         this.imgType = imgType0;
         this.index = index0;
      }
      
      public function inDataByStr(str0:String, index0:*) : void
      {
         var strArr0:Array = str0.split(",");
         this.label = strArr0[0];
         this.cnName = strArr0[1];
         this.imgType = strArr0[2];
         this.index = index0;
      }
      
      public function inChildDataByStrArr(arr0:Array) : void
      {
         var n:* = undefined;
         var str0:String = null;
         var da0:LabelAddData = null;
         for(n in arr0)
         {
            str0 = arr0[n];
            da0 = new LabelAddData();
            da0.inDataByStr(str0,n);
            this.childArr.push(da0);
         }
      }
      
      public function addChildData(da0:LabelAddData) : void
      {
         this.childArr.push(da0);
         if(da0.childArr.length > 0)
         {
            this.childIsGroupB = true;
         }
      }
      
      public function getChildByLabel(label0:String) : LabelAddData
      {
         var n:* = undefined;
         var da0:LabelAddData = null;
         for(n in this.childArr)
         {
            da0 = this.childArr[n];
            if(da0.label == label0)
            {
               return da0;
            }
         }
         return null;
      }
   }
}

