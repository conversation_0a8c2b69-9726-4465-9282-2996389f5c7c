package dataAll.equip.define
{
   import com.sounto.oldUtils.ComMethod;
   
   public class EquipColor
   {
      
      public static const WHITE:String = "white";
      
      public static const GREEN:String = "green";
      
      public static const BLUE:String = "blue";
      
      public static const PURPLE:String = "purple";
      
      public static const ORANGE:String = "orange";
      
      public static const RED:String = "red";
      
      public static const BLACK:String = "black";
      
      public static const DARKGOLD:String = "darkgold";
      
      public static const PURGOLD:String = "purgold";
      
      public static const YAGOLD:String = "yagold";
      
      public static const TYPE_ARR:Array = [WHITE,GREEN,BLUE,PURPLE,ORANGE,RED,BLACK,DARKGOLD,PURGOLD,YAGOLD];
      
      public static const NORMAL_TYPE_ARR:Array = [WHITE,GREEN,BLUE,PURPLE,ORANGE];
      
      public static const PARTS_TYPE_ARR:Array = [WHIT<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>LUE,PURPLE,ORANGE,RED,<PERSON><PERSON><PERSON><PERSON>];
      
      public static const RARE_MORE_ARR:Array = [RED,<PERSON><PERSON><PERSON><PERSON>,<PERSON>ARKGOLD,PURGOLD,YAGOLD];
      
      public static const moreBlackArr:Array = [BLACK,DARKGOLD,PURGOLD,YAGOLD];
      
      public static const noStrengthenMoveArr:Array = [DARKGOLD,PURGOLD,YAGOLD];
      
      public static const strengthenMustMul1_8:Array = [DARKGOLD,PURGOLD,YAGOLD];
      
      public static const CN_ARR:Array = ["白","绿","蓝","紫","橙","红","黑","金","紫金","氩金"];
      
      public static const CN_ARR2:Array = ["白色","绿色","蓝色","紫色","橙色","红色","黑色","金色","紫金","氩金"];
      
      public static const whiteColor:uint = 16777215;
      
      public static const greenColor:uint = 65280;
      
      public static const blueColor:uint = 65535;
      
      public static const purpleColor:uint = 16724991;
      
      public static const orangeColor:uint = 16763904;
      
      public static const redColor:uint = 16730184;
      
      public static const blackColor:uint = 7961023;
      
      public static const darkgoldColor:uint = 16776960;
      
      public static const purgoldColor:uint = 14825080;
      
      public static const yagoldColor:uint = 65460;
      
      public static const whiteHtmlColor:String = "#FFFFFF";
      
      public static const greenHtmlColor:String = "#00FF00";
      
      public static const blueHtmlColor:String = "#00FFFF";
      
      public static const purpleHtmlColor:String = "#FF66FF";
      
      public static const orangeHtmlColor:String = "#FFCC00";
      
      public static const redHtmlColor:String = "#FF4848";
      
      public static const blackHtmlColor:String = "#7979BF";
      
      public static const darkgoldHtmlColor:String = "#BFBF00";
      
      public static const purgoldHtmlColor:String = "#E23678";
      
      public static const yagoldHtmlColor:String = "#00ffb4";
      
      public static const ID_ARR:Array = ["08","07","06","05","04","03","02","01","00","10"];
      
      public static const PRICE_ARR:Array = [1,1.1,1.5,2,3,5,8,20,100,200];
      
      public function EquipColor()
      {
         super();
      }
      
      public static function getMustColorMul(color0:String) : Number
      {
         return 1;
      }
      
      public static function noStrengthenMoveB(color0:String) : Boolean
      {
         return noStrengthenMoveArr.indexOf(color0) >= 0;
      }
      
      public static function moreBlackB(color0:String) : Boolean
      {
         return moreBlackArr.indexOf(color0) >= 0;
      }
      
      public static function moreDarkgoldB(color0:String) : Boolean
      {
         return moreColorPan(color0,DARKGOLD);
      }
      
      public static function moreColorPan(color0:String, mColor0:String) : Boolean
      {
         var f0:int = int(TYPE_ARR.indexOf(color0));
         var mf0:int = int(TYPE_ARR.indexOf(mColor0));
         if(f0 >= mf0)
         {
            return true;
         }
         return false;
      }
      
      public static function getIndex(color0:String) : int
      {
         return TYPE_ARR.indexOf(color0);
      }
      
      public static function htmlColor(colorStr0:String) : String
      {
         return EquipColor[colorStr0 + "HtmlColor"];
      }
      
      public static function htmlColorByIndex(index0:int) : String
      {
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > TYPE_ARR.length - 1)
         {
            index0 = TYPE_ARR.length - 1;
         }
         var colorStr0:String = TYPE_ARR[index0];
         return EquipColor[colorStr0 + "HtmlColor"];
      }
      
      public static function color(colorStr0:String) : uint
      {
         return EquipColor[colorStr0 + "Color"];
      }
      
      public static function getPrice(type0:String) : Number
      {
         return PRICE_ARR[TYPE_ARR.indexOf(type0)];
      }
      
      public static function getID_byType(type0:String) : String
      {
         return ID_ARR[TYPE_ARR.indexOf(type0)];
      }
      
      public static function getCn(color0:String) : String
      {
         if(color0 == "")
         {
            return "";
         }
         return CN_ARR[TYPE_ARR.indexOf(color0)];
      }
      
      public static function getLongCn(color0:String) : String
      {
         var cn0:String = getCn(color0);
         if(cn0.length == 1)
         {
            cn0 += "色";
         }
         return cn0;
      }
      
      public static function getColorCn(color0:String) : String
      {
         return ComMethod.color(getLongCn(color0),htmlColor(color0));
      }
      
      public static function getAddColor(color0:String, addIndex0:int) : String
      {
         var index0:int = int(TYPE_ARR.indexOf(color0));
         index0 += addIndex0;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > TYPE_ARR.length - 1)
         {
            index0 = TYPE_ARR.length - 1;
         }
         return TYPE_ARR[index0];
      }
      
      public static function firstMax(color1:String, color2:String, sameB0:Boolean = false) : Boolean
      {
         var index1:int = int(TYPE_ARR.indexOf(color1));
         var index2:int = int(TYPE_ARR.indexOf(color2));
         if(sameB0)
         {
            return index1 >= index2;
         }
         return index1 > index2;
      }
      
      public static function getMaxColor(arr0:Array) : String
      {
         var color0:String = null;
         var f0:int = 0;
         var maxI0:int = -1;
         var max0:String = "";
         for each(color0 in arr0)
         {
            f0 = getIndex(color0);
            if(f0 > maxI0)
            {
               maxI0 = f0;
               max0 = color0;
            }
         }
         return max0;
      }
   }
}

