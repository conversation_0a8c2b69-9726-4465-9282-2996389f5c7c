package com.sounto.cf
{
   import com.common.data.Base64;
   
   public class AscCF
   {
      
      private static var enObj:Object = {};
      
      private static var deObj:Object = {};
      
      public function AscCF()
      {
         super();
      }
      
      public static function staticInit() : void
      {
         var f0:int = 0;
         var code0:int = 0;
         var en0:String = null;
         var de0:String = null;
         var ascArr0:Array = [];
         for(var i:int = 32; i <= 126; i++)
         {
            ascArr0.push(i);
         }
         for(i = 32; i <= 126; i++)
         {
            f0 = ascArr0.length * Math.random();
            code0 = int(ascArr0[f0]);
            ascArr0.splice(f0,1);
            en0 = String.fromCharCode(code0);
            de0 = String.fromCharCode(i);
            enObj[de0] = en0;
            deObj[en0] = de0;
         }
      }
      
      public static function encodeBase64(str0:String) : String
      {
         var s0:String = null;
         var r0:String = "";
         var len0:int = str0.length;
         for(var i:int = 0; i < len0; i++)
         {
            s0 = str0.charAt(i);
            r0 += enObj[s0];
         }
         return r0;
      }
      
      public static function decodeBase64(str0:String) : String
      {
         var s0:String = null;
         var r0:String = "";
         var len0:int = str0.length;
         for(var i:int = 0; i < len0; i++)
         {
            s0 = str0.charAt(i);
            r0 += deObj[s0];
         }
         return r0;
      }
      
      public static function encodeNumber(v0:Number) : String
      {
         return encodeBase64(String(v0));
      }
      
      public static function decodeNumber(str0:String) : Number
      {
         return Number(decodeBase64(str0));
      }
      
      public static function encode(str0:String) : String
      {
         return encodeBase64(Base64.encodeString(str0));
      }
      
      public static function decode(str0:String) : String
      {
         return Base64.decodeString(decodeBase64(str0));
      }
   }
}

