package dataAll.level.define.event
{
   public class LevelEventDefine
   {
      
      private static const createUnit_enemy:String = "createUnit:enemy";
      
      public var id:String = "";
      
      public var condtion:LevelEventConditionDefine = new LevelEventConditionDefine();
      
      public var orderArr:Array = [];
      
      public var isLastEnemyB:Boolean = false;
      
      public function LevelEventDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var order_xml0:XML = null;
         var d0:LevelEventOrderDefine = null;
         this.id = xml0.@id;
         this.condtion.inData_byXML(xml0.condition[0]);
         var order_xmllist0:XMLList = xml0.order;
         for(n in order_xmllist0)
         {
            order_xml0 = order_xmllist0[n];
            d0 = new LevelEventOrderDefine();
            d0.inData_byXML(order_xml0);
            this.orderArr.push(d0);
         }
      }
      
      public function getOrderArr(nowNum0:int) : Array
      {
         var arr0:Array = [];
         if(this.orderArr.length > 0)
         {
            if(this.condtion.orderChooseType == LevelEventConditionDefine.CHOOSE_NO)
            {
               arr0 = this.orderArr;
            }
            else if(this.condtion.orderChooseType == LevelEventConditionDefine.CHOOSE_RANDOM_ONE)
            {
               arr0 = [this.orderArr[int(Math.random() * this.orderArr.length)]];
            }
            else if(this.condtion.orderChooseType == LevelEventConditionDefine.CHOOSE_SEQUENCE_ONE)
            {
               arr0 = [this.orderArr[(nowNum0 - 1) % this.orderArr.length]];
            }
         }
         return arr0;
      }
      
      public function haveStringInOrder(str0:String) : Boolean
      {
         var n:* = undefined;
         var d0:LevelEventOrderDefine = null;
         for(n in this.orderArr)
         {
            d0 = this.orderArr[n];
            if(d0.xmlText.indexOf(str0) >= 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function findCreateEnemyOrder() : LevelEventOrderDefine
      {
         var n:* = undefined;
         var d0:LevelEventOrderDefine = null;
         for(n in this.orderArr)
         {
            d0 = this.orderArr[n];
            if(d0.xmlText.indexOf(createUnit_enemy) >= 0)
            {
               return d0;
            }
         }
         return null;
      }
   }
}

