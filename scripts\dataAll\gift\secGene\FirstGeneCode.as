package dataAll.gift.secGene
{
   import com.adobe.crypto.MD5;
   
   public class FirstGeneCode
   {
      
      public function FirstGeneCode()
      {
         super();
      }
      
      public static function getCode(uid0:String, lv0:int) : String
      {
         var trueLv0:int = Math.round(int(lv0 / 10) * 10);
         if(trueLv0 < 30)
         {
            trueLv0 = 30;
         }
         var s0:String = uid0 + "_" + trueLv0;
         var md5:String = MD5.hash(s0);
         return md5.substr(0,15);
      }
      
      public static function getAllCodeObj(uid0:String) : Object
      {
         var lv0:int = 0;
         var obj0:Object = {};
         for(var i:int = 3; i <= 9; i++)
         {
            lv0 = i * 10;
            obj0[lv0] = getCode(uid0,lv0);
         }
         return obj0;
      }
      
      public static function testGetGiftLv(uid0:String, code0:String) : int
      {
         var n:* = undefined;
         var obj0:Object = getAllCodeObj(uid0);
         for(n in obj0)
         {
            if(code0 == obj0[n])
            {
               return int(n);
            }
         }
         return 0;
      }
   }
}

