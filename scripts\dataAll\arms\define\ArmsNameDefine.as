package dataAll.arms.define
{
   import com.common.text.TextWay;
   
   public class ArmsNameDefine
   {
      
      private var obj:Object = {};
      
      public function ArmsNameDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var type_xml0:XML = null;
         var type0:String = null;
         var s0:String = null;
         var arr0:Array = null;
         var i:* = undefined;
         var s2:String = null;
         var name0:String = null;
         var cnName0:String = null;
         var type_xmllist0:XMLList = xml0.type;
         for(n in type_xmllist0)
         {
            type_xml0 = type_xmllist0[n];
            type0 = type_xml0.@name;
            s0 = String(type_xml0);
            arr0 = s0.split(";");
            for(i in arr0)
            {
               s2 = TextWay.toHan2(arr0[i]);
               if(!(!s2 || s2 == ""))
               {
                  name0 = s2.split(":")[0];
                  cnName0 = s2.split(":")[1];
                  if(name0.indexOf("/") == -1)
                  {
                     name0 = type0 + "/" + name0;
                  }
                  name0 = TextWay.toHanSpace(name0);
                  if(!this.obj.hasOwnProperty(type0))
                  {
                     this.obj[type0] = {};
                  }
                  this.obj[type0][name0] = cnName0.split(",");
               }
            }
         }
      }
      
      public function getNameArr(type0:String, url0:String) : Array
      {
         var arr0:Array = null;
         if(!this.obj.hasOwnProperty(type0))
         {
            return null;
         }
         return this.obj[type0][url0];
      }
      
      public function getRandomName(type0:String, url0:String) : String
      {
         var arr0:Array = this.getNameArr(type0,url0);
         if(!arr0)
         {
            return "";
         }
         return arr0[int(Math.random() * arr0.length)];
      }
      
      public function getArmsCnName(barrelUrl0:String, textureUrl0:String) : String
      {
         return this.getRandomName("texture",textureUrl0) + this.getRandomName("barrel",barrelUrl0);
      }
   }
}

