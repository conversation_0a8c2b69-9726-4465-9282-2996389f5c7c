package dataAll.pet.gene.define
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll.pro.PropertyArrayDefine;
   
   public class GenePropertyArrayDefine extends PropertyArrayDefine
   {
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public function GenePropertyArrayDefine()
      {
         super();
         this.growMin = 0;
         this.growMax = 0;
         this.growLimit = 0;
         this.limit4 = 0;
      }
      
      public function get growMin() : Number
      {
         return this.CF.getAttribute("growMin");
      }
      
      public function set growMin(v0:Number) : void
      {
         this.CF.setAttribute("growMin",v0);
      }
      
      public function get growMax() : Number
      {
         return this.CF.getAttribute("growMax");
      }
      
      public function set growMax(v0:Number) : void
      {
         this.CF.setAttribute("growMax",v0);
      }
      
      public function get growLimit() : Number
      {
         return this.CF.getAttribute("growLimit");
      }
      
      public function set growLimit(v0:Number) : void
      {
         this.CF.setAttribute("growLimit",v0);
      }
      
      public function get limit4() : Number
      {
         return this.CF.getAttribute("limit4");
      }
      
      public function set limit4(v0:Number) : void
      {
         this.CF.setAttribute("limit4",v0);
      }
      
      override public function inData_byXML(xml0:XML) : void
      {
         super.inData_byXML(xml0);
         this.growMin = Number(xml0.@growMin);
         this.growMax = Number(xml0.@growMax);
         this.growLimit = Number(xml0.@growLimit);
         this.limit4 = Number(xml0.@limit4);
      }
      
      public function getGrowLimit(geneD0:GeneDefine) : Number
      {
         if(geneD0.isLastB())
         {
            return this.limit4;
         }
         return this.growLimit;
      }
   }
}

