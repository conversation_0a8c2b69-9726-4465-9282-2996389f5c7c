package dataAll.equip.creator
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.creator.OneProData;
   import dataAll.skill.define.SkillDefine;
   
   public class EquipSkillCreator
   {
      
      private static var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public function EquipSkillCreator()
      {
         super();
      }
      
      public static function get maxLevel() : Number
      {
         return CF.getAttribute("maxLevel");
      }
      
      public static function set maxLevel(v0:Number) : void
      {
         CF.setAttribute("maxLevel",v0);
      }
      
      public static function init() : void
      {
         maxLevel = 50;
      }
      
      public static function setSkill(s0:EquipSave) : void
      {
         var xx:int = 0;
         var bb0:Boolean = s0.itemsLevel >= maxLevel;
         var pro0:Number = getProByColor(s0.color);
         if(bb0)
         {
            if(s0.color == "red")
            {
               xx = 0;
            }
            if(Math.random() <= pro0)
            {
               s0.skillArr = getSkillArr(1,s0.partType);
            }
         }
      }
      
      private static function getProByColor(color0:String) : Number
      {
         if(color0 == EquipColor.RED || color0 == EquipColor.BLACK || color0 == EquipColor.DARKGOLD)
         {
            return 1;
         }
         if(color0 == EquipColor.ORANGE)
         {
            return 0.19;
         }
         return 0;
      }
      
      private static function getSkillArr(num0:int, partsType0:String) : Array
      {
         var skillFather0:String = getSkillFather(partsType0);
         var arr0:Array = Gaming.defineGroup.skill.getSkillNameArr_byFather(skillFather0);
         return ComMethod.getRandomArray(arr0,num0);
      }
      
      public static function getSkillNameArr(partsType0:String) : Array
      {
         return Gaming.defineGroup.skill.getSkillNameArr_byFather(getSkillFather(partsType0));
      }
      
      private static function getSkillFather(partsType0:String) : String
      {
         if(partsType0 == EquipType.HEAD || partsType0 == EquipType.BELT)
         {
            return "headSkill";
         }
         return "coatSkill";
      }
      
      public static function getSkillTip(skillLabel0:String, compactB0:Boolean = false, titleB0:Boolean = true) : String
      {
         var str0:String = null;
         var s_d0:SkillDefine = Gaming.defineGroup.skill.getDefine(skillLabel0);
         if(Boolean(s_d0))
         {
            str0 = "";
            if(titleB0)
            {
               str0 += "<yellow " + s_d0.cnName + "/>" + "：";
            }
            if(!compactB0)
            {
               str0 += s_d0.getDescription(true,false);
               if(s_d0.isActiveB())
               {
                  str0 += "\n\n<green 技能冷却时间：" + s_d0.cd + "秒/>";
               }
            }
            return str0;
         }
         return "";
      }
      
      public static function getAllSkillTip(skillArr0:Array, compactB0:Boolean = false) : String
      {
         var n:* = undefined;
         var skillLabel0:String = null;
         var str0:String = "";
         for(n in skillArr0)
         {
            skillLabel0 = skillArr0[n];
            if(n != 0)
            {
               str0 += compactB0 ? "、" : "\n";
            }
            str0 += getSkillTip(skillLabel0,compactB0);
         }
         return str0;
      }
      
      public static function getOneProDataArr(s0:EquipSave) : Array
      {
         return getSkillOneProDataArr(s0);
      }
      
      private static function getSkillOneProDataArr(s0:EquipSave) : Array
      {
         var name0:String = null;
         var d0:SkillDefine = null;
         var da1:OneProData = null;
         var arr0:Array = [];
         var skillArr0:Array = s0.getSkillArr();
         for each(name0 in skillArr0)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            if(d0 is SkillDefine)
            {
               da1 = new OneProData();
               da1.name = name0;
               da1.cnName = d0.cnName;
               da1.type = "skill";
               arr0.push(da1);
               if(d0.isEquipRandomSkillB() == false)
               {
                  da1.lockB = true;
                  da1.noChangeLockB = true;
               }
            }
         }
         return arr0;
      }
   }
}

