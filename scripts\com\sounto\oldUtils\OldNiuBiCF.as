package com.sounto.oldUtils
{
   import com.common.data.Base64;
   
   public class OldNiuBiCF
   {
      
      private var varObj:Object;
      
      public function OldNiuBiCF()
      {
         super();
      }
      
      public function setAttribute(varName:String, varValue:Number) : *
      {
         var i:* = undefined;
         var tmpObj:Object = new Object();
         tmpObj = {"value":Base64.encodeString(Sounto64.encode(String(varValue)))};
         var tmpObj2:Object = new Object();
         for(i in this.varObj)
         {
            tmpObj2[i] = this.varObj[i];
         }
         tmpObj2[varName] = tmpObj.value;
         tmpObj = null;
         this.varObj = null;
         this.varObj = tmpObj2;
      }
      
      public function getAttribute(varName:String) : Number
      {
         if(this.varObj == null || !this.varObj.hasOwnProperty(varName))
         {
            return Number.NaN;
         }
         var tmpObj:Object = new Object();
         tmpObj.value = Number(Sounto64.decode(Base64.decodeString(this.varObj[varName])));
         return tmpObj.value;
      }
   }
}

