package com.sounto.utils
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.geom.ColorTransform;
   import flash.geom.Matrix;
   import flash.geom.Rectangle;
   
   public class BmpMethod
   {
      
      public function BmpMethod()
      {
         super();
      }
      
      public static function getBmp(mc:DisplayObject, rectOrBounds:Boolean = true, scale:Number = 1, cf0:ColorTransform = null, clipRect0:Rectangle = null) : BitmapData
      {
         var rect0:Rectangle = null;
         if(rectOrBounds)
         {
            rect0 = mc.getRect(mc);
         }
         else
         {
            rect0 = mc.getBounds(mc);
         }
         rect0.x *= scale;
         rect0.y *= scale;
         rect0.width *= scale;
         rect0.height *= scale;
         if(rect0.width == 0)
         {
            rect0.width = 1;
         }
         if(rect0.height == 0)
         {
            rect0.height = 1;
         }
         var mix:Matrix = new Matrix();
         mix.tx = -rect0.x / scale;
         mix.ty = -rect0.y / scale;
         mix.scale(scale,scale);
         var w0:Number = rect0.width;
         var h0:Number = rect0.height;
         if(Boolean(clipRect0))
         {
            w0 = clipRect0.width * scale;
            h0 = clipRect0.height * scale;
         }
         var bmp0:BitmapData = new BitmapData(w0,h0,true,0);
         bmp0.draw(mc,mix,cf0,null,clipRect0);
         return bmp0;
      }
      
      public static function shadowCopyBitmap(bmp0:Bitmap) : Bitmap
      {
         return new Bitmap(bmp0.bitmapData,bmp0.pixelSnapping,bmp0.smoothing);
      }
      
      public static function drawLines(bmp0:BitmapData, color0:uint, x0:int, y0:int, x2:int, y2:int) : void
      {
         var y:Number = NaN;
         var cy0:Number = NaN;
         var cx0:Number = NaN;
         for(var x:int = x0; x <= x2; x++)
         {
            y = y0;
            cy0 = y2 - y0;
            cx0 = x2 - x0;
            if(cy0 != 0 && cx0 != 0)
            {
               y = (x - x0) / cx0 * cy0 + y0;
            }
            bmp0.setPixel32(x,y,color0);
         }
      }
      
      public static function findBitmapInSp(sp0:DisplayObjectContainer) : Bitmap
      {
         var bit0:Bitmap = null;
         var num0:int = sp0.numChildren;
         for(var i:int = 0; i < num0; i++)
         {
            bit0 = sp0.getChildAt(i) as Bitmap;
            if(Boolean(bit0))
            {
               return bit0;
            }
         }
         return null;
      }
   }
}

