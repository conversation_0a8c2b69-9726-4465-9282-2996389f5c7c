package dataAll.things.define
{
   public class ThingsSmeltType
   {
      
      public static const coin:String = "coin";
      
      public static const gold:String = "gold";
      
      public static const stone:String = "stone";
      
      public static const effect:String = "effect";
      
      public static const currency:String = "currency";
      
      public static const box:String = "box";
      
      public static const armsChip:String = "armsChip";
      
      public static const equipChip:String = "equipChip";
      
      public static const armsEquip:String = "armsEquip";
      
      public static const pet:String = "pet";
      
      public static const vehicle:String = "vehicle";
      
      public static const otherChip:String = "otherChip";
      
      public static const festival:String = "festival";
      
      public static const other:String = "other";
      
      public static const no:String = "";
      
      public static const arr:Array = [armsEquip,armsChip,equipChip,otherChip,vehicle,box,stone,pet,effect,festival,currency,coin,gold,other,no];
      
      public function ThingsSmeltType()
      {
         super();
      }
      
      public static function getCn(name0:String) : String
      {
         if(name0 == coin)
         {
            return "银币商品";
         }
         if(name0 == gold)
         {
            return "黄金商品";
         }
         if(name0 == stone)
         {
            return "材料";
         }
         if(name0 == effect)
         {
            return "道具";
         }
         if(name0 == currency)
         {
            return "兑换券等";
         }
         if(name0 == box)
         {
            return "宝箱";
         }
         if(name0 == armsChip)
         {
            return "武器碎片";
         }
         if(name0 == equipChip)
         {
            return "装备碎片";
         }
         if(name0 == armsEquip)
         {
            return "武器装备相关";
         }
         if(name0 == pet)
         {
            return "宠物相关";
         }
         if(name0 == vehicle)
         {
            return "载具碎片";
         }
         if(name0 == otherChip)
         {
            return "其他碎片";
         }
         if(name0 == festival)
         {
            return "节日物品";
         }
         if(name0 == other)
         {
            return "其他";
         }
         if(name0 == no)
         {
            return "纯增产物品";
         }
         return name0;
      }
      
      public static function getGradeCn(grade0:int) : String
      {
         if(grade0 == -1)
         {
            return "0";
         }
         return grade0 + 1 + "";
      }
      
      public static function getGradeColor(grade0:int) : String
      {
         if(grade0 == -1)
         {
            return "#FF66FF";
         }
         if(grade0 == 0)
         {
            return "#00FF00";
         }
         if(grade0 == 1)
         {
            return "#0099FF";
         }
         if(grade0 == 2)
         {
            return "#FFFF00";
         }
         if(grade0 == 3)
         {
            return "#D94D29";
         }
         return "#999999";
      }
   }
}

