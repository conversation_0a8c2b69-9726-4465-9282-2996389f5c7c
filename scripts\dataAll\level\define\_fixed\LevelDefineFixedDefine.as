package dataAll.level.define._fixed
{
   import com.sounto.utils.ClassProperty;
   
   public class LevelDefineFixedDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var target:String = "";
      
      public var drop:String = "no";
      
      public var info:String = "no";
      
      public var unitG:String = "no";
      
      public var rectG:String = "no";
      
      public var eventG:String = "no";
      
      public function LevelDefineFixedDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
   }
}

