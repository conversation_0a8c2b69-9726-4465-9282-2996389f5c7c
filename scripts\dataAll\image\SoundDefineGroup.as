package dataAll.image
{
   import dataAll._app.edit.TorData;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll._base.IO_Define;
   import dataAll._base.NormalDefineGroup;
   import dataAll.arms.define.ArmsRangeDefine;
   
   public class SoundDefineGroup extends NormalDefineGroup
   {
      
      private var editAgentObj:Object = {};
      
      public function SoundDefineGroup()
      {
         super();
         defineClass = SoundDefine;
      }
      
      public function getDefine(name0:String) : SoundDefine
      {
         return obj[name0];
      }
      
      override public function afterInit() : void
      {
         var range0:ArmsRangeDefine = null;
         var url0:String = null;
         var cn0:String = null;
         var rangeArr0:Array = Gaming.defineGroup.bullet.getRangeArr();
         var father0:String = SoundDefine.SHOOT;
         for each(range0 in rangeArr0)
         {
            url0 = range0.def.shootSoundUrl;
            if(url0 != "")
            {
               if(getNormalDefine(url0) == null)
               {
                  cn0 = range0.def.cnName + "-射击";
                  this.newDefine(url0,cn0,father0);
               }
            }
         }
      }
      
      public function idImgUrlDefine(d0:ImageUrlDefine, fatherD0:IO_Define, posId0:String, posCn0:String) : void
      {
         var father0:String = null;
         var cn0:String = null;
         var url0:String = d0.soundUrl;
         if(url0 != "" && d0.soundRan == 0)
         {
            if(getNormalDefine(url0) == null)
            {
               father0 = SoundDefine.getFatherByPos(fatherD0,posId0);
               cn0 = fatherD0.getCnName();
               if(posCn0 != "")
               {
                  posCn0 += "-" + posCn0;
               }
               this.newDefine(url0,cn0,father0);
            }
         }
      }
      
      private function newDefine(url0:String, cn0:String, father0:String) : void
      {
         var d0:SoundDefine = null;
         if(getNormalDefine(url0) == null)
         {
            d0 = new SoundDefine();
            d0.name = url0;
            d0.cnName = cn0;
            d0.father = father0;
            addDefine(d0,father0);
         }
      }
      
      public function getEditAgent(proD0:EditProDefine, nowUrl:String, da0:TorData) : EditListAgent
      {
         var fatherArr0:Array = SoundDefine.ALL_ARR;
         var firstTitle0:String = "";
         var type0:String = "normal";
         return this.getEditListAgent(nowUrl,type0,fatherArr0,firstTitle0);
      }
      
      private function getEditListAgent(nowUrl:String, type0:String, fatherArr0:Array, firstTitle0:String) : EditListAgent
      {
         var cnArr0:Array = null;
         var zeroD0:SoundDefine = null;
         var father0:String = null;
         var arr0:Array = null;
         var d0:SoundDefine = null;
         var a0:EditListAgent = this.editAgentObj[type0];
         if(Boolean(a0))
         {
            a0.clearFun();
         }
         else
         {
            a0 = new EditListAgent();
            cnArr0 = SoundDefine.getFatherCnArr(fatherArr0);
            a0.inTitle(fatherArr0,cnArr0);
            zeroD0 = new SoundDefine();
            zeroD0.cnName = "空";
            zeroD0.father = SoundDefine.OTHER;
            a0.addDataLast(zeroD0,zeroD0.father);
            for each(father0 in fatherArr0)
            {
               arr0 = getArrByFather(father0);
               for each(d0 in arr0)
               {
                  a0.addDataLast(d0,father0);
               }
            }
            a0.cutPan();
            this.editAgentObj[type0] = a0;
         }
         a0.setFirstTitle(firstTitle0);
         a0.setNoLinkArr([nowUrl]);
         a0.createAllText();
         return a0;
      }
   }
}

