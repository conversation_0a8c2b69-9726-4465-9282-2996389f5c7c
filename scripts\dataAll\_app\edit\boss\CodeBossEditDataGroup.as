package dataAll._app.edit.boss
{
   import dataAll._app.edit.EditDataGroup;
   import dataAll._app.edit.EditSave;
   
   public class CodeBossEditDataGroup extends EditDataGroup
   {
      
      public function CodeBossEditDataGroup()
      {
         super();
         dataClass = BossEditData;
      }
      
      public function getArr() : Array
      {
         return arr;
      }
      
      public function newDataByCode(code0:String) : BossEditData
      {
         var obj0:Object = BossEditMethod.codeToObj(code0);
         return this.newDataByCodeAndObj(code0,obj0);
      }
      
      public function newDataByCodeArr(codeArr0:Array) : void
      {
         var code0:String = null;
         for each(code0 in codeArr0)
         {
            this.newDataByCode(code0);
         }
      }
      
      public function newDataByCodeAndObj(code0:String, obj0:Object) : BossEditData
      {
         var s0:EditSave = editSave.getNewSave();
         s0.obj = obj0;
         var da0:BossEditData = addSave(s0) as BossEditData;
         da0.setTempCode(code0);
         return da0;
      }
      
      public function afterInHistroy() : Bo<PERSON>an
      {
         if(arr.length > 10)
         {
            removeData(arr[0]);
            return true;
         }
         return false;
      }
      
      public function collectPan(da0:BossEditData) : String
      {
         var find0:BossEditData = null;
         if(da0.haveDataB() == false)
         {
            return "首领数据不存在！";
         }
         var max0:int = 10;
         if(arr.length >= max0)
         {
            return "最多只能收藏" + max0 + "个首领。";
         }
         find0 = this.getDataByTempCode(da0.getTempCode());
         if(Boolean(find0))
         {
            return "你已经收藏了该首领。";
         }
         return "";
      }
      
      public function collectData(da0:BossEditData) : void
      {
         addData(da0);
      }
      
      public function getDataByTempCode(code0:String) : BossEditData
      {
         var da0:BossEditData = null;
         for each(da0 in arr)
         {
            if(da0.getTempCode() == code0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getUIDataArr() : Array
      {
         var arr0:Array = arr.concat();
         arr0.reverse();
         return arr0;
      }
   }
}

