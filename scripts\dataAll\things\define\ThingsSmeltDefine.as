package dataAll.things.define
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class ThingsSmeltDefine
   {
      
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var havePriceB:Boolean = false;
      
      public var type:String = "";
      
      public var grade:int = -1;
      
      public var addType:String = "other";
      
      public function ThingsSmeltDefine()
      {
         super();
         this.maxNum = 0;
      }
      
      public function get price() : Number
      {
         return this.CF.getAttribute("price");
      }
      
      public function set price(v0:Number) : void
      {
         this.havePriceB = v0 > 0;
         this.CF.setAttribute("price",v0);
      }
      
      public function get maxNum() : Number
      {
         return this.CF.getAttribute("maxNum");
      }
      
      public function set maxNum(v0:Number) : void
      {
         this.CF.setAttribute("maxNum",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function canSmeltB() : Boolean
      {
         return this.type != "" && this.havePriceB && this.grade >= 0;
      }
      
      public function getPrice(setType0:String) : Number
      {
         if(setType0 == this.addType)
         {
            return this.price * 3;
         }
         return this.price;
      }
      
      public function getMinMust() : int
      {
         var price0:Number = this.price;
         var minPrice0:Number = 10;
         if(price0 == 0 || price0 >= minPrice0)
         {
            return 1;
         }
         return Math.ceil(minPrice0 / price0);
      }
   }
}

