package dataAll._app.login
{
   import com.sounto.utils.ClassProperty;
   import dataAll._player.role.RoleName;
   import dataAll.body.define.HeroDefine;
   
   public class SaveBaseData4399
   {
      
      public static var pro_arr:Array = [];
      
      public var index:int = 0;
      
      public var datetime:String = "";
      
      public var title:String = "";
      
      public var status:String = "0";
      
      public var create_time:String = "";
      
      public var update_times:Number = 0;
      
      public function SaveBaseData4399()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function inSaveObj(obj0:Object) : void
      {
         this.create_time = obj0.create_time;
         this.update_times = Number(obj0.update_times);
      }
      
      public function getCanUseB() : Boolean
      {
         return this.status == "0";
      }
      
      public function setToTest() : void
      {
         this.title = "爆枪";
      }
      
      public function getIndex() : int
      {
         return this.index;
      }
      
      public function getDatet() : String
      {
         return this.datetime;
      }
      
      public function getTitle() : String
      {
         return this.title;
      }
      
      public function getStatu() : String
      {
         return this.status;
      }
      
      public function getWinRole() : String
      {
         var role0:String = null;
         for each(role0 in RoleName.arr)
         {
            if(this.title.indexOf(role0) > 0)
            {
               return role0;
            }
         }
         return "";
      }
      
      public function getWinRoleStr() : String
      {
         var d0:HeroDefine = null;
         var role0:String = this.getWinRole();
         if(role0 != "")
         {
            d0 = Gaming.defineGroup.body.getHeroDefine(role0);
            if(Boolean(d0))
            {
               return d0.getRoleCn() + "通关";
            }
         }
         return "";
      }
      
      public function getSaveTip() : String
      {
         var s0:String = "建档时间：" + this.create_time;
         return s0 + ("\n存档次数：" + this.update_times);
      }
   }
}

