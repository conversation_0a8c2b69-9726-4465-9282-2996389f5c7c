package dataAll._app.head
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.head.define.HeadDefine;
   import dataAll._app.head.define.HeadHonorDefine;
   import dataAll._player.PlayerData;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.add.EquipAddChild;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.add.IO_EquipAddTipGetter;
   
   public class HeadData implements IO_EquipAddTipGetter, IO_EquipAddGetter
   {
      
      public var playerData:PlayerData;
      
      public var save:HeadSave = null;
      
      private var tempTipObj:Object = null;
      
      public function HeadData()
      {
         super();
      }
      
      public function inData_bySave(s0:HeadSave) : void
      {
         this.save = s0;
      }
      
      public function newDayCtrl(nowTimeStr0:String) : void
      {
         this.clearHave(nowTimeStr0);
      }
      
      public function getProAddObj() : Object
      {
         return this.getAddData();
      }
      
      public function getProAddMaxObj(pro0:String) : Object
      {
         return null;
      }
      
      public function getAddData() : EquipPropertyData
      {
         var s0:HeadOneSave = null;
         var honorDefine0:HeadHonorDefine = null;
         var d0:HeadDefine = null;
         var d2:HeadDefine = null;
         var addObj0:Object = null;
         var hobj0:Object = null;
         var obj0:Object = {};
         var tipObj0:Object = {};
         if(this.save.nowHead != "")
         {
            d0 = Gaming.defineGroup.head.getDefine(this.save.nowHead);
            obj0 = d0.getAddObj();
            EquipAddChild.addTipInObj(obj0,tipObj0,d0.cnName);
         }
         var e0:EquipPropertyData = new EquipPropertyData();
         e0.inData_byObj(obj0);
         for each(s0 in this.save.obj)
         {
            d2 = s0.getDefine();
            if(Boolean(d2) && d0 != d2)
            {
               if(d2.noEquip)
               {
                  addObj0 = d2.getAddObj();
                  e0.addData(addObj0);
                  EquipAddChild.addTipInObj(addObj0,tipObj0,d2.cnName);
               }
            }
         }
         honorDefine0 = Gaming.defineGroup.head.getHonorDefineByValue(this.getNowHonor());
         if(Boolean(honorDefine0))
         {
            hobj0 = honorDefine0.getAddObj();
            e0.addData(hobj0);
            EquipAddChild.addTipInObj(hobj0,tipObj0,"荣誉属性");
         }
         this.tempTipObj = tipObj0;
         return e0;
      }
      
      public function getProAddTipObj() : Object
      {
         return this.tempTipObj;
      }
      
      private function panHaveHead(name0:String) : Boolean
      {
         return this.save.panHaveHead(name0);
      }
      
      public function addHead(name0:String, nowTimeStr0:String) : void
      {
         this.save.addHead(name0,nowTimeStr0);
      }
      
      public function useHead(name0:String) : void
      {
         var before0:String = this.save.nowHead;
         this.save.useHead(name0);
         if(before0 != this.save.nowHead)
         {
            this.playerData.equip.fleshMergeData();
         }
      }
      
      public function unloadHead(name0:String) : void
      {
         var before0:String = this.save.nowHead;
         this.save.nowHead = "";
         if(before0 != this.save.nowHead)
         {
            this.playerData.equip.fleshMergeData();
         }
      }
      
      public function getNowHeadCn(noStr0:String = "", colorB0:Boolean = false) : String
      {
         var d0:HeadDefine = this.save.getNowHeadDefine();
         if(d0 is HeadDefine)
         {
            return colorB0 ? ComMethod.color(d0.cnName,"#00FF00") : d0.cnName;
         }
         return noStr0;
      }
      
      public function getAllHonor() : Number
      {
         return Gaming.defineGroup.head.getAllHonor();
      }
      
      public function getNowHonor() : Number
      {
         var s0:HeadOneSave = null;
         var obj0:Object = this.save.obj;
         var v0:Number = 0;
         for each(s0 in obj0)
         {
            v0 += s0.getDefine().getHonorValue();
         }
         return v0;
      }
      
      private function clearHave(nowTimeStr0:String) : void
      {
         var s0:HeadOneSave = null;
         var lifeDay0:int = 0;
         var newObj0:Object = {};
         for each(s0 in this.save.obj)
         {
            lifeDay0 = s0.getLifeDay(nowTimeStr0);
            if(lifeDay0 > 0)
            {
               newObj0[s0.name] = s0;
            }
            else
            {
               if(this.save.nowHead == s0.name)
               {
                  this.unloadHead(s0.name);
               }
               if(s0.name == "almighty_10")
               {
                  this.playerData.getSave().headCount.topOneNameArr.length = 0;
               }
            }
         }
         this.save.obj = newObj0;
      }
      
      private function flesh(nowTimeStr0:String) : void
      {
         var d0:HeadDefine = null;
         var t0:HeadTempData = null;
         var defineArr0:Array = Gaming.defineGroup.head.arr;
         for each(d0 in defineArr0)
         {
            if(!this.panHaveHead(d0.name))
            {
               t0 = HeadConditionCheck.check(d0,this.playerData.level,false);
               if(t0.completeB)
               {
                  this.addHead(d0.name,nowTimeStr0);
               }
            }
         }
      }
      
      public function getNoTempDataArr(timeStr0:String) : Array
      {
         var d0:HeadDefine = null;
         var t0:HeadTempData = null;
         this.flesh(timeStr0);
         var arr0:Array = [];
         var defineArr0:Array = Gaming.defineGroup.head.arr;
         for each(d0 in defineArr0)
         {
            if(!this.panHaveHead(d0.name))
            {
               t0 = HeadConditionCheck.check(d0,this.playerData.level);
               t0.inDataAffterCheck(d0,null,this.save,this.playerData.level,timeStr0);
               arr0.push(t0);
            }
         }
         return arr0;
      }
      
      public function getHaveTempDataArr(timeStr0:String) : Array
      {
         var s0:HeadOneSave = null;
         var d0:HeadDefine = null;
         var t0:HeadTempData = null;
         this.flesh(timeStr0);
         var arr0:Array = [];
         var sArr0:Array = this.save.getOneSaveSortArr();
         for each(s0 in sArr0)
         {
            d0 = s0.getDefine();
            t0 = new HeadTempData();
            t0.inDataAffterCheck(d0,s0,this.save,this.playerData.level,timeStr0);
            arr0.push(t0);
         }
         return arr0;
      }
   }
}

