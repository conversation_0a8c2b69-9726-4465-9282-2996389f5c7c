package dataAll._app.space.astron
{
   import dataAll._app.space.craft.CraftData;
   import dataAll._player.PlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.body.define.HeroDefine;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.define.EquipColor;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.define.HeroSkillDefine;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.hero.HeroBody;
   
   public class AstronData
   {
      
      private var PD:PlayerData = null;
      
      private var craftData:CraftData = null;
      
      private var bodyDef:NormalBodyDefine;
      
      private var body:IO_NormalBody = null;
      
      public function AstronData()
      {
         super();
      }
      
      public function overGamingClear() : void
      {
         this.body = null;
      }
      
      public function inCraftData(da0:CraftData, pd0:PlayerData) : void
      {
         this.PD = pd0;
         this.craftData = da0;
         this.bodyDef = Gaming.defineGroup.body.getDefine("Striker2");
      }
      
      public function getCnName() : String
      {
         if(Boolean(this.bodyDef))
         {
            return this.bodyDef.cnName;
         }
         return "";
      }
      
      public function getBodyDef() : NormalBodyDefine
      {
         return this.bodyDef;
      }
      
      public function getBody() : IO_NormalBody
      {
         return this.body;
      }
      
      public function setBody(b0:IO_NormalBody) : void
      {
         var hero0:HeroBody = null;
         var dat0:NormalBodyData = null;
         var imgObj0:Object = null;
         this.body = b0;
         if(Boolean(b0))
         {
            hero0 = b0 as HeroBody;
            dat0 = b0.getData();
            dat0.inAstronData(this);
            dat0.bodyLevel = this.level;
            dat0.setDpsFactor(this.getDps());
            dat0.setNewMaxLife(this.getLife());
            dat0.setHeadHurtMul(1);
            b0.getSkill().addSkill_byNameArr(this.getSkillArr());
            b0.getMot().maxJumpNum = 2;
            if(Boolean(hero0))
            {
               imgObj0 = Gaming.BG.getPartImageMcObjByEquipNameArr(this.getEquipNameArr(),this.bodyDef as HeroDefine);
               hero0.img.setAllEquip_byObj(imgObj0);
               hero0.dat.inNewArmsSaveGroup(Gaming.defineGroup.armsCreator.getLevelSaveGroup(this.getArmsNameArr(),true));
               hero0.dat.armsData.setAllArmsDps(this.getDps() * 0.7,true);
               hero0.dat.chargerData.setAllMaxMul(1.2,true);
            }
         }
      }
      
      public function get level() : int
      {
         return this.craftData.level;
      }
      
      public function getLife() : Number
      {
         return this.craftData.getLife();
      }
      
      public function getDps() : Number
      {
         return this.craftData.getDps();
      }
      
      private function getEquipNameArr() : Array
      {
         return ["spaceSuit"];
      }
      
      public function getArmsNameArr() : Array
      {
         var da0:ArmsData = null;
         var name0:String = null;
         var yaName0:String = null;
         var rangeD0:ArmsRangeDefine = null;
         var arr0:Array = ["dreamDie"];
         var haveObj0:Object = {};
         var allArr0:Array = this.PD.getArmsDataArr(true,true,true);
         for each(da0 in allArr0)
         {
            if(EquipColor.moreColorPan(da0.getColor(),EquipColor.YAGOLD))
            {
               name0 = da0.def.name;
               if(haveObj0.hasOwnProperty(name0) == false)
               {
                  haveObj0[name0] = 0;
                  yaName0 = name0 + "Ya";
                  rangeD0 = Gaming.defineGroup.bullet.getArmsRangeDefine(yaName0);
                  if(Boolean(rangeD0))
                  {
                     arr0.push(yaName0);
                  }
               }
            }
         }
         return arr0;
      }
      
      public function getSkillArr() : Array
      {
         return this.craftData.getSkillArr().concat(this.getPlayerSkillArr());
      }
      
      public function getPlayerSkillArr() : Array
      {
         var da0:HeroSkillData = null;
         var d0:HeroSkillDefine = null;
         var lv0:int = 0;
         var ar0:Array = [];
         var arr0:Array = this.PD.skill.dataArr.concat(this.PD.skillBag.dataArr);
         for each(da0 in arr0)
         {
            d0 = da0.save.getDefine();
            if(d0.isActiveB() && d0.lv >= 14)
            {
               lv0 = d0.lv - 9;
               if(d0.baseLabel == "moreMissile_hero" || d0.baseLabel == "groupLight_hero")
               {
                  lv0 = d0.lv;
               }
               ar0.push(d0.baseLabel + "_" + lv0);
            }
         }
         return ar0;
      }
   }
}

