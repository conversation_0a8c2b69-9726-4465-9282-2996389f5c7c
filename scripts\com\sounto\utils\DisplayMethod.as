package com.sounto.utils
{
   import com.sounto.image.AlignType;
   import com.sounto.math.Maths;
   import dataAll._data.ConstantDefine;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class DisplayMethod
   {
      
      private static var p:Point = new Point();
      
      public function DisplayMethod()
      {
         super();
      }
      
      public static function toLayer(sp1:DisplayObject, sp2:DisplayObject) : Boolean
      {
         var p0:DisplayObjectContainer = null;
         var index0:int = 0;
         if(<PERSON>olean(sp1.parent) && sp1.parent == sp2.parent)
         {
            p0 = sp1.parent;
            index0 = p0.getChildIndex(sp1);
            p0.setChildIndex(sp2,index0);
            return true;
         }
         return false;
      }
      
      public static function toUp(sp1:DisplayObject, sp2:DisplayObject) : Boolean
      {
         var p0:DisplayObjectContainer = null;
         var index1:int = 0;
         var index2:int = 0;
         if(Boolean(sp1.parent) && sp1.parent == sp2.parent)
         {
            p0 = sp1.parent;
            index1 = p0.getChildIndex(sp1);
            index2 = p0.getChildIndex(sp2);
            if(index1 < index2)
            {
               p0.setChildIndex(sp1,index2);
               return true;
            }
         }
         return false;
      }
      
      public static function clearAllChild(sp0:DisplayObjectContainer) : int
      {
         var num0:int = sp0.numChildren;
         for(var i:int = num0 - 1; i >= 0; i--)
         {
            sp0.removeChildAt(i);
         }
         return num0;
      }
      
      public static function getTempTruePointByCon(x0:Number, y0:Number, con:DisplayObject, addConXYB0:Boolean = true) : Point
      {
         var l0:Number = Math.sqrt(x0 * x0 + y0 * y0);
         var ra0:Number = Math.atan2(y0,x0) + con.rotation / 180 * Math.PI;
         p.x = l0 * Math.cos(ra0);
         p.y = l0 * Math.sin(ra0);
         if(addConXYB0)
         {
            p.x += con.x;
            p.y += con.y;
         }
         return p;
      }
      
      public static function getTempTruePoint(x0:Number, y0:Number, conX0:Number, conY0:Number, conRa0:Number, conScaleX0:int = 1) : Point
      {
         x0 *= conScaleX0;
         var l0:Number = Math.sqrt(x0 * x0 + y0 * y0);
         var ra0:Number = Math.atan2(y0,x0) + conRa0;
         p.x = l0 * Math.cos(ra0) + conX0;
         p.y = l0 * Math.sin(ra0) + conY0;
         return p;
      }
      
      public static function getTempTrueRa(ra0:Number, conRa0:Number, conScaleX0:int = 1) : Number
      {
         if(conScaleX0 < 0)
         {
            ra0 = Maths.flipRa_Y(ra0);
         }
         return Maths.ZhunJ(ra0 + conRa0);
      }
      
      public static function getTempInsidePoint(x0:Number, y0:Number, conX0:Number, conY0:Number, conRa0:Number, conScaleX0:int = 1) : Point
      {
         x0 -= conX0;
         y0 -= conY0;
         var l0:Number = Math.sqrt(x0 * x0 + y0 * y0);
         var ra0:Number = Math.atan2(y0,x0) - conRa0;
         p.x = l0 * Math.cos(ra0);
         p.y = l0 * Math.sin(ra0);
         p.x *= conScaleX0;
         return p;
      }
      
      public static function copyHaveBmpSprite(sp0:Sprite, sp1:Sprite) : void
      {
         var oneMC0:DisplayObject = null;
         var oneBitmap0:Bitmap = null;
         var oneShape0:Shape = null;
         var bitmap0:Bitmap = null;
         var len0:int = sp0.numChildren;
         for(var i:int = 0; i < len0; i++)
         {
            oneMC0 = sp0.getChildAt(i);
            oneBitmap0 = oneMC0 as Bitmap;
            oneShape0 = oneMC0 as Shape;
            if(oneBitmap0 is Bitmap)
            {
               bitmap0 = new Bitmap(oneBitmap0.bitmapData,"auto",oneBitmap0.smoothing);
               bitmap0.transform.matrix = oneBitmap0.transform.matrix;
               sp1.addChild(bitmap0);
            }
         }
         sp1.x = sp0.x;
         sp1.y = sp0.y;
         sp1.rotation = sp0.rotation;
      }
      
      public static function getCopyHaveBmpSprite(sp0:Sprite) : Sprite
      {
         var sp1:Sprite = new Sprite();
         copyHaveBmpSprite(sp0,sp1);
         return sp1;
      }
      
      public static function getBitmapInside(sp0:DisplayObjectContainer) : Bitmap
      {
         var oneMC0:DisplayObject = null;
         if(!sp0)
         {
            return null;
         }
         var len0:int = sp0.numChildren;
         for(var i:int = 0; i < len0; i++)
         {
            oneMC0 = sp0.getChildAt(i);
            if(oneMC0 is Bitmap)
            {
               return oneMC0 as Bitmap;
            }
         }
         return null;
      }
      
      public static function arrangeXOneMid(arr0:Array, xGap:int = 0, midX0:int = 0, firstY0:Number = 0) : void
      {
         var btnW0:Number = NaN;
         var num0:int = 0;
         var w0:Number = NaN;
         var firstX0:Number = NaN;
         if(arr0.length > 0)
         {
            btnW0 = Number(arr0[0].width);
            num0 = int(arr0.length);
            w0 = (num0 - 1) * xGap + num0 * btnW0;
            firstX0 = midX0 - w0 / 2;
            arrange(arr0,xGap,0,999,999,firstX0,firstY0);
         }
      }
      
      public static function arrange(arr0:Array, xGap:int = 0, yGap:int = 0, xNum:int = 999, yNum:int = 999, firstX0:Number = 0, firstY0:Number = 0, xAlign0:String = "", yAlign0:String = "", horizontalB:Boolean = true, visibleSetB0:Boolean = true) : void
      {
         var n:* = undefined;
         var lb0:DisplayObject = null;
         var btnRect0:Rectangle = null;
         var n0:int = 0;
         var nowPage0:int = 0;
         var firstIndex:int = xNum * yNum * nowPage0;
         var lastIndex:int = firstIndex + xNum * yNum;
         if(lastIndex > arr0.length)
         {
            lastIndex = int(arr0.length);
         }
         var beforeRight0:Number = firstX0;
         var beforeDown0:Number = firstY0;
         for(n in arr0)
         {
            lb0 = arr0[n];
            btnRect0 = lb0.getRect(lb0);
            if(n >= firstIndex && n < lastIndex)
            {
               n0 = n - firstIndex;
               lb0.x = beforeRight0 - btnRect0.x;
               lb0.y = beforeDown0 - btnRect0.y;
               alignment(lb0,lb0.x + btnRect0.x,lb0.y + btnRect0.y,xAlign0,yAlign0);
               if(horizontalB)
               {
                  if((n0 + 1) % xNum == 0)
                  {
                     beforeRight0 = firstX0;
                     beforeDown0 = lb0.y + btnRect0.y + btnRect0.height + yGap;
                  }
                  else
                  {
                     beforeRight0 = lb0.x + btnRect0.x + btnRect0.width + xGap;
                  }
               }
               else if((n0 + 1) % xNum == 0)
               {
                  beforeDown0 = firstY0;
                  beforeRight0 = lb0.x + btnRect0.x + btnRect0.width + xGap;
               }
               else
               {
                  beforeDown0 = lb0.y + btnRect0.y + btnRect0.height + yGap;
               }
               if(visibleSetB0)
               {
                  lb0.visible = true;
               }
            }
            else if(visibleSetB0)
            {
               lb0.visible = false;
            }
         }
      }
      
      public static function alignment(mc0:DisplayObject, x0:Number, y0:Number, xAlign0:String = "", yAlign0:String = "") : void
      {
         var rect0:Rectangle = mc0.getRect(mc0);
         if(xAlign0 == AlignType.MID)
         {
            mc0.x = -rect0.x - rect0.width / 2 + x0;
         }
         else if(xAlign0 == AlignType.LEFT)
         {
            mc0.x = -rect0.x + x0;
         }
         else if(xAlign0 == AlignType.RIGHT)
         {
            mc0.x = -rect0.x - rect0.width + x0;
         }
         if(yAlign0 == AlignType.MID)
         {
            mc0.y = -rect0.y - rect0.height / 2 + y0;
         }
         else if(yAlign0 == AlignType.TOP)
         {
            mc0.y = -rect0.y + y0;
         }
         else if(yAlign0 == AlignType.DOWN)
         {
            mc0.y = -rect0.y - rect0.height + y0;
         }
      }
      
      public static function setUISubPosition(target0:DisplayObject, sub0:DisplayObject, sub2:DisplayObject = null) : void
      {
         var maxH0:int = ConstantDefine.HEIGHT;
         var maxW0:int = ConstantDefine.WIDTH;
         var targetP0:Point = target0.localToGlobal(new Point());
         var allW0:Number = sub0.width;
         if(Boolean(sub2))
         {
            allW0 += sub2.width;
         }
         var direct0:int = -1;
         var rightX0:int = targetP0.x + target0.width;
         var leftX0:int = targetP0.x;
         var rightMore0:int = rightX0 + allW0 - maxW0;
         var leftMore0:int = allW0 - leftX0;
         if(leftMore0 > 0)
         {
            if(rightMore0 > 0)
            {
               if(rightMore0 > leftMore0 - 50)
               {
                  direct0 = -1;
               }
               else
               {
                  direct0 = 1;
               }
            }
            else
            {
               direct0 = 1;
            }
         }
         else
         {
            direct0 = -1;
         }
         var x0:int = 0;
         if(direct0 == 1)
         {
            if(rightMore0 < 0)
            {
               x0 = rightX0;
            }
            else
            {
               x0 = maxW0 - allW0;
            }
         }
         else if(direct0 == -1)
         {
            if(leftMore0 < 0)
            {
               x0 = leftX0 - sub0.width;
            }
            else
            {
               x0 = 0;
            }
         }
         var y0:int = targetP0.y;
         var allY0:int = sub0.height;
         if(Boolean(sub2))
         {
            if(allY0 < sub2.height)
            {
               allY0 = sub2.height;
            }
         }
         if(targetP0.y + allY0 > maxH0)
         {
            y0 = maxH0 - allY0;
            if(y0 < 0)
            {
               y0 = 0;
            }
         }
         sub0.x = x0;
         sub0.y = y0;
         if(Boolean(sub2))
         {
            sub2.y = y0;
            if(direct0 == 1)
            {
               sub2.x = x0 + sub0.width;
            }
            else
            {
               sub2.x = x0 - sub2.width;
            }
         }
      }
   }
}

