package dataAll.gift.giftHome
{
   public class GiftHomeDefine
   {
      
      public var levelArr:Array = [];
      
      private var levelObj:Object = {};
      
      public function GiftHomeDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var j:* = undefined;
         var x0:XML = null;
         var d0:GiftHomeLevelDefine = null;
         var xl0:XMLList = xml0.level;
         for(j in xl0)
         {
            x0 = xl0[j];
            d0 = new GiftHomeLevelDefine();
            d0.inData_byXML(x0);
            this.levelArr.push(d0);
            this.levelObj[d0.name] = d0;
         }
      }
      
      public function getLevelNameArr() : Array
      {
         var d0:GiftHomeLevelDefine = null;
         var arr0:Array = [];
         for each(d0 in this.levelArr)
         {
            arr0.push(d0.name);
         }
         return arr0;
      }
      
      public function getLevelCnArr() : Array
      {
         var d0:GiftHomeLevelDefine = null;
         var arr0:Array = [];
         for each(d0 in this.levelArr)
         {
            arr0.push(d0.cnName);
         }
         return arr0;
      }
      
      public function getLevelDefine(name0:String) : GiftHomeLevelDefine
      {
         return this.levelObj[name0];
      }
      
      public function getLevelDefineByAllNum(num0:int) : GiftHomeLevelDefine
      {
         var d0:GiftHomeLevelDefine = null;
         for(var i:int = this.levelArr.length - 1; i >= 0; i--)
         {
            d0 = this.levelArr[i];
            if(num0 >= d0.allMustNum)
            {
               return d0;
            }
         }
         return null;
      }
   }
}

