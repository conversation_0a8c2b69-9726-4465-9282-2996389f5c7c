package dataAll.arms.creator
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.arms.save.ArmsSave;
   import dataAll.bullet.BulletCritDefine;
   import dataAll.drop.define.ArmsColorDefine;
   import dataAll.equip.define.EquipColor;
   import dataAll.items.creator.OneProData;
   import dataAll.skill.define.SkillDefine;
   
   public class ArmsSpecialAndSkill
   {
      
      public static var skillArr:Array = [];
      
      public static var godSkillArr:Array = [];
      
      public function ArmsSpecialAndSkill()
      {
         super();
      }
      
      public static function setSave(s0:ArmsSave, dropName0:String) : void
      {
         var cd0:ArmsColorDefine = Gaming.defineGroup.dropColor.getByName(dropName0).arms;
         var index0:int = int(cd0.name.indexOf(s0.color));
         var specialNum0:Number = Number(cd0.specialNum[index0]);
         var skillNum0:Number = Number(cd0.skillNum[index0]);
         var godSkillNum0:Number = Number(cd0.godSkillNum[index0]);
         setSpecial(s0,specialNum0);
         setSkill(s0,skillNum0,false);
         setSkill(s0,godSkillNum0,true);
      }
      
      public static function inSkillData() : void
      {
         skillArr = Gaming.defineGroup.skill.armsSkillNameArr.concat([]);
         godSkillArr = Gaming.defineGroup.skill.godArmsSkillNameArr.concat([]);
         var xx:int = 0;
      }
      
      public static function setSkill(s0:ArmsSave, num0:Number, godB0:Boolean = false) : void
      {
         var cNum0:int = 0;
         var pro0:Number = NaN;
         var tNum0:int = 0;
         var arr0:Array = null;
         if(num0 > 0)
         {
            cNum0 = num0;
            pro0 = num0 - cNum0;
            tNum0 = cNum0 + (Math.random() < pro0 ? 1 : 0);
            if(tNum0 >= 1)
            {
               if(s0.getTrueLevel() < 35)
               {
                  tNum0 = 1;
               }
               if(EquipColor.getIndex(s0.color) < 5 && s0.getArmsType() == ArmsType.rocket)
               {
                  tNum0 = 1;
               }
               arr0 = ComMethod.getRandomArray(godB0 ? godSkillArr : skillArr,tNum0);
               setSkillByNameArr(s0,arr0,godB0);
            }
         }
      }
      
      public static function setSkillByNameArr(s0:ArmsSave, arr0:Array, godB0:Boolean) : void
      {
         if(godB0)
         {
            s0.godSkillArr = getTrueSkillArrByArr(s0,arr0,godB0);
         }
         else
         {
            s0.skillArr = getTrueSkillArrByArr(s0,arr0,godB0);
         }
      }
      
      public static function getTrueSkillArrByArr(s0:ArmsSave, arr0:Array, godB0:Boolean) : Array
      {
         var n:* = undefined;
         var skillName0:String = null;
         var d0:SkillDefine = null;
         var arr2:Array = [];
         for(n in arr0)
         {
            skillName0 = arr0[n];
            d0 = Gaming.defineGroup.skill.getDefineBy(godB0 ? "godArmsSkill" : "armsSkill",skillName0);
            if(!d0)
            {
               d0 = Gaming.defineGroup.skill.getDefine(skillName0);
            }
            if(d0.createByArmsTypePro != "")
            {
               arr2[n] = d0.name + "_" + s0.getArmsType();
            }
            else
            {
               arr2[n] = d0.name;
            }
         }
         return arr2;
      }
      
      private static function getMainSkillArrByArr(arr0:Array) : Array
      {
         var name0:String = null;
         var d0:SkillDefine = null;
         var name2:String = null;
         var f0:int = 0;
         var arr2:Array = [];
         for each(name0 in arr0)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            name2 = name0;
            if(d0.createByArmsTypePro != "")
            {
               f0 = int(name0.lastIndexOf("_"));
               name2 = name0.substr(0,f0);
               arr2.push(name0);
            }
         }
         return arr2;
      }
      
      public static function getCanSkillNameArr(s0:ArmsSave) : Array
      {
         var name0:String = null;
         var d0:SkillDefine = null;
         var arr2:Array = [];
         for each(name0 in skillArr)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            if(d0.createByArmsTypePro != "")
            {
               arr2.push(d0.name + "_" + s0.getArmsType());
            }
            else
            {
               arr2.push(d0.name);
            }
         }
         return arr2;
      }
      
      private static function setSpecial(s0:ArmsSave, num0:Number) : void
      {
         var arr2:Array = null;
         var i:int = 0;
         var index0:int = 0;
         var name0:String = null;
         if(s0.getArmsType() == ArmsType.rocket && s0.color == "purple")
         {
            return;
         }
         if(num0 > 0)
         {
            if(Math.random() <= num0)
            {
               arr2 = ArmsSpecial.specialArr.concat([]);
               for(i = 0; i < num0; i++)
               {
                  index0 = int(Math.random() * arr2.length);
                  name0 = arr2[index0];
                  arr2.splice(index0,1);
                  ArmsSpecialAndSkill[name0](s0);
               }
            }
         }
      }
      
      public static function setSpecialByNameArr(s0:ArmsSave, nameArr0:Array) : void
      {
         var name0:String = null;
         for each(name0 in nameArr0)
         {
            ArmsSpecialAndSkill[name0](s0);
         }
      }
      
      public static function setSuperSave(s0:ArmsSave, specialNameArr0:Array, skillArr0:Array, godSkillArr0:Array) : void
      {
         var sName0:String = null;
         if(Boolean(specialNameArr0))
         {
            for each(sName0 in specialNameArr0)
            {
               ArmsSpecialAndSkill[sName0](s0,true);
            }
         }
         setSkillByNameArr(s0,skillArr0,false);
         setSkillByNameArr(s0,godSkillArr0,true);
      }
      
      private static function penetrationGap(s0:ArmsSave, superB0:Boolean = false) : void
      {
         var i:int = EquipColor.getIndex(s0.color);
         if(i >= 2 && i <= 3)
         {
            s0.penetrationGap = int(50 + Math.random() * 30);
         }
         else if(i >= 4)
         {
            s0.penetrationGap = int(80 + Math.random() * 220);
         }
      }
      
      private static function twoShootPro(s0:ArmsSave, superB0:Boolean = false) : void
      {
         var i:int = EquipColor.getIndex(s0.color);
         if(i >= 2 && i <= 3)
         {
            s0.twoShootPro = 0.1 + Math.random() * 0.1;
            if(superB0)
            {
               s0.twoShootPro = 0.2;
            }
         }
         else if(i >= 4)
         {
            s0.twoShootPro = 0.2 + Math.random() * 0.1;
            if(superB0)
            {
               s0.twoShootPro = 0.3;
            }
         }
      }
      
      private static function penetrationNum(s0:ArmsSave, superB0:Boolean = false) : void
      {
         var i:int = EquipColor.getIndex(s0.color);
         if(i >= 2 && i <= 3)
         {
            s0.penetrationNum = 1;
         }
         else if(i >= 4)
         {
            s0.penetrationNum = 2;
         }
      }
      
      private static function crit(s0:ArmsSave, superB0:Boolean = false) : void
      {
         var d0:BulletCritDefine = new BulletCritDefine();
         var i:int = EquipColor.getIndex(s0.color);
         if(i >= 2 && i <= 3)
         {
            d0.pro = 0.1 + Math.random() * 0.1;
            d0.mul = 1;
         }
         else if(i >= 4)
         {
            d0.pro = 0.1 + Math.random() * 0.1;
            d0.mul = 2;
         }
         if(superB0)
         {
            d0.pro = 0.2;
         }
         s0.critD = d0;
      }
      
      private static function floorBounce(s0:ArmsSave, superB0:Boolean = false) : void
      {
         var i:int = EquipColor.getIndex(s0.color);
         if(i >= 2 && i <= 3)
         {
            s0.bounceD.floor = int(1 + Math.random() * 2);
            if(superB0)
            {
               s0.bounceD.floor = 2;
            }
         }
         else if(i >= 4)
         {
            s0.bounceD.floor = int(2 + Math.random() * 3);
            if(superB0)
            {
               s0.bounceD.floor = 4;
            }
         }
      }
      
      private static function bodyBounce(s0:ArmsSave, superB0:Boolean = false) : void
      {
         var i:int = EquipColor.getIndex(s0.color);
         if(i >= 2 && i <= 3)
         {
            s0.bounceD.body = int(1 + Math.random() * 1);
            if(superB0)
            {
               s0.bounceD.body = 1;
            }
         }
         else if(i >= 4)
         {
            s0.bounceD.body = int(2 + Math.random() * 2);
            if(superB0)
            {
               s0.bounceD.body = 3;
            }
         }
      }
      
      public static function getSpecialTip(d0:Object) : String
      {
         var name0:String = null;
         var s0:String = null;
         var str0:String = "";
         for each(name0 in ArmsSpecial.arr)
         {
            s0 = getOneSpecialAllString(d0,name0);
            if(s0 != "")
            {
               str0 += "\n" + s0;
            }
         }
         return str0;
      }
      
      public static function haveSpecialNum(d0:Object) : int
      {
         var num0:int = 0;
         if(d0.penetrationGap > 0)
         {
            num0++;
         }
         if(d0.penetrationNum > 0)
         {
            num0++;
         }
         if(d0.critD.mul > 0)
         {
            num0++;
         }
         if(d0.bounceD.floor > 0)
         {
            num0++;
         }
         if(d0.bounceD.body > 0)
         {
            num0++;
         }
         if(d0.twoShootPro > 0)
         {
            num0++;
         }
         return num0;
      }
      
      public static function getSpecialNameArr(d0:ArmsDefine) : Array
      {
         var arr0:Array = [];
         if(d0.penetrationNum > 0)
         {
            arr0.push("penetrationNum");
         }
         if(d0.critD.mul > 0)
         {
            arr0.push("crit");
         }
         if(d0.bounceD.floor > 0)
         {
            arr0.push("floorBounce");
         }
         if(d0.bounceD.body > 0)
         {
            arr0.push("bodyBounce");
         }
         if(d0.twoShootPro > 0)
         {
            arr0.push("twoShootPro");
         }
         return arr0;
      }
      
      public static function haveOneSpecial(d0:Object, name0:String) : Boolean
      {
         if(name0 == "crit")
         {
            return d0.critD.mul > 0;
         }
         if(name0 == "floorBounce")
         {
            return d0.bounceD.floor > 0;
         }
         if(name0 == "bodyBounce")
         {
            return d0.bounceD.body > 0;
         }
         if(d0.hasOwnProperty(name0))
         {
            if(d0[name0] is Number || d0[name0] is int)
            {
               return d0[name0] > 0;
            }
         }
         return false;
      }
      
      public static function setOneValueByOther(d0:ArmsSave, d2:ArmsSave, name0:String) : void
      {
         if(name0 == "crit")
         {
            d0.critD.mul = d2.critD.mul;
            d0.critD.pro = d2.critD.pro;
         }
         else if(name0 == "floorBounce")
         {
            d0.bounceD.floor = d2.bounceD.floor;
         }
         else if(name0 == "bodyBounce")
         {
            d0.bounceD.body = d2.bounceD.body;
         }
         else if(d0.hasOwnProperty(name0))
         {
            if(d0[name0] is Number || d0[name0] is int)
            {
               d0[name0] = d2[name0];
            }
         }
      }
      
      public static function samePan(d0:ArmsSave, d2:ArmsSave) : Boolean
      {
         var name0:String = null;
         var n:* = undefined;
         for each(name0 in ArmsSpecial.arr)
         {
            if(getOneSpecialAllString(d0,name0) != getOneSpecialAllString(d2,name0))
            {
               return false;
            }
         }
         for(n in d0.skillArr)
         {
            if(d0.skillArr[n] != d2.skillArr[n])
            {
               return false;
            }
         }
         return true;
      }
      
      public static function getOneProDataArr(s0:ArmsSave) : Array
      {
         var arr0:Array = getSpcialOneProDataArr(s0);
         return arr0.concat(getSkillOneProDataArr(s0));
      }
      
      private static function getSpcialOneProDataArr(s0:ArmsSave) : Array
      {
         var name0:String = null;
         var str0:String = null;
         var strArr0:Array = null;
         var da1:OneProData = null;
         var lockProArr0:Array = null;
         var arr0:Array = [];
         var ad0:ArmsDefine = s0.getArmsRangeDefine().def;
         var rareB0:Boolean = EquipColor.RARE_MORE_ARR.indexOf(ad0.color) >= 0;
         for each(name0 in ArmsSpecial.arr)
         {
            str0 = getOneSpecialAllString(s0,name0);
            if(str0 != "")
            {
               strArr0 = str0.split("|");
               da1 = new OneProData();
               da1.name = name0;
               da1.cnName = strArr0[0];
               da1.valueString = strArr0[1];
               da1.type = "special";
               lockProArr0 = ArmsType.getLockPro(s0.getArmsType(),ad0);
               if(Boolean(lockProArr0))
               {
                  if(lockProArr0.indexOf(da1.name) >= 0)
                  {
                     da1.lockB = true;
                     da1.noChangeLockB = true;
                  }
               }
               if(rareB0)
               {
                  da1.lockB = true;
               }
               arr0.push(da1);
            }
         }
         return arr0;
      }
      
      private static function getSkillOneProDataArr(s0:ArmsSave) : Array
      {
         var name0:String = null;
         var d0:SkillDefine = null;
         var da1:OneProData = null;
         var arr0:Array = [];
         var skillArr0:Array = s0.skillArr;
         for each(name0 in skillArr0)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            if(d0 is SkillDefine)
            {
               da1 = new OneProData();
               da1.name = name0;
               da1.cnName = d0.cnName;
               da1.type = "skill";
               arr0.push(da1);
            }
         }
         return arr0;
      }
      
      public static function getOneSpecialAllString(d0:Object, name0:String) : String
      {
         if(haveOneSpecial(d0,name0))
         {
            if(name0 == "twoShootPro")
            {
               return "瞬间连发|" + TextWay.numberToPer(d0.twoShootPro) + "概率";
            }
            if(name0 == "penetrationNum")
            {
               return "穿人个数|" + d0.penetrationNum + "个";
            }
            if(name0 == "penetrationGap")
            {
               return "穿墙深度|" + d0.penetrationGap;
            }
            if(name0 == "crit")
            {
               return ComMethod.toFixed(d0.critD.mul,1) + "倍暴击|" + TextWay.numberToPer(d0.critD.pro) + "概率";
            }
            if(name0 == "floorBounce")
            {
               return "地面反弹|" + d0.bounceD.floor + "次";
            }
            if(name0 == "bodyBounce")
            {
               return "击中反弹|" + d0.bounceD.body + "次";
            }
         }
         return "";
      }
      
      public static function getSkillTip(skillLabel0:String, compactB0:Boolean = false) : String
      {
         var str0:String = null;
         var s_d0:SkillDefine = Gaming.defineGroup.skill.getDefineBy("armsSkill",skillLabel0);
         if(!s_d0)
         {
            s_d0 = Gaming.defineGroup.skill.getDefineBy("godArmsSkill",skillLabel0);
         }
         if(!s_d0)
         {
            s_d0 = Gaming.defineGroup.skill.getDefine(skillLabel0);
         }
         if(Boolean(s_d0))
         {
            str0 = "<yellow " + s_d0.cnName + "/>";
            if(!compactB0)
            {
               str0 += "：" + s_d0.getDescription();
            }
            return str0;
         }
         return "";
      }
      
      public static function getAllSkillTip(skillArr0:Array, compactB0:Boolean = false) : String
      {
         var n:* = undefined;
         var skillLabel0:String = null;
         var str0:String = "";
         for(n in skillArr0)
         {
            skillLabel0 = skillArr0[n];
            if(n != 0)
            {
               str0 += compactB0 ? "、" : "\n";
            }
            str0 += getSkillTip(skillLabel0,compactB0);
         }
         return str0;
      }
      
      public static function canGotoFirstPan(da0:ArmsData) : String
      {
         var d0:ArmsDefine = da0.def;
         var haveNum0:int = haveSpecialNum(d0);
         if(haveNum0 == 0)
         {
            return "该武器没有初始特殊属性。";
         }
         var colorB0:Boolean = EquipColor.moreBlackB(d0.color);
         if(colorB0 == false)
         {
            return "黑色及以上武器才能恢复初始特殊属性。";
         }
         return "";
      }
      
      public static function gotoFirstSpecial(da0:ArmsData) : Boolean
      {
         var s0:ArmsSave = null;
         var d0:ArmsDefine = da0.def;
         var error0:String = canGotoFirstPan(da0);
         if(error0 == "")
         {
            s0 = da0.save;
            s0.twoShootPro = d0.twoShootPro;
            s0.penetrationNum = d0.penetrationNum;
            s0.critD.inData_byObj(d0.critD);
            s0.bounceD.inData_byObj(d0.bounceD);
            s0.penetrationGap = d0.penetrationGap;
            return true;
         }
         return false;
      }
   }
}

