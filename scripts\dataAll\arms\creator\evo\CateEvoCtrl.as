package dataAll.arms.creator.evo
{
   import com.sounto.utils.ArrayMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.must.define.MustDefine;
   
   public class CateEvoCtrl extends DiyArmsEvoCtrl
   {
      
      private static const hurtMulArr:Array = [90,100,110,145,155,165,180,195,195,195,195,195,195].concat([210,220]).concat([230]);
      
      public function CateEvoCtrl()
      {
         super();
      }
      
      override public function getCnName(cn0:String, evoLv0:int, d0:ArmsDefine) : String
      {
         return ArmsEvoCtrl.getCnName(cn0,evoLv0,d0,false);
      }
      
      override public function getHurtMul(evoLv0:int, d0:ArmsDefine) : Number
      {
         var index0:int = evoLv0 - 1 + d0.evoMustFirstLv;
         return Number(ArrayMethod.getElementLimit(hurtMulArr,index0)) / 100;
      }
      
      override public function doEvo(da0:ArmsData) : void
      {
         var s0:ArmsSave = da0.save;
         var lv0:int = s0.evoLv + da0.def.evoMustFirstLv;
         if(lv0 == 8)
         {
            s0.doEvo(6);
         }
         else
         {
            s0.doEvo();
         }
         s0.armsImgLabel = getArmsImageName(da0.def,s0.evoLv);
      }
      
      override public function getMust(evoLv0:int, ad0:ArmsDefine) : MustDefine
      {
         var mustLv0:int = evoLv0 + 1 + ad0.evoMustFirstLv;
         var d0:MustDefine = new MustDefine();
         d0.lv = 90;
         d0.coin = 0;
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 6)
         {
            arr0 = ["rocketCate;120","armsRadium;240","armsTitanium;360","skeletonMedal_1;30"];
         }
         if(mustLv0 == 7)
         {
            arr0 = ["rocketCate;200","armsRadium;320","armsTitanium;480","skeletonMedal_1;60"];
         }
         if(mustLv0 == 8)
         {
            arr0 = ["rocketCate;400","armsRadium;600","armsTitanium;720","skeletonMedal_1;60"];
         }
         if(mustLv0 >= 9 && mustLv0 <= 14)
         {
            arr0 = ["rocketCate;800","fireGem;500","poisonGem;500","zodiacCash;120","demStone;330"];
         }
         d0.inThingsDataByArr(arr0);
         return d0;
      }
      
      public function doEvoPurgold(s0:ArmsSave) : void
      {
         ArrayMethod.addNoRepeatInArr(s0.godSkillArr,"catePurgold");
      }
      
      override public function haveEvoLv(d0:ArmsDefine, evoLv0:int) : Boolean
      {
         var lv0:int = evoLv0 + d0.evoMustFirstLv;
         if(lv0 > 8 && lv0 < 8 + 6)
         {
            return false;
         }
         return true;
      }
   }
}

