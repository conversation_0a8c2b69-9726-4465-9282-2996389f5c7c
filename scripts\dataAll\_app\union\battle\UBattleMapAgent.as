package dataAll._app.union.battle
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.union.info.MemberInfo;
   import dataAll.ui.text.ProTipType;
   
   public class UBattleMapAgent
   {
      
      private static const delCharStr:String = "*.:_-+![]{}()<>【】●《》『』！◤◢·：、";
      
      public var def:UnionBattleMapDefine = null;
      
      private var memberArr:Array = [];
      
      public function UBattleMapAgent()
      {
         super();
      }
      
      public function init(d0:UnionBattleMapDefine) : void
      {
         this.def = d0;
      }
      
      public function clearData() : void
      {
         this.memberArr.length = 0;
      }
      
      public function get name() : String
      {
         return this.def.name;
      }
      
      public function addMemberInfo(i0:MemberInfo) : void
      {
         this.memberArr.push(i0);
      }
      
      public function sortByLevelTime() : void
      {
         var i0:MemberInfo = null;
         this.memberArr.sort(this.sortByLevelTime_fun);
         var rank0:int = 1;
         for each(i0 in this.memberArr)
         {
            i0.setBattleRank(rank0);
            rank0++;
         }
      }
      
      private function sortByLevelTime_fun(a0:MemberInfo, b0:MemberInfo) : int
      {
         var at0:Number = a0.extraObj.lt;
         var bt0:Number = b0.extraObj.lt;
         if(UBattleAgent.nullLevelTime(at0))
         {
            return 1;
         }
         if(UBattleAgent.nullLevelTime(bt0))
         {
            return -1;
         }
         return ArrayMethod.sortNumberFun(at0,bt0);
      }
      
      public function getTopOneNull() : MemberInfo
      {
         if(this.memberArr.length > 0)
         {
            return this.memberArr[0];
         }
         return null;
      }
      
      public function findMemberInfo(info0:MemberInfo) : int
      {
         return this.memberArr.indexOf(info0);
      }
      
      public function getMemberNum() : int
      {
         return this.memberArr.length;
      }
      
      public function getBtnTopText() : String
      {
         var s0:String = "";
         var top0:MemberInfo = this.getTopOneNull();
         if(Boolean(top0))
         {
            s0 = int(top0.getBattleScore()) + "·" + StringMethod.keepCharNumLast(top0.extraObj.playerName,6,delCharStr);
         }
         return s0;
      }
      
      public function getBtnTip() : String
      {
         var i0:MemberInfo = null;
         var s0:String = "";
         s0 += ProTipType.getTitleMixed("个人战斗力加成上限");
         s0 += "\n" + this.getRankText(1);
         s0 += "\n" + this.getRankText(2);
         s0 += "\n" + this.getRankText(3);
         s0 += "\n" + this.getRankText(4);
         s0 += "\n" + this.getRankText(5);
         s0 += "\n" + this.getRankText(6,"其他");
         s0 += "\n";
         if(this.memberArr.length > 0)
         {
            s0 += "\n" + ProTipType.getTitleMixed("积分排名");
            for each(i0 in this.memberArr)
            {
               s0 += "\n<yellow " + int(i0.getBattleScore()) + "/>·<green " + NumberMethod.toFixed(i0.extraObj.lt,2) + "秒 /><gray " + i0.extraObj.playerName + "/>";
            }
         }
         return s0;
      }
      
      private function getRankText(rank0:int, title0:String = "") : String
      {
         if(title0 == "")
         {
            title0 = "第" + rank0 + "名";
         }
         return "<gray " + title0 + "：" + NumberMethod.toPer(this.def.getDpsMax(rank0),0) + "/>";
      }
   }
}

