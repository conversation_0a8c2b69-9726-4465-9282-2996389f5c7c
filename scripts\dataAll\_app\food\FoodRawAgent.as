package dataAll._app.food
{
   import dataAll.equip.creator.EquipSkillCreator;
   import dataAll.ui.text.ProTipType;
   
   public class FoodRawAgent
   {
      
      private var def:FoodRawDefine;
      
      private var foodData:IO_FoodAgentFather;
      
      public function FoodRawAgent()
      {
         super();
      }
      
      public function init(def0:FoodRawDefine, foodData0:IO_FoodAgentFather) : void
      {
         this.def = def0;
         this.foodData = foodData0;
      }
      
      public function get cnName() : String
      {
         return this.def.cnName;
      }
      
      public function get name() : String
      {
         return this.def.name;
      }
      
      public function get iconUrl() : String
      {
         return this.def.iconUrl;
      }
      
      public function get num() : int
      {
         return this.foodData.getRawNum(this.def.name);
      }
      
      public function getGatherTip() : String
      {
         var s0:String = "";
         s0 += ProTipType.getTitleMixed("食物技能");
         return s0 + ("\n" + EquipSkillCreator.getSkillTip(this.def.skill,false,false));
      }
   }
}

