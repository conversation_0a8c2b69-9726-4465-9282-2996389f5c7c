package dataAll._app.outfit
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.outfit.define.OutfitDefine;
   import dataAll._app.outfit.define.OutfitMustDefine;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.device.DeviceDefine;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.equip.weapon.WeaponDefine;
   
   public class OutfitDataPan
   {
      
      public function OutfitDataPan()
      {
         super();
      }
      
      public static function pan(da0:OutfitData, pd0:NormalPlayerData) : void
      {
         var mustD0:OutfitMustDefine = null;
         var str0:String = null;
         var bb0:Boolean = false;
         var mustStr0:String = "";
         var d0:OutfitDefine = da0.define;
         da0.haveB = true;
         for each(mustD0 in d0.mustArr)
         {
            str0 = mustD0.getMustStr();
            bb0 = panOne(mustD0,pd0);
            if(bb0)
            {
               str0 = ComMethod.color(str0 + " √","#00FF00");
            }
            else
            {
               da0.haveB = false;
            }
            mustStr0 += str0 + "\n";
         }
         da0.mustStr = mustStr0;
      }
      
      private static function panOne(d0:OutfitMustDefine, pd0:NormalPlayerData) : Boolean
      {
         var fun0:Function = OutfitDataPan[d0.type];
         if(fun0 is Function)
         {
            return fun0(d0,pd0);
         }
         return false;
      }
      
      private static function arms(d0:OutfitMustDefine, pd0:NormalPlayerData) : Boolean
      {
         var da0:ArmsData = null;
         var bb0:Boolean = false;
         var dataArr0:Array = pd0.arms.dataArr;
         for each(da0 in dataArr0)
         {
            bb0 = normalPan(d0,da0.def.name,da0.armsType,da0.save.getTrueLevel(),da0.getColor());
            if(bb0)
            {
               return true;
            }
         }
         return false;
      }
      
      private static function equip(d0:OutfitMustDefine, pd0:NormalPlayerData) : Boolean
      {
         var da0:EquipData = null;
         var nameArr0:Array = null;
         var equipD0:EquipDefine = null;
         var name0:String = null;
         var n0:String = null;
         var bb0:Boolean = false;
         var dataArr0:Array = pd0.equip.dataArr;
         for each(da0 in dataArr0)
         {
            nameArr0 = [];
            equipD0 = da0.save.getDefine();
            name0 = equipD0.name;
            if(equipD0 is WeaponDefine)
            {
               nameArr0 = [(equipD0 as WeaponDefine).baseLabel];
            }
            else if(equipD0 is DeviceDefine)
            {
               nameArr0 = [(equipD0 as DeviceDefine).baseLabel];
            }
            else if(equipD0 is VehicleDefine)
            {
               nameArr0 = (equipD0 as VehicleDefine).getAllBeforeNameArr().concat([name0]);
            }
            else
            {
               nameArr0 = [name0];
            }
            for each(n0 in nameArr0)
            {
               bb0 = normalPan(d0,n0,da0.save.partType,da0.save.getTrueLevel(),da0.getColor());
               if(bb0)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      private static function normalPan(d0:OutfitMustDefine, name0:String, partType0:String, lv0:int, color0:String) : Boolean
      {
         if(d0.nameArr.length == 0 || d0.nameArr.indexOf(name0) >= 0)
         {
            if(d0.partType == "" || d0.partType == partType0)
            {
               if(lv0 >= d0.lv)
               {
                  if(d0.colorArr.length == 0 || d0.colorArr.indexOf(color0) >= 0)
                  {
                     return true;
                  }
               }
            }
         }
         return false;
      }
   }
}

