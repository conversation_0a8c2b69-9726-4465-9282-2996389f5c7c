package dataAll.equip.jewelry
{
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.save.EquipSave;
   
   public class JewelrySave extends EquipSave
   {
      
      private static const ZERO:JewelrySave = new JewelrySave();
      
      public function JewelrySave()
      {
         super();
      }
      
      override public function getZero() : EquipSave
      {
         return ZERO;
      }
      
      override public function inDataByDefine(d0:EquipDefine) : void
      {
         super.inDataByDefine(d0);
         var md0:JewelryDefine = d0 as JewelryDefine;
         name = md0.name;
         itemsLevel = md0.lv;
         skillArr = md0.skillArr.concat();
      }
      
      public function getJewelryDefine() : JewelryDefine
      {
         return Gaming.defineGroup.jewelry.getDefine(imgName);
      }
      
      override public function getTrueObj() : Object
      {
         return this.getJewelryDefine().getAddObj();
      }
      
      override public function getDefine() : EquipDefine
      {
         return this.getJewelryDefine();
      }
      
      override public function getDataClass() : EquipData
      {
         return new JewelryData();
      }
      
      override public function getSkillArr() : Array
      {
         return this.getJewelryDefine().skillArr;
      }
      
      override public function clone() : EquipSave
      {
         var obj0:Object = ClassProperty.copyObj(this);
         var s0:EquipSave = new JewelrySave();
         s0.inData_byObj(obj0);
         return s0;
      }
   }
}

