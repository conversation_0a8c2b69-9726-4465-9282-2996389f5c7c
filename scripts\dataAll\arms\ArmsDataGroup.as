package dataAll.arms
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.creator.ArmsDataCreator;
   import dataAll.arms.define.ArmsType;
   import dataAll.arms.save.ArmsSave;
   import dataAll.arms.save.ArmsSaveGroup;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.define.EquipColor;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsBatchColor;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.ItemsMoreOrder;
   import dataAll.items.SwapFail;
   import dataAll.items.save.ItemsSave;
   import dataAll.items.save.ItemsSaveGroup;
   import dataAll.ui.tip.CheckData;
   
   public class ArmsDataGroup extends ItemsDataGroup
   {
      
      public var haveHeroEnabled:Boolean = true;
      
      public var nowData:ArmsData = null;
      
      public var saveGroup:ArmsSaveGroup = null;
      
      public var tempCapacityObj:Object = {};
      
      public function ArmsDataGroup()
      {
         super();
         dataType = ItemsDataGroup.TYPE_ARMS;
      }
      
      public static function getData_MaxDpsByType(arr0:Array, type0:String) : ArmsData
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         var dps0:Number = NaN;
         var max0:Number = 0;
         var da2:ArmsData = null;
         for(n in arr0)
         {
            da0 = arr0[n];
            if(da0.armsType == type0 || type0 == "")
            {
               if(Gaming.LG.canArmsPan(da0))
               {
                  dps0 = da0.getUIShowDps();
                  if(max0 < dps0)
                  {
                     max0 = dps0;
                     da2 = da0;
                  }
               }
            }
         }
         return da2;
      }
      
      public static function filterCanEvoDataArr(marr0:Array) : Array
      {
         var da0:ArmsData = null;
         var arr0:Array = [];
         for each(da0 in marr0)
         {
            if(da0.isCanEvoB())
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public static function armsSwapTo(dg1:ArmsDataGroup, dg2:ArmsDataGroup, site1:int, site2:int, wearCompareLevel:int = 9999) : CheckData
      {
         var da2:IO_ItemsData = null;
         var da1:IO_ItemsData = null;
         var check0:CheckData = new CheckData();
         if(dg1 != dg2)
         {
            if(dg1.placeType == ItemsDataGroup.PLACE_WEAR)
            {
               if(dg1.dataArr.length <= 1)
               {
                  da2 = dg2.getDataBySite(site2);
                  if(!da2)
                  {
                     check0.bb = false;
                     check0.info = "至少装备一把武器！";
                     check0.name = SwapFail.mustHaveOneArms;
                     return check0;
                  }
               }
            }
            else if(dg2.dataArr.length <= 1)
            {
               da1 = dg1.getDataBySite(site1);
               if(!da1)
               {
                  check0.bb = false;
                  check0.info = "至少装备一把武器！";
                  check0.name = SwapFail.mustHaveOneArms;
                  return check0;
               }
            }
         }
         return ItemsDataGroup.swapTo(dg1,dg2,site1,site2,wearCompareLevel);
      }
      
      override public function inData_bySaveGroup(sg0:ItemsSaveGroup) : void
      {
         var n:* = undefined;
         var s0:ArmsSave = null;
         var da0:ArmsData = null;
         this.clearData();
         this.saveGroup = sg0 as ArmsSaveGroup;
         for(n in sg0.arr)
         {
            s0 = sg0.arr[n];
            da0 = new ArmsData();
            da0.inData_bySave(s0,normalPlayerData,true,this);
            setIDIfNo(da0);
            da0.setPlaceType(placeType);
            dataArr[n] = da0;
            _siteDataArr = null;
         }
      }
      
      public function getAllDps() : Number
      {
         var n:* = undefined;
         var mul0:Number = NaN;
         var len0:int = 0;
         var da0:ArmsData = null;
         var s0:Number = NaN;
         var dps0:Number = 0;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(Gaming.LG.canArmsPan(da0))
            {
               s0 = da0.getUIShowDps();
               dps0 += s0;
            }
         }
         mul0 = 1;
         len0 = int(dataArr.length);
         if(len0 == 2)
         {
            mul0 = 0.9;
         }
         else if(len0 == 3)
         {
            mul0 = 0.8;
         }
         else if(len0 == 4)
         {
            mul0 = 0.75;
         }
         else if(len0 >= 5)
         {
            mul0 = 0.7;
         }
         return Math.ceil(dps0 * mul0);
      }
      
      public function getTrueDps() : Number
      {
         return this.getAllDps();
      }
      
      public function getShowDps() : Number
      {
         var da0:ArmsData = null;
         var addMul0:Number = NaN;
         var typeArr0:Array = [];
         for each(da0 in dataArr)
         {
            if(Gaming.LG.canArmsPan(da0))
            {
               if(typeArr0.indexOf(da0.armsType) == -1)
               {
                  typeArr0.push(da0.armsType);
               }
            }
         }
         addMul0 = 1 + typeArr0.length * 0.2;
         return Math.ceil(this.getTrueDps() * addMul0);
      }
      
      public function getNowData_MaxDpsByType(type0:String) : ArmsData
      {
         return getData_MaxDpsByType(dataArr,type0);
      }
      
      public function getMaxDpsByType(type0:String) : Number
      {
         var da0:ArmsData = this.getNowData_MaxDpsByType(type0);
         if(da0 is ArmsData)
         {
            return da0.getUIShowDps();
         }
         return 0;
      }
      
      public function fleshData_byEquip(ea0:EquipPropertyData) : void
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            da0.fleshData_byEquip(ea0);
         }
      }
      
      public function setAllArmsDps(dps0:Number, saveB0:Boolean = false) : void
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            da0.hurtRatio = ArmsDataCreator.getHurt(da0,dps0) * da0.getDpsMul();
            if(saveB0)
            {
               da0.save.hurtRatio = da0.hurtRatio;
            }
         }
      }
      
      public function fillAllData(capacityMul0:Number = 1) : void
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            da0.fillAllData(capacityMul0);
         }
         this.tempCapacityObj = null;
      }
      
      public function clearCapacity(mul0:int = 1) : void
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            da0.nowCapacityNum -= da0.capacity * mul0;
            if(da0.nowCapacityNum < 0)
            {
               da0.nowCapacityNum = 0;
            }
         }
      }
      
      public function setCapacityNow(now0:int) : void
      {
         var da0:ArmsData = null;
         for each(da0 in dataArr)
         {
            da0.nowCapacityNum = now0;
         }
      }
      
      public function onlyHaveNameArr(arr0:Array) : void
      {
         var da0:ArmsData = null;
         var delArr0:Array = [];
         for each(da0 in dataArr)
         {
            if(arr0.indexOf(da0.def.name) == -1)
            {
               delArr0.push(da0);
            }
         }
         removeDataArr(delArr0);
      }
      
      override public function clearData() : void
      {
         super.clearData();
         this.nowData = null;
      }
      
      override public function addSave(s0:ItemsSave, fleshSaveGroupB0:Boolean = true) : IO_ItemsData
      {
         var da0:ArmsData = new ArmsData();
         da0.inData_bySave(s0 as ArmsSave,normalPlayerData,true,this);
         addData(da0,fleshSaveGroupB0);
         return da0;
      }
      
      public function getNextSiteArmsData(firstB0:Boolean, typeLimitArr0:Array = null, nameLimitArr0:Array = null) : ArmsData
      {
         var arr0:Array = this.getSiteDataArrMust(firstB0,typeLimitArr0,nameLimitArr0);
         var index0:int = int(arr0.indexOf(this.nowData));
         if(index0 >= 0)
         {
            index0 = (index0 + 1) % arr0.length;
         }
         else
         {
            index0 = 0;
         }
         return arr0[index0];
      }
      
      public function getSiteDataArrMust(firstB0:Boolean, typeLimitArr0:Array = null, nameLimitArr0:Array = null) : Array
      {
         var arr2:Array = null;
         var da0:ArmsData = null;
         var addB0:Boolean = false;
         var siteArr0:Array = getSiteDataArray();
         var arr0:Array = siteArr0;
         if(firstB0 || typeLimitArr0 || Boolean(nameLimitArr0))
         {
            arr2 = [];
            for each(da0 in arr0)
            {
               addB0 = da0.panCanUseOne(firstB0,typeLimitArr0,nameLimitArr0);
               if(addB0)
               {
                  arr2.push(da0);
               }
            }
            if(arr2.length > 0)
            {
               arr0 = arr2;
            }
            else
            {
               arr0 = this.getArrByFirstB(siteArr0);
            }
         }
         return arr0;
      }
      
      private function getArrByFirstB(siteArr0:Array = null) : Array
      {
         var da0:ArmsData = null;
         var addB0:Boolean = false;
         if(siteArr0 == null)
         {
            siteArr0 = getSiteDataArray();
         }
         var arr0:Array = siteArr0;
         var arr2:Array = [];
         for each(da0 in arr0)
         {
            addB0 = da0.panCanUseOne(true,null,null);
            if(addB0)
            {
               arr2.push(da0);
            }
         }
         if(arr2.length > 0)
         {
            arr0 = arr2;
         }
         else
         {
            arr0 = siteArr0;
         }
         return arr0;
      }
      
      public function getCanUseArr(firstB0:Boolean, typeLimitArr0:Array = null, nameLimitArr0:Array = null, chargerG0:ArmsChargerDataGroup = null) : Array
      {
         var da0:ArmsData = null;
         var addB0:Boolean = false;
         var arr2:Array = [];
         for each(da0 in dataArr)
         {
            addB0 = da0.panCanUseOne(firstB0,typeLimitArr0,nameLimitArr0,chargerG0);
            if(addB0)
            {
               arr2.push(da0);
            }
         }
         return arr2;
      }
      
      public function nowArmsDataInArrB() : Boolean
      {
         return this.indexOf(this.nowData) >= 0;
      }
      
      public function indexOf(da0:ArmsData) : int
      {
         return dataArr.indexOf(da0);
      }
      
      public function getArmsDataByGap(len0:int, firstChoiceB0:Boolean) : ArmsData
      {
         var n:* = undefined;
         var arr2:Array = null;
         var da0:ArmsData = null;
         var r0:Number = NaN;
         var arr0:Array = dataArr.concat([]);
         if(normalPlayerData is NormalPlayerData)
         {
            if(firstChoiceB0 && !normalPlayerData.isPlayerCtrlB())
            {
               arr2 = [];
               for each(da0 in arr0)
               {
                  if(da0.save.firstChoiceB)
                  {
                     arr2.push(da0);
                  }
               }
               if(arr2.length > 0)
               {
                  arr0 = arr2;
               }
            }
         }
         arr0.sort(this.sortByAIShootRange);
         for(n in arr0)
         {
            da0 = arr0[n];
            r0 = da0.getAIShootRange();
            if(r0 > len0)
            {
               return da0;
            }
         }
         return arr0[arr0.length];
      }
      
      private function sortByAIShootRange(da0:ArmsData, da1:ArmsData) : int
      {
         var r0:Number = da0.getAIShootRange();
         var r1:Number = da1.getAIShootRange();
         if(r0 > r1)
         {
            return 1;
         }
         if(r0 < r1)
         {
            return -1;
         }
         return 0;
      }
      
      public function getHaveCapacityArmsData(armsNameRange0:Array = null) : ArmsData
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(Boolean(armsNameRange0))
            {
               if(armsNameRange0.indexOf(da0.name) == -1)
               {
                  continue;
               }
            }
            if(da0.nowCapacityNum > 0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getDataByBaseName(label0:String) : ArmsData
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(da0.name == label0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getDataByArmsType(type0:String) : ArmsData
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(da0.save.getArmsType() == type0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getArmsNameRange() : Array
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         var arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(arr0.indexOf(da0.name) == -1)
            {
               arr0.push(da0.name);
            }
         }
         return arr0;
      }
      
      override public function getBatchSellDataArr(colorArr0:Array) : Array
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         var bb0:Boolean = false;
         var sell_arr0:Array = [];
         this.sort(null);
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(da0.save.site > 1 && !da0.save.lockB && !da0.havePartsB() && da0.getEvoLv() <= 1)
            {
               bb0 = ItemsBatchColor.dataPanColorArr(da0.getSave(),colorArr0);
               if(bb0)
               {
                  sell_arr0.push(da0);
               }
            }
         }
         return sell_arr0;
      }
      
      override public function getSortBtnText() : String
      {
         return "战斗力排序";
      }
      
      override public function getBatchSellTextTip() : String
      {
         var str0:String = ComMethod.color("保留：","#00FF00");
         str0 += "\n战斗力前" + ComMethod.color("2","#00FF00") + "位的武器";
         return str0 + "\n装配着零件的武器";
      }
      
      override public function getComposeTextTip() : String
      {
         var str0:String = ComMethod.color("保留：","#00FF00");
         str0 += "\n战斗力前" + ComMethod.color("2","#00FF00") + "位的武器";
         return str0 + "\n装配着零件的武器";
      }
      
      public function getBestColor() : String
      {
         var da0:ArmsData = null;
         var color2:String = null;
         var color0:String = "white";
         for each(da0 in dataArr)
         {
            color2 = da0.getColor();
            if(EquipColor.firstMax(color2,color0))
            {
               color0 = color2;
            }
         }
         return color0;
      }
      
      public function getMaxPartsNum() : int
      {
         var da0:ArmsData = null;
         var num0:int = 0;
         var max0:int = 0;
         for each(da0 in dataArr)
         {
            num0 = int(da0.partsData.dataArr.length);
            if(num0 > max0)
            {
               max0 = num0;
            }
         }
         return max0;
      }
      
      public function saveNowTempCapacity() : void
      {
         var da0:ArmsData = null;
         this.tempCapacityObj = {};
         for each(da0 in dataArr)
         {
            this.tempCapacityObj[da0.save.id] = da0.nowCapacityNum;
         }
      }
      
      public function readNowTempCapacity() : void
      {
         var da0:ArmsData = null;
         for each(da0 in dataArr)
         {
            if(this.tempCapacityObj is Object)
            {
               if(this.tempCapacityObj.hasOwnProperty(da0.save.id))
               {
                  da0.nowCapacityNum = this.tempCapacityObj[da0.save.id];
               }
               else
               {
                  da0.nowCapacityNum = 0;
               }
            }
         }
      }
      
      public function FTimer() : void
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(da0 != this.nowData)
            {
               if(da0.now_t < da0.attackGap)
               {
                  da0.now_t += 1 / 30;
               }
            }
         }
      }
      
      override public function getChosenChooseOrderArr() : Array
      {
         var arr0:Array = [];
         arr0.push(ItemsMoreOrder.allChoose);
         arr0.push(ItemsMoreOrder.invertChoose);
         arr0.push(ItemsMoreOrder.unlockChoose);
         return arr0;
      }
      
      override public function getChosenCtrlOrderArr() : Array
      {
         var arr0:Array = null;
         arr0 = [];
         arr0 = super.getChosenCtrlOrderArr();
         arr0.push(ItemsMoreOrder.refining);
         arr0.push(ItemsMoreOrder.sell);
         arr0.push(ItemsMoreOrder.decompose);
         return arr0;
      }
      
      public function repair() : Array
      {
         var da0:ArmsData = null;
         var site0:int = 0;
         var delArr0:Array = [];
         for each(da0 in dataArr)
         {
            site0 = da0.save.site;
            if(site0 > this.saveGroup.gripMaxNum - 1 || !this.saveGroup.getUnlockBySite(site0))
            {
               delArr0.push(da0);
            }
         }
         removeDataArr(delArr0);
         return delArr0;
      }
      
      override public function sort(dg0:ItemsDataGroup) : void
      {
         this.sortByDps();
         _siteDataArr = null;
      }
      
      public function sortByDps() : void
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         var arr0:Array = dataArr.concat([]);
         arr0.sort(this.sortByDpsFun);
         for(n in arr0)
         {
            da0 = arr0[n];
            da0.save.site = n;
         }
      }
      
      private function sortByDpsFun(da0:ArmsData, da1:ArmsData) : int
      {
         var dps0:Number = da0.getUIShowDps();
         var dps1:Number = da1.getUIShowDps();
         if(dps0 < dps1)
         {
            return 1;
         }
         if(dps0 == dps1)
         {
            return 0;
         }
         return -1;
      }
      
      public function zuobiPan() : String
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         var str0:String = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            str0 = da0.zuobiPan();
            if(str0 != "")
            {
               return da0.getCnName() + "lv." + da0.save.getTrueLevel() + "：" + str0;
            }
         }
         return this.saveGroup.zuobiPan();
      }
      
      override public function getSaveGroup() : ItemsSaveGroup
      {
         return this.saveGroup;
      }
      
      override public function getGripType() : String
      {
         return "armsGrip";
      }
      
      override public function getAllChildTypeArr() : Array
      {
         return ArmsType.TYPE_ARR;
      }
      
      override public function getBlackMarketCanShowTypeArr() : Array
      {
         return ArmsType.NORMAL_TYPE_ARR;
      }
   }
}

