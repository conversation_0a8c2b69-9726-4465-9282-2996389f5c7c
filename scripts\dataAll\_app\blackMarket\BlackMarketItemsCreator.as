package dataAll._app.blackMarket
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._player.base.PlayerBaseData;
   import dataAll.arms.define.ArmsType;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.save.ItemsSave;
   
   public class BlackMarketItemsCreator
   {
      
      private static var colorArr:Array = [EquipColor.RED,EquipColor.ORANGE,EquipColor.PURPLE];
      
      private static var levelPro:Array = [1,0.5,0.2];
      
      public function BlackMarketItemsCreator()
      {
         super();
      }
      
      public static function getSaveArr(type0:String, heroLv0:int, numMul0:Number = 1) : Array
      {
         var n:* = undefined;
         var color0:String = null;
         var num0:int = 0;
         var cLv0:int = 0;
         var i:int = 0;
         var fun0:Function = null;
         var lv0:int = 0;
         var s0:ItemsSave = null;
         var arr0:Array = [];
         for(n in colorArr)
         {
            color0 = colorArr[n];
            num0 = getNumByColor(color0) * numMul0;
            cLv0 = getLevelByColor(color0);
            for(i = 0; i < num0; i++)
            {
               fun0 = BlackMarketItemsCreator["getRandom_" + type0];
               if(fun0 is Function)
               {
                  lv0 = heroLv0 + Math.random() * cLv0;
                  if(lv0 > PlayerBaseData.ENEMY_LEVEL)
                  {
                     lv0 = PlayerBaseData.ENEMY_LEVEL;
                  }
                  s0 = fun0(lv0,color0);
                  if(s0 is ItemsSave)
                  {
                     if(!(s0.color == "red" && s0.itemsLevel < 51))
                     {
                        arr0.push(s0);
                     }
                  }
               }
            }
         }
         return arr0;
      }
      
      private static function getRandom_arms(lv0:int, color0:String) : ArmsSave
      {
         var childType0:String = ArrayMethod.getRandomOne(ArmsType.NORMAL_TYPE_ARR);
         if(childType0 == ArmsType.rocket && color0 != "purple" && color0 != "orange" && color0 != "red")
         {
            return null;
         }
         return Gaming.defineGroup.armsCreator.getSave(color0,lv0,childType0,"diff_3");
      }
      
      private static function getRandom_equip(lv0:int, color0:String) : EquipSave
      {
         var childType0:String = ArrayMethod.getRandomOne(EquipType.NORMAL_ARR);
         return Gaming.defineGroup.equipCreator.getSave(color0,lv0,childType0,"diff_3");
      }
      
      private static function getNumByColor(color0:String) : int
      {
         if(color0 == EquipColor.PURPLE)
         {
            return 4;
         }
         if(color0 == EquipColor.ORANGE)
         {
            return 1;
         }
         if(color0 == EquipColor.RED)
         {
            return Math.random() < 0.5 ? 0 : 1;
         }
         return 0;
      }
      
      private static function getLevelByColor(color0:String) : int
      {
         if(color0 == EquipColor.PURPLE)
         {
            return -3;
         }
         if(color0 == EquipColor.ORANGE)
         {
            return -1;
         }
         if(color0 == EquipColor.RED)
         {
            return 0;
         }
         return 5;
      }
   }
}

