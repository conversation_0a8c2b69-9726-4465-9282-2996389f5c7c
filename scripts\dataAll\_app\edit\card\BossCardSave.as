package dataAll._app.edit.card
{
   import com.adobe.serialization.json.JSON2;
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._base.OneSave;
   
   public class BossCardSave extends OneSave
   {
      
      public static var pro_arr:Array = null;
      
      private static const xmlProArr:Array = ["id","n","s","li","dp","sr"];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var id:String = "";
      
      public var n:String = "";
      
      public var o:Object = {};
      
      public var sr:Array = [];
      
      public var lk:int = 0;
      
      private var _xml:XML = null;
      
      public function BossCardSave()
      {
         super();
      }
      
      public function get s() : Number
      {
         return this.CF.getAttribute("s");
      }
      
      public function set s(v0:Number) : void
      {
         this.CF.setAttribute("s",v0);
      }
      
      public function get li() : Number
      {
         return this.CF.getAttribute("li");
      }
      
      public function set li(v0:Number) : void
      {
         this.CF.setAttribute("li",v0);
      }
      
      public function get dp() : Number
      {
         return this.CF.getAttribute("dp");
      }
      
      public function set dp(v0:Number) : void
      {
         this.CF.setAttribute("dp",v0);
      }
      
      public function get ev() : Number
      {
         return this.CF.getAttribute("ev");
      }
      
      public function set ev(v0:Number) : void
      {
         this.CF.setAttribute("ev",v0);
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.o = ClassProperty.copyObj(obj0["o"]);
         var pro0:String = "dpsMul_wavegun";
         var v0:Number = 0.05;
         if(this.o.hasOwnProperty(pro0))
         {
            if(this.o[pro0] > v0)
            {
               this.o[pro0] = v0;
            }
         }
         if(this.n == "OreWorm")
         {
            if(this.o.hasOwnProperty("lottery"))
            {
               if(this.o["lottery"] == 5)
               {
                  this.o["lottery"] = 10;
               }
            }
         }
         BossCardCreator.repairSkill(this);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var json0:String = null;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         var os0:String = xml0.@o;
         if(os0 != "" && os0 != null)
         {
            json0 = TextWay.replaceStr(os0,"\'","\"");
            this.o = JSON2.decode(json0);
         }
         this._xml = xml0;
      }
      
      public function getXMLStr() : String
      {
         var pro0:String = null;
         var j0:String = "<body";
         for each(pro0 in xmlProArr)
         {
            j0 += " " + pro0 + "=\"" + this[pro0] + "\"";
         }
         return j0 + "/>";
      }
      
      public function getVipMust() : int
      {
         if(Boolean(this._xml))
         {
            return int(this._xml.@vip);
         }
         return 0;
      }
      
      public function getOValueArg() : Number
      {
         var n:* = undefined;
         var num0:int = 0;
         var v0:Number = 0;
         for(n in this.o)
         {
            num0++;
            if(this.o[n] is Number)
            {
               v0 += this.o[n];
            }
         }
         if(num0 > 0)
         {
            return v0 / num0;
         }
         return 0;
      }
   }
}

