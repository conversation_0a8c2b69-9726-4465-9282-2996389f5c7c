package dataAll._app.active.define
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class ActiveTaskDefine
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var condition:String = "";
      
      public var noGotoB:Boolean = false;
      
      public function ActiveTaskDefine()
      {
         super();
         this.num = 0;
         this.active = 0;
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function get active() : Number
      {
         return this.CF.getAttribute("active");
      }
      
      public function set active(v0:Number) : void
      {
         this.CF.setAttribute("active",v0);
      }
      
      public function inData_byXML(x0:XML) : void
      {
         ClassProperty.inData_byXMLAt(this,x0,pro_arr);
      }
   }
}

