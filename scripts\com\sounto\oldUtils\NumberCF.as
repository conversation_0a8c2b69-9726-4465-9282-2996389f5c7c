package com.sounto.oldUtils
{
   public class NumberCF
   {
      
      private var varObj:Object;
      
      protected var V:Number = Math.random() / 5 + 0.01;
      
      public function NumberCF()
      {
         super();
      }
      
      public function setAttribute(varName:String, varValue:Number) : *
      {
         var i:* = undefined;
         var tmpObj:Object = new Object();
         tmpObj = {"value":varValue / this.V};
         var tmpObj2:Object = new Object();
         for(i in this.varObj)
         {
            tmpObj2[i] = this.varObj[i];
         }
         tmpObj2[varName] = tmpObj.value;
         tmpObj = null;
         this.varObj = null;
         this.varObj = tmpObj2;
      }
      
      public function getAttribute(varName:String) : Number
      {
         if(this.varObj == null || !this.varObj.hasOwnProperty(varName))
         {
            return Number.NaN;
         }
         var tmpObj:Object = new Object();
         tmpObj.value = this.varObj[varName] * this.V;
         return tmpObj.value;
      }
   }
}

