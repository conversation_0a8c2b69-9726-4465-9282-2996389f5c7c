package com.sounto.hit
{
   import com.common.data.Line2;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class NewHitIO
   {
      
      private static var bakP:Point = new Point();
      
      private static var bakL:Line2 = new Line2();
      
      public function NewHitIO()
      {
         super();
      }
      
      public static function hit(x1:Number, y1:Number, w1:Number, h1:Number, x2:Number, y2:Number, w2:Number, h2:Number) : Boolean
      {
         var bb:Boolean = false;
         var lx:Number = x1 - (x2 + w2);
         var rx:Number = x2 - (x1 + w1);
         var uy:Number = y1 - (y2 + h2);
         var dy:Number = y2 - (y1 + h1);
         if(lx < 0 && rx < 0 && (uy < 0 && dy < 0))
         {
            bb = true;
         }
         return bb;
      }
      
      public static function rectArr_midLine(arr0:Array, x1:Number, y1:Number, w1:Number, h1:Number, panSizeB0:Boolean = false) : Line2
      {
         var n:* = undefined;
         var rect0:Rectangle = null;
         var x2:Number = NaN;
         var y2:Number = NaN;
         var w2:Number = NaN;
         var h2:Number = NaN;
         var lx:Number = NaN;
         var rx:Number = NaN;
         var uy:Number = NaN;
         var dy:Number = NaN;
         var bb:Boolean = false;
         var mx1:Number = NaN;
         var my1:Number = NaN;
         var c1:Number = NaN;
         var mx2:Number = NaN;
         var my2:Number = NaN;
         var c2:Number = NaN;
         for(n in arr0)
         {
            rect0 = arr0[n];
            x2 = rect0.x;
            y2 = rect0.y;
            w2 = rect0.width;
            h2 = rect0.height;
            lx = x1 - (x2 + w2);
            rx = x2 - (x1 + w1);
            uy = y1 - (y2 + h2);
            dy = y2 - (y1 + h1);
            bb = false;
            if(lx < 0 && rx < 0 && (uy < 0 && dy < 0))
            {
               bb = true;
            }
            if(bb)
            {
               mx1 = x1 + w1 / 2;
               my1 = y1 + h1 / 2;
               c1 = Math.sqrt(w1 * w1 + h1 * h1);
               if(c1 <= 0)
               {
                  c1 = 0.1;
               }
               mx2 = x2 + w2 / 2;
               my2 = y2 + h2 / 2;
               c2 = Math.sqrt(w2 * w2 + h2 * h2);
               if(c2 <= 0)
               {
                  c2 = 0.1;
               }
               if(panSizeB0)
               {
                  if(c1 > 200 && c1 > c2 * 2)
                  {
                     bakL.x = mx2;
                     bakL.y = my2;
                  }
                  else if(c2 > 200 && c2 > c1 * 2)
                  {
                     bakL.x = mx1;
                     bakL.y = my1;
                  }
                  else
                  {
                     bakL.x = mx1 + (mx2 - mx1) * (c1 / (c2 + c1));
                     bakL.y = my1 + (my2 - my1) * (c1 / (c2 + c1));
                  }
               }
               else
               {
                  bakL.x = mx1 + (mx2 - mx1) * (c1 / (c2 + c1));
                  bakL.y = my1 + (my2 - my1) * (c1 / (c2 + c1));
               }
               bakL.w = n;
               if(mx1 < mx2)
               {
                  bakL.ra = 0;
               }
               else
               {
                  bakL.ra = Math.PI;
               }
               return bakL;
            }
         }
         return null;
      }
   }
}

