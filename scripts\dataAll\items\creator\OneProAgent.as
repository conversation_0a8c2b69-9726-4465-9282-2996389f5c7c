package dataAll.items.creator
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.edit.card.BossCardRemake;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.skill.define.SkillDefine;
   import dataAll.skill.define.SkillDescrip;
   
   public class OneProAgent
   {
      
      public var type:String = "pro";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var value:Number = 0;
      
      public var max:Number = 0;
      
      public var valueString:String = "";
      
      public var state:String = "random";
      
      public var data:Object = null;
      
      private var proDef:PropertyArrayDefine = null;
      
      private var skillDef:SkillDefine = null;
      
      public var tipString:String = "";
      
      public function OneProAgent()
      {
         super();
      }
      
      public function isProB() : Boolean
      {
         return this.type == OneProAgentType.pro;
      }
      
      public function isSkillB() : Boolean
      {
         return this.type == OneProAgentType.skill;
      }
      
      public function inPropertyArrayDefineName(name0:String, value0:Number) : void
      {
         this.name = name0;
         this.type = OneProAgentType.pro;
         this.value = value0;
         var d0:PropertyArrayDefine = Gaming.defineGroup.getPropertyArrayDefine(name0);
         if(Boolean(d0))
         {
            this.proDef = d0;
            this.cnName = d0.cnName;
            this.valueString = d0.getValueString(value0);
         }
      }
      
      public function inSkillName(name0:String) : void
      {
         this.name = name0;
         this.type = OneProAgentType.skill;
         var d0:SkillDefine = Gaming.defineGroup.skill.getDefine(name0);
         if(Boolean(d0))
         {
            this.skillDef = d0;
            this.cnName = d0.cnName;
            this.valueString = "";
         }
      }
      
      public function overMaxB() : Boolean
      {
         if(this.isProB())
         {
            if(this.value >= this.max)
            {
               return true;
            }
            if(NumberMethod.toFixed(this.value,3) >= this.max)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getBcardCn() : String
      {
         if(Boolean(this.skillDef))
         {
            return "技能·" + this.cnName;
         }
         return this.cnName;
      }
      
      public function bcardFlesh() : void
      {
         if(this.value >= this.max)
         {
            if(this.state == OneProAgentState.up)
            {
               this.state = OneProAgentState.lock;
            }
         }
      }
      
      public function bcardClick() : void
      {
         var arr0:Array = OneProAgentState.skillArr;
         if(this.type == OneProAgentType.pro && this.overMaxB() == false)
         {
            arr0 = OneProAgentState.proArr;
         }
         this.state = ArrayMethod.getNextOne(this.state,arr0);
      }
      
      public function getBcardTip() : String
      {
         var upValue0:Number = NaN;
         var s0:String = "";
         if(Boolean(this.skillDef))
         {
            if(this.state == OneProAgentState.random)
            {
               s0 += "<orange 【随机】/>重造后将随机技能。";
            }
            else if(this.state == OneProAgentState.lock)
            {
               s0 += "<blue 【锁住】/>锁住技能，使其不被重造。";
            }
            s0 += "\n\n" + SkillDescrip.getSkillArrGather([this.name],"#FFFF00");
         }
         else if(Boolean(this.proDef))
         {
            upValue0 = BossCardRemake.getUpValue(this.name,this.value);
            if(this.state == OneProAgentState.random)
            {
               s0 += "<orange 【随机】/>重造后将随机属性和属性值。";
            }
            else if(this.state == OneProAgentState.lock)
            {
               s0 += "<blue 【已锁】/>锁住属性和属性值，使其不被重造。";
            }
            else if(this.state == OneProAgentState.up)
            {
               s0 += "<green 【提升】/>重造后，该属性保持不变，同时属性值将提升<yellow " + this.proDef.getValueString(upValue0,false) + "/>，直到到达上限。";
            }
            s0 += "\n\n上限值：<yellow " + this.proDef.getValueString(this.max,false) + "/>";
            if(this.overMaxB())
            {
               s0 += "<purple (已到)/>";
            }
         }
         return s0;
      }
   }
}

