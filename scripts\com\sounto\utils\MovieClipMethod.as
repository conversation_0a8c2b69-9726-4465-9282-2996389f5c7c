package com.sounto.utils
{
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   
   public class MovieClipMethod
   {
      
      public function MovieClipMethod()
      {
         super();
      }
      
      public static function mcGoto(mc0:MovieClip, label0:*) : void
      {
         var bb0:Boolean = false;
         if(<PERSON><PERSON><PERSON>(mc0))
         {
            bb0 = false;
            if(label0 is String)
            {
               if(label0 != "")
               {
                  bb0 = true;
               }
            }
            else if(label0 is int)
            {
               bb0 = true;
            }
            mc0.visible = bb0;
            mc0.gotoAndStop(bb0 ? label0 : 1);
         }
      }
      
      public static function stopAllChild(sp0:DisplayObjectContainer) : void
      {
         var mc0:MovieClip = null;
         var len0:int = sp0.numChildren;
         for(var i:int = 0; i < len0; i++)
         {
            mc0 = sp0.getChildAt(i) as MovieClip;
            if(Boolean(mc0))
            {
               if(mc0.totalFrames > 1)
               {
                  mc0.stop();
               }
            }
         }
      }
   }
}

