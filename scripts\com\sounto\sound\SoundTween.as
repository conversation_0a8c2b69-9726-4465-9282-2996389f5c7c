package com.sounto.sound
{
   import flash.display.MovieClip;
   import flash.events.Event;
   
   public class SoundTween
   {
      
      private static var mc:MovieClip = new MovieClip();
      
      private static var addEventB:Boolean = false;
      
      private static var arr:Array = [];
      
      public function SoundTween()
      {
         super();
      }
      
      public static function to(sc0:SoundData, _time:Number = 2, _end:Number = 0, _first:Number = -1, completeStopB0:Boolean = true) : void
      {
         var one0:SoundTweenOne = getTween(sc0);
         if(!(one0 is SoundTweenOne))
         {
            one0 = new SoundTweenOne();
            one0.soundData = sc0;
            arr.push(one0);
         }
         one0.start(_time,_end,_first,completeStopB0);
         if(!addEventB)
         {
            mc.addEventListener(Event.ENTER_FRAME,FTimer);
            addEventB = true;
         }
      }
      
      private static function getTween(sc0:SoundData) : SoundTweenOne
      {
         var one0:SoundTweenOne = null;
         var arr_len0:int = int(arr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            one0 = arr[i];
            if(one0.soundData == sc0)
            {
               return one0;
            }
         }
         return null;
      }
      
      private static function FTimer(e:Event) : void
      {
         var one0:SoundTweenOne = null;
         var arr_len0:int = int(arr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            one0 = arr[i];
            one0.FTimer();
            if(one0.state == "over")
            {
               arr.splice(i,1);
               i--;
               arr_len0--;
            }
         }
      }
   }
}

