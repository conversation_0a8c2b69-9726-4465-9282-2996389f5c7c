package dataAll.things.save
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.parts.PartsCreator;
   import dataAll._app.parts.define.PartsConst;
   import dataAll.items.save.ItemsSave;
   import dataAll.things.define.ThingsDefine;
   
   public class ThingsSave extends ItemsSave
   {
      
      public static var pro_arr:Array = [];
      
      public static var mePro_arr:Array = null;
      
      private var thingsDefine:ThingsDefine = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var autoUseB:Boolean = false;
      
      public function ThingsSave()
      {
         super();
         this.nowNum = 1;
      }
      
      public function set nowNum(v0:Number) : void
      {
         this.CF.setAttribute("nowNum",v0);
      }
      
      public function get nowNum() : Number
      {
         return this.CF.getAttribute("nowNum");
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         var xx:int = 0;
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
         name = PartsCreator.getSwapName(name);
         var d0:ThingsDefine = Gaming.defineGroup.things.getDefine(name);
         if(Boolean(d0))
         {
            this.thingsDefine = d0;
            itemsType = d0.father;
         }
         else
         {
            xx = 0;
         }
      }
      
      public function inData_byDefine(d0:ThingsDefine) : void
      {
         this.thingsDefine = d0;
         name = d0.name;
         itemsLevel = d0.itemsLevel;
         cnName = d0.cnName;
         itemsType = d0.father;
      }
      
      public function getBaseLabel() : String
      {
         return this.thingsDefine.baseLabel;
      }
      
      override public function getCnName() : String
      {
         if(cnName == "")
         {
            return this.getDefine().cnName;
         }
         return cnName;
      }
      
      public function getPartsColor() : String
      {
         return PartsConst.getIconColor(this.thingsDefine,getTrueLevel());
      }
      
      public function getDefine() : ThingsDefine
      {
         return this.thingsDefine;
      }
      
      public function overNum(v0:Number) : void
      {
         this.nowNum += v0;
      }
      
      override public function getChildType() : String
      {
         return this.getBaseLabel();
      }
      
      public function isPartsSpecialB() : Boolean
      {
         return this.getDefine().isPartsSpecialB();
      }
      
      public function isPartsRareB() : Boolean
      {
         return this.getDefine().isPartsRareB();
      }
      
      public function isPartsNormalB() : Boolean
      {
         return this.getDefine().isPartsNormalB();
      }
      
      public function getPartsType() : String
      {
         return this.getDefine().objType;
      }
      
      override public function isImportantB() : Boolean
      {
         if(super.isImportantB())
         {
            return true;
         }
         if(this.getDefine().isPartsB())
         {
            if(!this.isPartsNormalB())
            {
               return true;
            }
            if(getTrueLevel() >= 66)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getPartsAlertText() : String
      {
         var d0:ThingsDefine = this.getDefine();
         var str0:String = "";
         str0 += ComMethod.color(this.nowNum + "个","#00FF00");
         str0 += d0.cnName;
         return str0 + ComMethod.color("(" + getTrueLevel() + "级)","#00FFFF");
      }
      
      public function clone() : ThingsSave
      {
         var s0:ThingsSave = new ThingsSave();
         s0.inData_byObj(this);
         return s0;
      }
      
      public function getPartsNextDefine() : ThingsDefine
      {
         if(!itemsType == "parts")
         {
            INIT.showError("getNextDefine()方法不可用于：" + name);
         }
         return Gaming.defineGroup.things.getDefine(this.getPartsNextName());
      }
      
      public function getPartsNextName() : String
      {
         return this.getBaseLabel() + "_" + this.getPartsNextLv();
      }
      
      public function getPartsNextLv() : int
      {
         if(this.isPartsNormalB())
         {
            return getTrueLevel() + PartsConst.cLv;
         }
         return getTrueLevel() + 1;
      }
   }
}

