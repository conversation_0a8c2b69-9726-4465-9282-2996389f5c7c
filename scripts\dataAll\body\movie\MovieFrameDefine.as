package dataAll.body.movie
{
   import com.sounto.math.IDRect;
   
   public class MovieFrameDefine
   {
      
      public var motArr:Array = [];
      
      public var motObj:Object = new Object();
      
      public var soundLabelArr:Array = null;
      
      public var specialPointObj:Object = null;
      
      public var trueSpecialPointObj:Object = null;
      
      public var hurtRectArr:Array = [];
      
      public var attackRectArr:Array = [];
      
      public var attackMergeRect:IDRect = null;
      
      public function MovieFrameDefine()
      {
         super();
      }
      
      public static function labelToSoundArr(label0:String) : Array
      {
         var l0:String = null;
         var f0:int = 0;
         var str0:String = null;
         var num0:int = 0;
         var i:int = 0;
         var xxx0:int = 0;
         if(label0 == "")
         {
            return null;
         }
         var arr0:Array = label0.split(",");
         var newArr0:Array = arr0;
         if(label0.indexOf(":") > 0)
         {
            if(label0.indexOf("f:") != 0)
            {
               newArr0 = [];
               for each(l0 in arr0)
               {
                  if(l0.indexOf(":") > 0)
                  {
                     f0 = int(label0.indexOf(":"));
                     str0 = l0.substr(0,f0);
                     num0 = int(l0.substr(f0 + 1));
                     for(i = 1; i <= num0; i++)
                     {
                        newArr0.push(str0 + i);
                     }
                  }
                  else
                  {
                     newArr0.push(l0);
                  }
               }
            }
            else
            {
               xxx0 = 0;
            }
         }
         return newArr0;
      }
   }
}

