package dataAll.level.define.mapRect
{
   import com.sounto.hit.spiderWeb.SpiderWebNode;
   import flash.display.MovieClip;
   import flash.geom.Rectangle;
   
   public class MapRect extends Rectangle
   {
      
      public static const rHitMainSpace:String = "rHitMainSpace";
      
      public static const rEnemySpace:String = "rEnemySpace";
      
      public static const rEnemySpace2:String = "rEnemySpace2";
      
      public static const r123:String = "r123";
      
      public static const rFlesh:String = "rFlesh";
      
      public static const rLeftSpider:String = "rLeftSpider";
      
      public static const farBirthRanSpiderPartol:String = "farBirthRanSpiderPartol";
      
      public static const enemyBirthArr:Array = ["r1","r2","r3"];
      
      private static const weBirthArr:Array = ["r_birth"];
      
      public static const r_over:* = "r_over";
      
      public static const r_birth:* = "r_birth";
      
      public static const HIDE:String = "hide";
      
      public static const hideRan:String = "hideRan";
      
      public var id:String = "";
      
      public var label:String = "";
      
      public function MapRect()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         this.id = String(xml0.@id);
         this.label = String(xml0.@label);
         var arr0:Array = String(xml0).split(",");
         x = Number(arr0[0]);
         y = Number(arr0[1]);
         width = Number(arr0[2]);
         height = Number(arr0[3]);
      }
      
      public function inDataBySp(mc0:MovieClip, labelNumObj0:Object) : void
      {
         var num0:int = 0;
         var id0:String = mc0.name;
         var label0:String = mc0.currentLabel;
         if(Boolean(label0) && label0 != "")
         {
            if(!labelNumObj0.hasOwnProperty(label0))
            {
               labelNumObj0[label0] = 0;
            }
            ++labelNumObj0[label0];
            num0 = int(labelNumObj0[label0]);
            if(label0 == "birth" || label0 == "over")
            {
               id0 = "r_" + label0;
            }
            else if(label0 == "enemy")
            {
               id0 = "r" + num0;
            }
            else if(label0 == HIDE)
            {
               id0 = HIDE + num0;
            }
         }
         this.id = id0;
         this.label = label0;
         x = mc0.x;
         y = mc0.y;
         width = mc0.width;
         height = mc0.height;
         if(this.id == r_over)
         {
            height += 50;
         }
      }
      
      public function inData_byRect(r0:Rectangle) : void
      {
         x = r0.x;
         y = r0.y;
         width = r0.width;
         height = r0.height;
      }
      
      public function inData_bySquare(x0:int, y0:int, range0:int) : void
      {
         x = x0 - range0;
         y = y0 - range0;
         width = range0 * 2;
         height = range0 * 2;
      }
      
      public function inSpiderWebNode(s0:SpiderWebNode) : void
      {
         x = s0.x - width / 2;
         y = s0.y - height;
      }
      
      public function inMid(x0:Number, y0:Number) : void
      {
         x = x0 - width / 2;
         y = y0 - height / 2;
      }
      
      public function isEnemyBirthB() : Boolean
      {
         return enemyBirthArr.indexOf(this.id) >= 0;
      }
      
      public function isWeBirthB() : Boolean
      {
         return weBirthArr.indexOf(this.id) >= 0;
      }
   }
}

