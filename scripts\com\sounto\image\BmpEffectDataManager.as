package com.sounto.image
{
   import com.sounto.math.Maths;
   import com.sounto.net.SWFLoaderManager;
   import dataAll.image.ImageUrlDefine;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.filters.BlurFilter;
   import flash.geom.ColorTransform;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import gameAll.image.GameSprite;
   
   public class BmpEffectDataManager
   {
      
      private static const overPanFatherArr:Array = ["boomEffect"];
      
      private static const overPanLabelArr:Array = ["fitHit","yellow_motion","immune_equip"];
      
      private static var zeroEF:BmpEffectData = new BmpEffectData();
      
      public static const zeroEFUrl:String = "boomEffect/zero";
      
      private static const _xs:String = "/";
      
      private var overMinGap:Number = 20;
      
      private var overMinFrame:Number = 3;
      
      public var swfM:SWFLoaderManager = null;
      
      public var arr:Array = [];
      
      public var obj:Object = new Object();
      
      public var normalBmp:Bitmap = new Bitmap();
      
      public var effectBmp:Bitmap = new Bitmap();
      
      public var filterBmp:Bitmap = new Bitmap();
      
      private var zeroBmpData:BitmapData;
      
      private var normalBmpData:BitmapData;
      
      private var effectBmpData:BitmapData;
      
      private var filterBmpData:BitmapData;
      
      private var normalArr:Array = [];
      
      private var effectArr:Array = [];
      
      private var filterArr:Array = [];
      
      private var overObj:Object = {};
      
      private var normalFillRect:Rectangle = new Rectangle();
      
      private var effectFillRect:Rectangle = new Rectangle();
      
      private var filterFillRect:Rectangle = new Rectangle();
      
      private var gameWorld_x:int = 0;
      
      private var gameWorld_y:int = 0;
      
      private var before_gameWorld_x:int = 0;
      
      private var before_gameWorld_y:int = 0;
      
      private var tempRect:Rectangle = new Rectangle();
      
      private var tempPoint:Point = new Point();
      
      private var screenRect:Rectangle = new Rectangle();
      
      private var zeroPoint:Point = new Point();
      
      private var filterColorF:ColorTransform = new ColorTransform(1,1,1,1,0,0,0,-50);
      
      private var filterBlurFilter:BlurFilter = new BlurFilter(5,5);
      
      public var filterTailB:Boolean = true;
      
      private var filterB:Boolean = true;
      
      private var gunFire:String = "gunFire";
      
      private var bladeHitEffect:String = "bladeHitEffect";
      
      private var blood:String = "blood";
      
      private var sf:String = "f";
      
      public function BmpEffectDataManager()
      {
         super();
      }
      
      private static function initFillRect(fillRect0:Rectangle) : void
      {
         fillRect0.x = 100000;
         fillRect0.y = 100000;
         fillRect0.width = 0;
         fillRect0.height = 0;
      }
      
      private static function countFillRect(fillRect0:Rectangle, p0:Point, nowRect0:Rectangle) : void
      {
         if(fillRect0.x > p0.x)
         {
            fillRect0.width += fillRect0.x - p0.x;
            fillRect0.x = p0.x;
         }
         if(fillRect0.y > p0.y)
         {
            fillRect0.height += fillRect0.y - p0.y;
            fillRect0.y = p0.y;
         }
         if(fillRect0.width < p0.x + nowRect0.width)
         {
            fillRect0.width = p0.x + nowRect0.width - fillRect0.x;
         }
         if(fillRect0.height < p0.y + nowRect0.height - fillRect0.y)
         {
            fillRect0.height = p0.y + nowRect0.height - fillRect0.y;
         }
      }
      
      public function initStageSize(w0:int, h0:int, gs0:GameSprite) : *
      {
         this.clearBitmap();
         if(!this.normalBmpData)
         {
            this.normalBmpData = new BitmapData(w0,h0,true,0);
            this.effectBmpData = new BitmapData(w0,h0,true,0);
            this.filterBmpData = new BitmapData(w0,h0,true,0);
            this.normalBmp.bitmapData = this.normalBmpData;
            this.effectBmp.bitmapData = this.effectBmpData;
            this.effectBmp.blendMode = "add";
            this.filterBmp.bitmapData = this.filterBmpData;
            this.filterBmp.blendMode = "add";
         }
         this.screenRect.width = w0;
         this.screenRect.height = h0;
         gs0.L_regularEffect.addChild(this.filterBmp);
         gs0.L_regularEffect.addChild(this.effectBmp);
         gs0.L_regularEffect.addChild(this.normalBmp);
      }
      
      public function fleshScreen() : void
      {
         var gs0:GameSprite = Gaming.gameSprite;
         var w0:int = gs0.screenWidth;
         var h0:int = gs0.screenHeight;
         this.initStageSize(w0,h0,gs0);
      }
      
      public function fleshScreenNewSize() : void
      {
         var gs0:GameSprite = Gaming.gameSprite;
         var w0:int = gs0.screenWidth;
         var h0:int = gs0.screenHeight;
         if(this.screenRect.width != w0)
         {
            this.fleshScreen();
         }
         else
         {
            this.screenRect.width = w0;
            this.screenRect.height = h0;
         }
      }
      
      public function getNormalBmpData() : BitmapData
      {
         return this.normalBmpData;
      }
      
      public function clearBitmap() : void
      {
         if(Boolean(this.normalBmpData))
         {
            this.normalBmpData.dispose();
            this.effectBmpData.dispose();
            this.filterBmpData.dispose();
            this.normalBmpData = null;
            this.effectBmpData = null;
            this.filterBmpData = null;
         }
      }
      
      public function addResourceListKeep(list0:Array, raNum0:int = 1) : void
      {
         this.addResourceList(list0,raNum0,true,true);
      }
      
      public function addImgUrlDefineName(name0:String) : void
      {
         this.addImgUrlDefineKeep(Gaming.defineGroup.imageUrl.getDefine(name0));
      }
      
      public function addImgUrlDefineKeep(d0:ImageUrlDefine) : void
      {
         this.addResourceListKeep(d0.getUrlArr(),d0.raNum);
         if(d0.sm != "")
         {
            this.addImgUrlDefineName(d0.sm);
         }
      }
      
      public function addImgUrlDefineKeepArr(arr0:Array) : void
      {
         var d0:ImageUrlDefine = null;
         for each(d0 in arr0)
         {
            this.addImgUrlDefineKeep(d0);
         }
      }
      
      public function addImgUrlDefineNameArrKeep(arr0:Array) : void
      {
         var name0:String = null;
         var d0:ImageUrlDefine = null;
         for each(name0 in arr0)
         {
            d0 = Gaming.defineGroup.imageUrl.getDefine(name0);
            this.addImgUrlDefineKeep(d0);
         }
      }
      
      public function addResourceUrl(url0:String, raNum0:int = 1, throwB0:Boolean = true, keepB0:Boolean = false) : *
      {
         var mc0:MovieClip = null;
         var index0:int = int(url0.indexOf("/"));
         var s0:String = url0.substring(0,index0);
         var n0:String = url0.substring(index0 + 1);
         if(!this.getBmp(s0,n0))
         {
            mc0 = this.swfM.getResource(s0,n0);
            if(mc0 is MovieClip)
            {
               this.addResource(mc0,s0,n0,raNum0,keepB0);
            }
         }
      }
      
      public function addResourceList(list0:Array, raNum0:int = 1, throwB0:Boolean = true, keepB0:Boolean = false) : void
      {
         var url0:String = null;
         var arr_len0:int = int(list0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            url0 = list0[n];
            this.addResourceUrl(url0,raNum0,throwB0,keepB0);
         }
      }
      
      public function addResource(mc0:MovieClip, father0:String, label0:String, raNum:int = 1, keepB0:Boolean = false) : *
      {
         var bmp0:BmpEffectData = new BmpEffectData();
         bmp0.Switch(mc0,raNum,keepB0);
         bmp0.father = father0;
         bmp0.label = label0;
         if(overPanFatherArr.indexOf(father0) >= 0)
         {
            bmp0.overPanB = true;
         }
         if(overPanLabelArr.indexOf(label0) >= 0)
         {
            bmp0.overPanB = true;
         }
         if(zeroEFUrl == father0 + _xs + label0)
         {
            if(!zeroEF.bmpArr)
            {
               zeroEF = bmp0;
            }
         }
         this.arr.push(bmp0);
         if(!this.obj.hasOwnProperty(father0))
         {
            this.obj[father0] = new Object();
         }
         this.obj[father0][label0] = this.arr.length - 1;
      }
      
      public function fleshObj() : void
      {
         var bmp0:BmpEffectData = null;
         this.obj = new Object();
         var arr_len0:int = int(this.arr.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            bmp0 = this.arr[n];
            if(!this.obj.hasOwnProperty(bmp0.father))
            {
               this.obj[bmp0.father] = new Object();
            }
            this.obj[bmp0.father][bmp0.label] = n;
         }
      }
      
      public function clearResourceByNameArray(arr0:Array, ingnoreKeepB0:Boolean = false) : void
      {
         var name0:String = null;
         var bmp0:BmpEffectData = null;
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            name0 = arr0[n];
            name0 = name0.split(",")[0];
            bmp0 = this.getBmpFull(name0);
            if(Boolean(bmp0))
            {
               if(ingnoreKeepB0 || !bmp0.getKeepB())
               {
                  this.clearBmpEffectData(bmp0);
                  this.obj[bmp0.father][bmp0.label] = null;
               }
            }
         }
      }
      
      private function clearBmpEffectData(bmp0:BmpEffectData) : void
      {
         var i:* = undefined;
         var bmp1:BitmapData = null;
         var arr0:Array = bmp0.bmpArr;
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            for(i in arr0[n])
            {
               bmp1 = arr0[n][i];
               bmp1.dispose();
            }
         }
      }
      
      public function clearAll() : void
      {
         this.normalArr.length = 0;
         this.effectArr.length = 0;
         this.filterArr.length = 0;
         this.normalBmpData.fillRect(this.screenRect,0);
         this.effectBmpData.fillRect(this.screenRect,0);
         this.filterBmpData.fillRect(this.screenRect,0);
      }
      
      public function getBmp(father0:String, label0:String) : BmpEffectData
      {
         var obj2:Object = null;
         if(this.obj.hasOwnProperty(father0))
         {
            obj2 = this.obj[father0];
            if(obj2.hasOwnProperty(label0))
            {
               return this.arr[obj2[label0]];
            }
         }
         return null;
      }
      
      public function getBmpFull(url0:String) : BmpEffectData
      {
         var index0:int = int(url0.indexOf("/"));
         var s0:String = url0.substring(0,index0);
         var n0:String = url0.substring(index0 + 1);
         return this.getBmp(s0,n0);
      }
      
      public function addDefine(d0:ImageUrlDefine, x0:int, y0:int, ra0:Number, con0:String = "") : BmpEffectData
      {
         if(con0 == "")
         {
            con0 = d0.con;
         }
         var da0:BmpEffectData = this.addEffectFullName(d0.getRandomUrl(),x0,y0,ra0,con0,d0.bottomLayerB);
         if(Boolean(da0))
         {
            da0.setDieType(d0.imgDieType);
         }
         return da0;
      }
      
      public function addEffect(father0:String, label0:String, x0:int, y0:int, ra0:Number, con0:String = "", bottomLayerB:Boolean = false) : BmpEffectData
      {
         var bmp1:BmpEffectData = null;
         var haveB0:Boolean = false;
         if(father0 == this.gunFire && label0 == this.sf)
         {
            label0 += int(1 + Math.random() * 2);
         }
         if(father0 == this.bladeHitEffect && label0 == this.blood)
         {
            con0 = BmpEffectCon.normal;
         }
         var bmp0:BmpEffectData = this.getBmp(father0,label0);
         if(Boolean(bmp0))
         {
            if(bmp0.overPanB)
            {
               haveB0 = this.overPan(bmp0,x0,y0,ra0);
               if(!haveB0)
               {
                  return zeroEF;
               }
            }
            bmp1 = bmp0.copy();
            bmp1.x = x0;
            bmp1.y = y0;
            bmp1.rotation = ra0 * 180 / Math.PI;
            bmp1.showFirst();
            if(con0 == BmpEffectCon.normal)
            {
               if(bottomLayerB)
               {
                  this.normalArr.unshift(bmp1);
               }
               else
               {
                  this.normalArr.push(bmp1);
               }
            }
            else if(con0 == BmpEffectCon.add)
            {
               if(bottomLayerB)
               {
                  this.effectArr.unshift(bmp1);
               }
               else
               {
                  this.effectArr.push(bmp1);
               }
            }
            else if(con0 == BmpEffectCon.filter)
            {
               if(bottomLayerB)
               {
                  this.filterArr.unshift(bmp1);
               }
               else
               {
                  this.filterArr.push(bmp1);
               }
            }
            if(bmp0.overPanB)
            {
               this.addOver(bmp1);
            }
            return bmp1;
         }
         INIT.showError("没找到BmpEffectData:" + father0 + "/" + label0);
         return null;
      }
      
      public function addEffectFullName(fullName0:String, x0:int, y0:int, ra0:Number, con0:String = "", bottomLayerB:Boolean = false) : BmpEffectData
      {
         var index0:int = int(fullName0.indexOf("/"));
         var s0:String = fullName0.substring(0,index0);
         var n0:String = fullName0.substring(index0 + 1);
         return this.addEffect(s0,n0,x0,y0,ra0,con0,bottomLayerB);
      }
      
      private function addOver(bmp0:BmpEffectData) : void
      {
         var url0:String = bmp0.father + _xs + bmp0.label;
         var earr0:Array = this.overObj[url0];
         if(!earr0)
         {
            earr0 = [];
            this.overObj[url0] = earr0;
         }
         earr0.push(bmp0);
      }
      
      private function overPan(bmp0:BmpEffectData, x0:int, y0:int, ra0:Number) : Boolean
      {
         var b1:BmpEffectData = null;
         var c0:Number = NaN;
         var url0:String = bmp0.father + _xs + bmp0.label;
         var earr0:Array = this.overObj[url0];
         if(Boolean(earr0))
         {
            for each(b1 in earr0)
            {
               if(b1.currentFrame <= this.overMinFrame)
               {
                  c0 = Maths.Long(b1.x - x0,b1.y - y0);
                  if(c0 < this.overMinGap)
                  {
                     return false;
                  }
               }
            }
         }
         return true;
      }
      
      private function copyFlesh(arr0:Array, data0:BitmapData, fillRect0:Rectangle) : Array
      {
         var bmp0:BmpEffectData = null;
         var delB0:Boolean = false;
         var nowRect0:Rectangle = null;
         var copyB0:Boolean = false;
         var dw0:Number = NaN;
         var dh0:Number = NaN;
         if(fillRect0.width > 0 && fillRect0.height > 0)
         {
            data0.fillRect(fillRect0,0);
         }
         initFillRect(fillRect0);
         var p0:Point = this.tempPoint;
         p0.x = 0;
         p0.y = 0;
         var arr1:Array = [];
         var index1:int = 0;
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            bmp0 = arr0[n];
            delB0 = bmp0.canDelB();
            if(!delB0)
            {
               if(bmp0.visible)
               {
                  bmp0.showFirst();
                  nowRect0 = bmp0.nowRect;
                  p0.x = bmp0.x + nowRect0.x + this.gameWorld_x;
                  p0.y = bmp0.y + nowRect0.y + this.gameWorld_y;
                  this.tempRect.width = bmp0.nowBmp.width;
                  this.tempRect.height = bmp0.nowBmp.height;
                  copyB0 = true;
                  dw0 = data0.width;
                  dh0 = data0.height;
                  if(p0.x + this.tempRect.width < 0)
                  {
                     copyB0 = false;
                  }
                  else if(p0.x > dw0)
                  {
                     copyB0 = false;
                  }
                  else if(p0.y + this.tempRect.height < 0)
                  {
                     copyB0 = false;
                  }
                  else if(p0.y > dh0)
                  {
                     copyB0 = false;
                  }
                  if(copyB0)
                  {
                     data0.copyPixels(bmp0.nowBmp,this.tempRect,p0,null,null,true);
                  }
                  countFillRect(fillRect0,p0,nowRect0);
               }
               arr1[index1] = bmp0;
               if(bmp0.overPanB)
               {
                  this.addOver(bmp0);
               }
               index1++;
            }
         }
         return arr1;
      }
      
      private function filterCopyFlesh(arr0:Array, data0:BitmapData, fillRect0:Rectangle) : Array
      {
         var bmp0:BmpEffectData = null;
         var delB0:Boolean = false;
         var nowRect0:Rectangle = null;
         var copyB0:Boolean = false;
         var dw0:Number = NaN;
         var dh0:Number = NaN;
         var p0:Point = this.tempPoint;
         p0.x = 0;
         p0.y = 0;
         data0.scroll(this.gameWorld_x - this.before_gameWorld_x,this.gameWorld_y - this.before_gameWorld_y);
         data0.colorTransform(fillRect0,this.filterColorF);
         var arr1:Array = [];
         var index1:int = 0;
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            bmp0 = arr0[n];
            delB0 = bmp0.canDelB();
            if(!delB0)
            {
               if(bmp0.visible)
               {
                  bmp0.showFirst();
                  nowRect0 = bmp0.nowRect;
                  p0.x = bmp0.x + nowRect0.x + this.gameWorld_x;
                  p0.y = bmp0.y + nowRect0.y + this.gameWorld_y;
                  this.tempRect.width = bmp0.nowBmp.width;
                  this.tempRect.height = bmp0.nowBmp.height;
                  copyB0 = true;
                  dw0 = data0.width;
                  dh0 = data0.height;
                  if(p0.x + this.tempRect.width < 0)
                  {
                     copyB0 = false;
                  }
                  else if(p0.x > dw0)
                  {
                     copyB0 = false;
                  }
                  else if(p0.y + this.tempRect.height < 0)
                  {
                     copyB0 = false;
                  }
                  else if(p0.y > dh0)
                  {
                     copyB0 = false;
                  }
                  if(copyB0)
                  {
                     data0.copyPixels(bmp0.nowBmp,this.tempRect,p0,null,null,true);
                  }
               }
               arr1[index1] = bmp0;
               if(bmp0.overPanB)
               {
                  this.addOver(bmp0);
               }
               index1++;
            }
         }
         p0.x = 0;
         p0.y = 0;
         this.before_gameWorld_x = this.gameWorld_x;
         this.before_gameWorld_y = this.gameWorld_y;
         return arr1;
      }
      
      private function playFlesh(arr0:Array) : *
      {
         var bmp0:BmpEffectData = null;
         var timeStopB0:Boolean = Gaming.targetInput.timeStopB;
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            bmp0 = arr0[n];
            bmp0.FTimer(timeStopB0);
         }
      }
      
      public function dataTimer(gameWorldX:int, gameWorldY:int) : *
      {
         this.gameWorld_x = gameWorldX;
         this.gameWorld_y = gameWorldY;
         this.playFlesh(this.normalArr);
         this.playFlesh(this.effectArr);
         this.playFlesh(this.filterArr);
      }
      
      public function imageTimer(timeStopB0:Boolean = false) : void
      {
         this.overObj = {};
         this.normalArr = this.copyFlesh(this.normalArr,this.normalBmpData,this.normalFillRect);
         this.effectArr = this.copyFlesh(this.effectArr,this.effectBmpData,this.effectFillRect);
         var fB0:Boolean = this.filterTailB && timeStopB0 == false;
         if(fB0 != this.filterB)
         {
            this.filterB = fB0;
            if(fB0 == false)
            {
               this.filterBmpData.fillRect(this.filterBmpData.rect,0);
            }
         }
         if(fB0)
         {
            this.filterArr = this.filterCopyFlesh(this.filterArr,this.filterBmpData,this.screenRect);
         }
         else
         {
            this.filterArr = this.copyFlesh(this.filterArr,this.filterBmpData,this.filterFillRect);
         }
      }
   }
}

