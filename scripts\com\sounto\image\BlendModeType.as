package com.sounto.image
{
   import flash.display.BlendMode;
   
   public class BlendModeType
   {
      
      public static var arr:Array = [BlendMode.NORMAL,BlendMode.ADD,BlendMode.ALPHA,BlendMode.DARKEN,BlendMode.DIFFERENCE,BlendMode.ERASE,BlendMode.HARDLIGHT,BlendMode.INVERT,BlendMode.LAYER,BlendMode.LIGHTEN,BlendMode.MULTIPLY,BlendMode.OVERLAY,BlendMode.SCREEN,BlendMode.SHADER,BlendMode.SUBTRACT];
      
      public static var cnArr:Array = ["正常","添加","蒙版","变暗","差值","擦除","强光","反转","图层","变亮","乘法","叠加","滤色","着色器","减去"];
      
      public function BlendModeType()
      {
         super();
      }
   }
}

