package dataAll.gift.anniver
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll._data.ConstantDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class AnniverOnlineSave
   {
      
      public static var pro_arr:Array = [];
      
      public static var GIFT_NAME:String = "anniverOnline";
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var getObj:Object = {};
      
      public var dailyB:Boolean = false;
      
      public function AnniverOnlineSave()
      {
         super();
         this.num = 0;
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.getObj = ClassProperty.copyObj(obj0["getObj"]);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.setDayGetB(false);
         this.dailyB = false;
      }
      
      public function getLastGetB() : Boolean
      {
         var gArr0:Array = this.getDefineGiftArr();
         var g0:GiftAddDefineGroup = gArr0[gArr0.length - 1];
         return this.getGiftGetB(String(g0.mustLevel));
      }
      
      public function getGiftGetB(name0:String) : Boolean
      {
         return this.getObj[name0];
      }
      
      public function setGiftGetB(name0:String, bb0:Boolean) : void
      {
         this.getObj[name0] = bb0;
      }
      
      public function getDayGetB() : Boolean
      {
         return this.getGiftGetB("1");
      }
      
      public function setDayGetB(bb0:Boolean) : void
      {
         return this.setGiftGetB("1",bb0);
      }
      
      public function dailyPan(onlineMin0:Number) : void
      {
         var min0:int = 0;
         if(!this.dailyB)
         {
            min0 = onlineMin0;
            if(min0 >= this.getMustMin())
            {
               this.dailyB = true;
               ++this.num;
            }
         }
      }
      
      public function getMustMin() : int
      {
         return 30;
      }
      
      public function getGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var gArr0:Array = this.getGiftArr();
         for each(g0 in gArr0)
         {
            gift0.merge(g0);
         }
         return gift0;
      }
      
      public function getFillNum(timeStr0:String) : int
      {
         var day0:int = 0;
         if(this.num > 40)
         {
            return 0;
         }
         day0 = StringDate.compareDateByStr(ConstantDefine.anniver.signStart,timeStr0) + 1;
         if(!this.dailyB)
         {
            day0 -= 1;
         }
         return day0 - this.num;
      }
      
      public function fill(num0:int) : void
      {
         this.num += num0;
      }
      
      public function getGiftBtnTip() : String
      {
         var g0:GiftAddDefineGroup = null;
         var str0:String = "";
         var gArr0:Array = this.getGiftArr();
         if(gArr0.length == 0)
         {
            str0 = "礼包已领取";
         }
         else
         {
            for each(g0 in gArr0)
            {
               str0 += "\n";
               if(g0.cnName == "")
               {
                  str0 += "<blue " + g0.mustLevel + "天礼包/>";
               }
               else
               {
                  str0 += "<blue " + g0.cnName + "礼包/>";
               }
            }
            str0 = "包含：" + str0;
         }
         return str0;
      }
      
      private function getGiftArr() : Array
      {
         var g0:GiftAddDefineGroup = null;
         var must0:int = 0;
         var getB0:Boolean = false;
         var unlockB0:Boolean = false;
         var arr0:Array = [];
         var gArr0:Array = this.getDefineGiftArr();
         var anum0:int = this.num;
         for each(g0 in gArr0)
         {
            must0 = g0.mustLevel;
            getB0 = this.getGiftGetB(String(must0));
            unlockB0 = this.getGiftUnlock(g0.name);
            if(!getB0 && unlockB0)
            {
               arr0.push(g0);
            }
         }
         return arr0;
      }
      
      public function getGiftUnlock(giftName0:String) : Boolean
      {
         var gift0:GiftAddDefineGroup = Gaming.defineGroup.gift.getOne(giftName0);
         var must0:Number = gift0.mustLevel;
         if(must0 <= 1)
         {
            return this.dailyB;
         }
         return this.num >= must0;
      }
      
      public function getDefineGiftArr() : Array
      {
         return Gaming.defineGroup.gift.getArrByFather(GIFT_NAME);
      }
      
      public function getGiftEvent(gift0:GiftAddDefineGroup) : void
      {
         var g0:GiftAddDefineGroup = null;
         var must0:int = 0;
         var gArr0:Array = this.getGiftArr();
         for each(g0 in gArr0)
         {
            must0 = g0.mustLevel;
            this.setGiftGetB(String(must0),true);
         }
      }
      
      public function getGettedGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         var name0:String = null;
         var getB0:Boolean = false;
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var gArr0:Array = this.getDefineGiftArr();
         for each(g0 in gArr0)
         {
            name0 = String(g0.mustLevel);
            getB0 = this.getGiftGetB(name0);
            if(getB0)
            {
               gift0.merge(g0);
            }
         }
         return gift0;
      }
   }
}

