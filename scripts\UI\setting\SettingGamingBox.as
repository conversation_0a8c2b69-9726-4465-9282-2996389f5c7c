package UI.setting
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._data.ConstantDefine;
   import dataAll._player.supple.PlayerDataSupple;
   import fl.motion.ColorMatrix;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.events.TimerEvent;
   import flash.filters.ColorMatrixFilter;
   import flash.text.TextField;
   import flash.utils.Timer;
   import gameAll.level.PlayMode;
   import gameAll.level.data.OverLevelShow;
   import w_test.drop.LevelDropCount;
   
   public class SettingGamingBox extends BtnBox
   {
      
      public var labelTag:Sprite = null;
      
      public var errorSp:Sprite;
      
      public var verTxt:TextField;
      
      private var saveTimer:Timer = new Timer(1000);
      
      private var SAVE_T:int = 60;
      
      private var save_t:int = -1;
      
      protected var greenFilter:ColorMatrixFilter;
      
      private var longBtnArr:Array = [];
      
      public function SettingGamingBox()
      {
         super();
         this.saveTimer.addEventListener(TimerEvent.TIMER,this.saveTimerFun);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var btn0:NormalBtn = null;
         elementNameArr = ["labelTag","verTxt","errorSp"];
         super.setImg(img0);
         FontDeal.dealLine(this.errorSp["txt"]);
         FontDeal.dealOne(this.verTxt);
         this.verTxt.styleSheet = TextMethod.getLinkCss("#999999","#FFFF00",false);
         this.verTxt.addEventListener(TextEvent.LINK,this.verTxtClick);
         getBtn("resume").setName("返回关卡中");
         getBtn("restart").setName("重玩本关");
         getBtn("main").setName("退出关卡");
         getBtn("save").setName("保存存档");
         this.aboutBtn.setName("切换P1角色");
         getBtn("pass").setName("作弊功能");
         var greenFilterColor0:ColorMatrix = new ColorMatrix();
         greenFilterColor0.SetHueMatrix(184);
         this.greenFilter = new ColorMatrixFilter(greenFilterColor0.GetFlatArray());
         for each(btn0 in btnArr)
         {
            if(btn0.width > 100)
            {
               this.longBtnArr.push(btn0);
            }
         }
         countBtnData(this.longBtnArr);
         this.dropBtn.setName("掉落\n查看");
         ItemsGripTipCtrl.addNormalBtnTip(this.dropBtn);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get aboutBtn() : NormalBtn
      {
         return getBtn("about");
      }
      
      private function get passBtn() : NormalBtn
      {
         return getBtn("pass");
      }
      
      private function get dropBtn() : NormalBtn
      {
         return getBtn("drop");
      }
      
      private function fleshData() : void
      {
         this.saveTimerFun();
         var gamingB0:Boolean = Gaming.LG.state != "no";
         if(gamingB0)
         {
            showBtnArr(["save","resume","restart","main"],this.longBtnArr);
            getBtn("restart").actived = Gaming.LG.canRestartB();
            if(LevelDropCount.testB)
            {
               getBtn("restart").actived = true;
            }
         }
         else
         {
            showBtnArr(["save","about","pass"],this.longBtnArr);
         }
         this.setBtnNameByMapModel();
         if(this.aboutBtn.visible)
         {
            this.fleshAboutBtn();
         }
         this.fleshPassBtn();
         this.fleshDropBtn(gamingB0);
         this.fleshVerText();
         PlayerDataSupple.oweNuclearStone();
         this.setErrorStr(Gaming.PG.da.getErrorStr());
      }
      
      private function fleshVerText(showUid0:Boolean = false) : void
      {
         var s0:String = ConstantDefine.getAllVer();
         if(showUid0 == false)
         {
            s0 += "  " + TextMethod.link("显示uid","uid");
         }
         else
         {
            s0 += "  " + Gaming.getUid() + "_" + Gaming.getSaveIndex();
         }
         this.verTxt.text = s0;
      }
      
      private function verTxtClick(e:TextEvent) : void
      {
         if(e.text == "uid")
         {
            this.fleshVerText(true);
         }
      }
      
      private function setErrorStr(str0:String) : void
      {
         if(str0 != "")
         {
            str0 = ComMethod.color("重要提示","#FFFF00",14) + "\n" + str0;
            this.errorSp["txt"].htmlText = FontDeal.getDealLeadingStr(this.errorSp["txt"],str0);
            this.errorSp.visible = true;
         }
         else
         {
            this.errorSp.visible = false;
         }
      }
      
      private function fleshDropBtn(gamingB0:Boolean) : void
      {
         this.dropBtn.visible = false;
         this.dropBtn.tipString = "";
      }
      
      private function fleshPassBtn() : void
      {
         if(this.passBtn.visible)
         {
         }
      }
      
      private function setBtnNameByMapModel() : void
      {
         var levelName0:String = this.getLevelName();
         getBtn("restart").setName("重玩" + levelName0);
         getBtn("main").setName("退出" + levelName0);
      }
      
      private function getLevelName() : String
      {
         var model0:String = Gaming.LG.mapMode;
         if(model0 == MapMode.ENDLESS)
         {
            return "无尽模式";
         }
         return "关卡";
      }
      
      private function barChange(v0:Number, label0:String) : void
      {
         if(Boolean(Gaming.PG.save))
         {
            Gaming.PG.save.setting.setValue(label0,v0);
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var mainStr0:String = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         var levelName0:String = this.getLevelName();
         var model0:String = Gaming.LG.mapMode;
         if(btn0.label == "resume")
         {
            Gaming.uiGroup.settingUI.hide();
         }
         else if(btn0.label == "restart")
         {
            Gaming.uiGroup.alertBox.showCheck("确定要重玩" + levelName0 + "？","yesAndNo",0,this.restartFun);
         }
         else if(btn0.label == "main")
         {
            mainStr0 = Gaming.LG.nowLevel.define.info.overWarn;
            if(mainStr0 == "")
            {
               mainStr0 = "确定要退出" + levelName0 + "？";
            }
            if(Gaming.LG.mode == PlayMode.ARENA && !Gaming.LG.nowLevel.dat.winB)
            {
               mainStr0 = "如果退出本关，那么本场竞技挑战就会被判定为失败。\n你确定要退出关卡？";
            }
            else if(model0 == MapMode.ENDLESS)
            {
            }
            Gaming.uiGroup.alertBox.showCheck(mainStr0,"yesAndNo",0,this.mainFun);
         }
         else if(btn0.label == "save")
         {
            this.saveFun();
         }
         else if(btn0.label == "about")
         {
            this.showP1();
         }
         else if(btn0.label == "pass")
         {
            this.showCheating();
         }
      }
      
      private function showPass() : void
      {
         if(Gaming.PG.da.main.havePass())
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("要修改二级密码，请先输入旧的二级密码。","",this.yesOldPass);
         }
         else
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("输入你要设置的二级秘密。\n以后登录该存档都验证这个密码才可进入游戏。","",this.yesPass);
         }
      }
      
      private function yesOldPass(str0:String) : void
      {
         if(Gaming.PG.da.main.panPass(str0))
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("验证成功！请输入新的二级密码（不输入则清空密码）。","",this.yesPass);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("旧的二级密码输入错误！");
         }
      }
      
      private function yesPass(str0:String) : void
      {
         Gaming.PG.da.main.setPass(str0);
         if(str0 == "")
         {
            Gaming.uiGroup.alertBox.showSuccess("清空密码成功！");
         }
         else
         {
            Gaming.uiGroup.alertBox.showSuccess("设置成功！请牢记二级密码。\n保存存档后密码才能生效。");
         }
         this.fleshData();
      }
      
      private function restartFun() : void
      {
         Gaming.LG.restartLevel();
      }
      
      private function mainFun() : void
      {
         Gaming.TG.task.lowerTaskPan(this.afterMainFun);
      }
      
      private function afterMainFun() : void
      {
         Gaming.LG.overLevel(OverLevelShow.UI_CLICK);
      }
      
      private function saveFun() : void
      {
         UIOrder.saveCanStop();
      }
      
      private function showP1() : void
      {
         Gaming.uiGroup.settingUI.hide();
         Gaming.uiGroup.p1SwapBox.show();
      }

      private function showCheating() : void
      {
         // 尝试显示作弊界面，如果失败则显示简单的作弊菜单
         if (this.tryShowCheatingUI()) {
            return;
         }

         // 如果UI界面无法显示，则显示分类作弊菜单
         this.showMainCheatingMenu();
      }

      private function tryShowCheatingUI() : Boolean
      {
         try {
            // 强制设置testUI的haveDataB为true
            Gaming.uiGroup.testUI.haveDataB = true;

            // 激活作弊系统
            Gaming.testCtrl.cheating.enabled = true;

            // 尝试显示TestUI
            Gaming.uiGroup.testUI.show();
            Gaming.uiGroup.testUI.showBox("cheatingBox");

            // 隐藏设置界面
            Gaming.uiGroup.settingUI.hide();

            return true;
         } catch (e:Error) {
            // 如果出错，返回false
            return false;
         }
      }

      private function showMainCheatingMenu() : void
      {
         var menuText:String = "=== 作弊功能主菜单 ===\n\n";
         menuText += "人物类[00]  背包类[01]  地图类[02]  物品类[03]\n";
         menuText += "成就类[04]  尸宠类[05]  任务类[06]  货币类[07]\n";
         menuText += "厨艺类[08]  魂卡类[09]  飞船类[10]  竞技场[11]\n";
         menuText += "军队类[12]  时间类[13]  存档类[14]  图内修改[15]\n\n";
         menuText += "请输入功能类别代码：";

         try {
            Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "00", this.handleMainMenuChoice);
         } catch (e:Error) {
            // 如果文本输入不可用，显示快捷选项
            this.showQuickCheatingOptions();
         }
      }

      private function showQuickCheatingOptions() : void
      {
         var options:String = "作弊功能快捷选项：\n\n";
         options += "确定 = 执行常用作弊功能\n";
         options += "取消 = 返回设置菜单\n\n";
         options += "常用功能包括：\n";
         options += "• 添加10万银币和黄金\n";
         options += "• 设置等级99\n";
         options += "• 添加经验和物品\n";
         options += "• 解锁所有地图\n";
         options += "• 解锁所有背包";

         try {
            Gaming.uiGroup.alertBox.showConfirm(options, this.executeQuickCheats, this.cancelCheating);
         } catch (e:Error) {
            this.executeQuickCheats();
         }
      }

      private function handleMainMenuChoice(choice:String) : void
      {
         if (!choice || choice == "") {
            Gaming.uiGroup.alertBox.showError("请输入有效的功能代码");
            return;
         }

         choice = choice.toLowerCase().replace(/\s/g, "");

         switch(choice) {
            case "00":
            case "人物类":
               this.showHeroCheatingMenu();
               break;
            case "01":
            case "背包类":
               this.showBagCheatingMenu();
               break;
            case "02":
            case "地图类":
               this.showMapCheatingMenu();
               break;
            case "03":
            case "物品类":
               this.showThingCheatingMenu();
               break;
            case "04":
            case "成就类":
               this.showAchieveCheatingMenu();
               break;
            case "05":
            case "尸宠类":
               this.showPetCheatingMenu();
               break;
            case "06":
            case "任务类":
               this.showTaskCheatingMenu();
               break;
            case "07":
            case "货币类":
               this.showMoneyCheatingMenu();
               break;
            case "08":
            case "厨艺类":
               this.showFoodCheatingMenu();
               break;
            case "09":
            case "魂卡类":
               this.showCardCheatingMenu();
               break;
            case "10":
            case "飞船类":
               this.showSpaceCheatingMenu();
               break;
            case "11":
            case "竞技场":
               this.showArenaCheatingMenu();
               break;
            case "12":
            case "军队类":
               this.showUnionCheatingMenu();
               break;
            case "13":
            case "时间类":
               this.showTimeCheatingMenu();
               break;
            case "14":
            case "存档类":
               this.showSaveCheatingMenu();
               break;
            case "15":
            case "图内修改":
               this.showInMapCheatingMenu();
               break;
            default:
               Gaming.uiGroup.alertBox.showError("无效的功能代码，请重新输入");
               break;
         }
      }

      // 人物类作弊菜单
      private function showHeroCheatingMenu() : void
      {
         var menuText:String = "=== 人物类作弊功能 ===\n\n";
         menuText += "人物昵称[00*昵称]  人物等级[01*数值]\n";
         menuText += "人物经验[02*数值]  添加称号[03*称号]\n";
         menuText += "巅峰等级[04*数值]  巅峰经验[05*数值]\n";
         menuText += "职务等级[06*数值]  职务经验[07*数值]\n";
         menuText += "活跃度[08*数值]  返回主菜单[99]\n\n";
         menuText += "请输入指令（格式：代码*参数）：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "01*99", this.executeHeroCheating);
      }

      // 背包类作弊菜单
      private function showBagCheatingMenu() : void
      {
         var menuText:String = "=== 背包类作弊功能 ===\n\n";
         menuText += "武器背包[00*数值]  装备背包[01*数值]\n";
         menuText += "物品背包[02*数值]  基因背包[03*数值]\n";
         menuText += "零件背包[04*数值]  技能背包[05*数值]\n";
         menuText += "尸宠背包[06*数值]  魂卡背包[07*数值]\n";
         menuText += "武器仓库[08*数值]  解锁所有[09]\n";
         menuText += "返回主菜单[99]\n\n";
         menuText += "请输入指令（格式：代码*参数）：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "09", this.executeBagCheating);
      }

      // 地图类作弊菜单
      private function showMapCheatingMenu() : void
      {
         var menuText:String = "=== 地图类作弊功能 ===\n\n";
         menuText += "解锁所有地图[00]  通关所有地图[01]\n";
         menuText += "解锁所有秘境[02]  设置秘境钥匙[03*数值]\n";
         menuText += "返回主菜单[99]\n\n";
         menuText += "请输入指令（格式：代码*参数）：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "00", this.executeMapCheating);
      }

      // 物品类作弊菜单
      private function showThingCheatingMenu() : void
      {
         var menuText:String = "=== 物品类作弊功能 ===\n\n";
         menuText += "添加所有物品[00*数值]  添加所有武器[01*等级]\n";
         menuText += "添加所有黑武器[02*等级]  添加所有套装[03]\n";
         menuText += "添加所有时装[04]  返回主菜单[99]\n\n";
         menuText += "请输入指令（格式：代码*参数）：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "00*999", this.executeThingCheating);
      }

      // 货币类作弊菜单
      private function showMoneyCheatingMenu() : void
      {
         var menuText:String = "=== 货币类作弊功能 ===\n\n";
         menuText += "设置银币[00*数值]  设置积分[01*数值]\n";
         menuText += "设置黄金[02*数值]  黄金充值[03*数量]\n";
         menuText += "设置纪念币[04*数值]  设置小南瓜[05*数值]\n";
         menuText += "返回主菜单[99]\n\n";
         menuText += "请输入指令（格式：代码*参数）：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "02*9999999", this.executeMoneyCheating);
      }

      // 时间类作弊菜单
      private function showTimeCheatingMenu() : void
      {
         var menuText:String = "=== 时间类作弊功能 ===\n\n";
         menuText += "时间加速倍数[00*倍数]  设置游戏帧数[01*帧数]\n";
         menuText += "设置新的一天[02]  设置新的一周[03]\n";
         menuText += "本地时间开关[04]  清除双倍时间[05]\n";
         menuText += "返回主菜单[99]\n\n";
         menuText += "请输入指令（格式：代码*参数）：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "01*60", this.executeTimeCheating);
      }

      // 图内修改菜单
      private function showInMapCheatingMenu() : void
      {
         var menuText:String = "=== 图内修改功能 ===\n\n";
         menuText += "通关当前[00]  重玩关卡[01]  主角AI[02]\n";
         menuText += "秒杀队友[03]  秒杀全图[04]  寄生首领[05]\n";
         menuText += "寄生宠物[06]  副手怒气[07]  返回主菜单[99]\n\n";
         menuText += "请输入指令代码：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "00", this.executeInMapCheating);
      }

      // 其他分类菜单的占位函数
      private function showAchieveCheatingMenu() : void
      {
         Gaming.uiGroup.alertBox.showInfo("成就类功能开发中，请使用其他功能");
         this.showMainCheatingMenu();
      }

      private function showPetCheatingMenu() : void
      {
         Gaming.uiGroup.alertBox.showInfo("尸宠类功能开发中，请使用其他功能");
         this.showMainCheatingMenu();
      }

      private function showTaskCheatingMenu() : void
      {
         Gaming.uiGroup.alertBox.showInfo("任务类功能开发中，请使用其他功能");
         this.showMainCheatingMenu();
      }

      private function showFoodCheatingMenu() : void
      {
         Gaming.uiGroup.alertBox.showInfo("厨艺类功能开发中，请使用其他功能");
         this.showMainCheatingMenu();
      }

      private function showCardCheatingMenu() : void
      {
         Gaming.uiGroup.alertBox.showInfo("魂卡类功能开发中，请使用其他功能");
         this.showMainCheatingMenu();
      }

      private function showSpaceCheatingMenu() : void
      {
         Gaming.uiGroup.alertBox.showInfo("飞船类功能开发中，请使用其他功能");
         this.showMainCheatingMenu();
      }

      private function showArenaCheatingMenu() : void
      {
         Gaming.uiGroup.alertBox.showInfo("竞技场功能开发中，请使用其他功能");
         this.showMainCheatingMenu();
      }

      private function showUnionCheatingMenu() : void
      {
         Gaming.uiGroup.alertBox.showInfo("军队类功能开发中，请使用其他功能");
         this.showMainCheatingMenu();
      }

      private function showSaveCheatingMenu() : void
      {
         Gaming.uiGroup.alertBox.showInfo("存档类功能开发中，请使用其他功能");
         this.showMainCheatingMenu();
      }

      // 执行人物类作弊
      private function executeHeroCheating(command:String) : void
      {
         if (!command || command == "") {
            Gaming.uiGroup.alertBox.showError("请输入有效的指令");
            return;
         }

         var parts:Array = command.split("*");
         var code:String = parts[0];
         var value:String = parts.length > 1 ? parts[1] : "";

         if (code == "99") {
            this.showMainCheatingMenu();
            return;
         }

         // 激活作弊系统
         Gaming.testCtrl.cheating.enabled = true;
         Gaming.uiGroup.testUI.haveDataB = true;

         var message:String = "";

         switch(code) {
            case "00": // 人物昵称
               if (value != "") {
                  Gaming.PG.DATA.base.save.playerName = value;
                  message = "设置人物昵称为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入昵称");
                  return;
               }
               break;

            case "01": // 人物等级
               if (value != "") {
                  Gaming.PG.DATA.base.save.level = Number(value);
                  Gaming.uiGroup.mainUI.show();
                  message = "设置人物等级为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入等级数值");
                  return;
               }
               break;

            case "02": // 人物经验
               if (value != "") {
                  Gaming.PG.DATA.base.save.exp = Number(value);
                  message = "设置人物经验为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入经验数值");
                  return;
               }
               break;

            case "03": // 添加称号
               if (value != "") {
                  // 这里可以添加称号逻辑
                  message = "添加称号功能开发中";
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入称号名称");
                  return;
               }
               break;

            case "04": // 巅峰等级
               if (value != "") {
                  Gaming.PG.da.peak.save.lv = Number(value);
                  message = "设置巅峰等级为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入巅峰等级");
                  return;
               }
               break;

            case "05": // 巅峰经验
               if (value != "") {
                  Gaming.PG.da.peak.save.exp = Number(value);
                  message = "设置巅峰经验为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入巅峰经验");
                  return;
               }
               break;

            default:
               Gaming.uiGroup.alertBox.showError("无效的指令代码");
               return;
         }

         Gaming.uiGroup.alertBox.showSuccess(message);
      }

      // 执行背包类作弊
      private function executeBagCheating(command:String) : void
      {
         if (!command || command == "") {
            Gaming.uiGroup.alertBox.showError("请输入有效的指令");
            return;
         }

         var parts:Array = command.split("*");
         var code:String = parts[0];
         var value:String = parts.length > 1 ? parts[1] : "";

         if (code == "99") {
            this.showMainCheatingMenu();
            return;
         }

         var message:String = "";
         var numValue:Number = Number(value);

         switch(code) {
            case "00": // 武器背包
               if (value != "") {
                  Gaming.PG.da.armsBag.saveGroup.unlockTo(numValue);
                  message = "设置武器背包数量为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入数量");
                  return;
               }
               break;

            case "01": // 装备背包
               if (value != "") {
                  Gaming.PG.da.equipBag.saveGroup.unlockTo(numValue);
                  message = "设置装备背包数量为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入数量");
                  return;
               }
               break;

            case "02": // 物品背包
               if (value != "") {
                  Gaming.PG.da.thingsBag.saveGroup.unlockTo(numValue);
                  message = "设置物品背包数量为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入数量");
                  return;
               }
               break;

            case "09": // 解锁所有
               Gaming.PG.da.armsHouse.saveGroup.unlockTo(240);
               Gaming.PG.da.equipHouse.saveGroup.unlockTo(240);
               Gaming.PG.da.geneBag.saveGroup.unlockTo(232);
               Gaming.PG.da.arms.saveGroup.unlockTo(5);
               Gaming.PG.da.skill.saveGroup.unlockTo(9);
               Gaming.PG.da.pet.saveGroup.lockLen = Number(999);
               Gaming.PG.da.partsBag.saveGroup.unlockTo(144);
               Gaming.PG.da.armsBag.saveGroup.unlockTo(80);
               Gaming.PG.da.equipBag.saveGroup.unlockTo(180);
               Gaming.PG.da.thingsBag.saveGroup.unlockTo(600);
               message = "已解锁所有背包";
               break;

            default:
               Gaming.uiGroup.alertBox.showError("无效的指令代码");
               return;
         }

         Gaming.uiGroup.alertBox.showSuccess(message);
      }

      // 执行地图类作弊
      private function executeMapCheating(command:String) : void
      {
         if (!command || command == "") {
            Gaming.uiGroup.alertBox.showError("请输入有效的指令");
            return;
         }

         var parts:Array = command.split("*");
         var code:String = parts[0];
         var value:String = parts.length > 1 ? parts[1] : "";

         if (code == "99") {
            this.showMainCheatingMenu();
            return;
         }

         var message:String = "";

         switch(code) {
            case "00": // 解锁所有地图
               Gaming.PG.da.worldMap.saveGroup.unlockAll();
               Gaming.PG.da.worldMap.saveGroup.winAll();
               Gaming.PG.da.worldMap.saveGroup.unlockAllDiff();
               Gaming.uiGroup.mainUI.show();
               message = "已解锁所有地图";
               break;

            case "01": // 通关所有地图
               Gaming.PG.da.worldMap.saveGroup.unlockAll();
               Gaming.PG.da.worldMap.saveGroup.winAll();
               Gaming.PG.da.worldMap.saveGroup.unlockAllDiff();
               Gaming.uiGroup.mainUI.show();
               message = "已通关所有地图";
               break;

            case "02": // 解锁所有秘境
               Gaming.PG.da.wilder.unlockAllWider();
               message = "已解锁所有秘境";
               break;

            case "03": // 设置秘境钥匙
               if (value != "") {
                  Gaming.PG.da.wilder.saveGroup.keyNum = Number(value);
                  message = "设置秘境钥匙为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入钥匙数量");
                  return;
               }
               break;

            default:
               Gaming.uiGroup.alertBox.showError("无效的指令代码");
               return;
         }

         Gaming.uiGroup.alertBox.showSuccess(message);
      }

      // 执行物品类作弊
      private function executeThingCheating(command:String) : void
      {
         if (!command || command == "") {
            Gaming.uiGroup.alertBox.showError("请输入有效的指令");
            return;
         }

         var parts:Array = command.split("*");
         var code:String = parts[0];
         var value:String = parts.length > 1 ? parts[1] : "";

         if (code == "99") {
            this.showMainCheatingMenu();
            return;
         }

         // 激活作弊系统
         Gaming.testCtrl.cheating.enabled = true;
         Gaming.uiGroup.testUI.haveDataB = true;

         var message:String = "";

         switch(code) {
            case "00": // 添加所有物品
               Gaming.testCtrl.cheating.cheatingByStr("addAllThings" + (value || "10"));
               message = "已添加所有物品 x" + (value || "10");
               break;

            case "01": // 添加所有武器
               Gaming.testCtrl.cheating.cheatingByStr("addAllWeapon" + (value || "1"));
               message = "已添加所有武器，等级：" + (value || "1");
               break;

            case "02": // 添加所有黑武器
               Gaming.testCtrl.cheating.cheatingByStr("addBlackArms");
               message = "已添加所有黑色武器";
               break;

            case "03": // 添加所有套装
               Gaming.testCtrl.cheating.cheatingByStr("addSuit");
               message = "已添加所有套装";
               break;

            case "04": // 添加所有时装
               Gaming.testCtrl.cheating.cheatingByStr("addFashion");
               message = "已添加所有时装";
               break;

            default:
               Gaming.uiGroup.alertBox.showError("无效的指令代码");
               return;
         }

         Gaming.uiGroup.alertBox.showSuccess(message);
      }

      // 执行货币类作弊
      private function executeMoneyCheating(command:String) : void
      {
         if (!command || command == "") {
            Gaming.uiGroup.alertBox.showError("请输入有效的指令");
            return;
         }

         var parts:Array = command.split("*");
         var code:String = parts[0];
         var value:String = parts.length > 1 ? parts[1] : "";

         if (code == "99") {
            this.showMainCheatingMenu();
            return;
         }

         var message:String = "";
         var numValue:Number = Number(value);

         switch(code) {
            case "00": // 设置银币
               if (value != "") {
                  Gaming.PG.save.coin = numValue;
                  message = "设置银币为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入银币数量");
                  return;
               }
               break;

            case "01": // 设置积分
               if (value != "") {
                  Gaming.PG.save.sore = numValue;
                  message = "设置积分为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入积分数量");
                  return;
               }
               break;

            case "02": // 设置黄金
               if (value != "") {
                  Gaming.PG.save.gold = numValue;
                  message = "设置黄金为：" + value;
               } else {
                  Gaming.uiGroup.alertBox.showError("请输入黄金数量");
                  return;
               }
               break;

            default:
               Gaming.uiGroup.alertBox.showError("无效的指令代码");
               return;
         }

         Gaming.uiGroup.alertBox.showSuccess(message);
      }

      // 执行时间类作弊
      private function executeTimeCheating(command:String) : void
      {
         if (!command || command == "") {
            Gaming.uiGroup.alertBox.showError("请输入有效的指令");
            return;
         }

         var parts:Array = command.split("*");
         var code:String = parts[0];
         var value:String = parts.length > 1 ? parts[1] : "";

         if (code == "99") {
            this.showMainCheatingMenu();
            return;
         }

         // 激活作弊系统
         Gaming.testCtrl.cheating.enabled = true;
         Gaming.uiGroup.testUI.haveDataB = true;

         var message:String = "";

         switch(code) {
            case "01": // 设置游戏帧数
               Gaming.testCtrl.cheating.cheatingByStr("setFrame" + (value || "60"));
               message = "设置游戏帧数为：" + (value || "60");
               break;

            case "02": // 设置新的一天
               Gaming.testCtrl.cheating.cheatingByStr("newDay");
               message = "已触发新的一天";
               break;

            case "03": // 设置新的一周
               Gaming.testCtrl.cheating.cheatingByStr("newWeek");
               message = "已触发新的一周";
               break;

            default:
               Gaming.uiGroup.alertBox.showError("无效的指令代码");
               return;
         }

         Gaming.uiGroup.alertBox.showSuccess(message);
      }

      // 执行图内修改
      private function executeInMapCheating(command:String) : void
      {
         if (!command || command == "") {
            Gaming.uiGroup.alertBox.showError("请输入有效的指令");
            return;
         }

         if (command == "99") {
            this.showMainCheatingMenu();
            return;
         }

         // 激活作弊系统
         Gaming.testCtrl.cheating.enabled = true;
         Gaming.uiGroup.testUI.haveDataB = true;

         var message:String = "";

         switch(command) {
            case "00": // 通关当前
               Gaming.testCtrl.cheating.cheatingByStr("winLevel");
               message = "已通关当前关卡";
               break;

            case "01": // 重玩关卡
               Gaming.testCtrl.cheating.cheatingByStr("restartLevel");
               message = "已重新开始关卡";
               break;

            case "02": // 主角AI
               Gaming.testCtrl.cheating.cheatingByStr("heroAI");
               message = "已切换主角AI";
               break;

            case "03": // 秒杀队友
               Gaming.testCtrl.cheating.cheatingByStr("killTeam");
               message = "已秒杀队友";
               break;

            case "04": // 秒杀全图
               Gaming.testCtrl.cheating.cheatingByStr("killAll");
               message = "已秒杀全图";
               break;

            default:
               Gaming.uiGroup.alertBox.showError("无效的指令代码");
               return;
         }

         Gaming.uiGroup.alertBox.showSuccess(message);
      }

      // 执行快捷作弊
      private function executeQuickCheats() : void
      {
         // 激活作弊系统
         Gaming.testCtrl.cheating.enabled = true;
         Gaming.uiGroup.testUI.haveDataB = true;

         // 执行常用作弊指令
         Gaming.PG.save.coin = 9999999;
         Gaming.PG.save.gold = 9999999;
         Gaming.PG.save.sore = 9999999;
         Gaming.PG.DATA.base.save.level = 99;
         Gaming.PG.DATA.base.save.exp = 999999;

         // 解锁背包
         Gaming.PG.da.armsBag.saveGroup.unlockTo(80);
         Gaming.PG.da.equipBag.saveGroup.unlockTo(180);
         Gaming.PG.da.thingsBag.saveGroup.unlockTo(600);

         // 解锁地图
         Gaming.PG.da.worldMap.saveGroup.unlockAll();
         Gaming.PG.da.worldMap.saveGroup.winAll();
         Gaming.PG.da.worldMap.saveGroup.unlockAllDiff();

         Gaming.uiGroup.mainUI.show();

         var successMsg:String = "=== 快捷作弊执行完成 ===\n\n";
         successMsg += "✓ 设置银币、黄金、积分为999万\n";
         successMsg += "✓ 设置等级99，经验99万\n";
         successMsg += "✓ 解锁所有背包\n";
         successMsg += "✓ 解锁并通关所有地图";

         Gaming.uiGroup.alertBox.showSuccess(successMsg);
      }

      private function cancelCheating() : void
      {
         Gaming.uiGroup.alertBox.showInfo("已取消作弊功能");
      }

      // 兼容性函数，重定向到主菜单
      private function handleCheatingMenuChoice(choice:String) : void
      {
         this.showMainCheatingMenu();
      }

      private function executeCheatCommand(command:String) : void
      {
         this.showMainCheatingMenu();
      }
      
      private function fleshAboutBtn() : void
      {
         this.aboutBtn.setName("切换P1角色");
         this.aboutBtn.visible = !Gaming.LG.isGaming();
      }
      
      private function aboutBtnClick(e:MouseEvent) : void
      {
         // 这个方法现在不再使用，因为about按钮现在用于P1切换
      }
      
      private function doubleSwap() : void
      {
         var doubleB0:Boolean = Gaming.PG.da.moreWay.switchDoubleModel();
         this.fleshAboutBtn();
         Gaming.uiGroup.moreBox.fleshData();
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         if(!Gaming.PG.save.guide.firstDoubleB)
         {
            Gaming.uiGroup.alertBox.showNormal("您设置当前游戏为" + (doubleB0 ? "双" : "单") + "人模式，人物控制按键已经改变，\n是否前往系统界面查看按键设置？","yesAndNo",this.gotoSeeDoubleKey);
            Gaming.PG.save.guide.firstDoubleB = true;
         }
      }
      
      private function gotoSeeDoubleKey() : void
      {
         UIShow.showApp("setting",true);
         Gaming.uiGroup.settingUI.showBox("key");
      }
      
      public function saveShow() : void
      {
         this.saveTimer.start();
         this.save_t = 0;
         getBtn("save").actived = false;
         this.saveTimerFun();
      }
      
      private function saveTimerFun(e:TimerEvent = null) : void
      {
         var btn0:NormalBtn = getBtn("save");
         if(this.save_t >= this.SAVE_T)
         {
            this.save_t = -1;
            btn0.setName("保存存档");
            btn0.actived = true;
            this.saveTimer.stop();
         }
         else if(this.save_t >= 0)
         {
            ++this.save_t;
            if(this.visible)
            {
               btn0.setName("保存存档（" + (this.SAVE_T - this.save_t) + "）");
            }
         }
      }
   }
}

