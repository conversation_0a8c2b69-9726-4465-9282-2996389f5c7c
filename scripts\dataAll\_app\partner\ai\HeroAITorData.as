package dataAll._app.partner.ai
{
   import dataAll._app.edit.TorData;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit._def.EditProGather;
   import dataAll._app.edit.tor.TorEditAgent;
   import dataAll._player.PlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.body.define.HeroDefine;
   import dataAll.level.define.unit.AiType;
   import gameAll.body.ai.one.PlayerAiData;
   
   public class HeroAITorData extends TorData
   {
      
      public var PD:NormalPlayerData;
      
      private var heroDef:HeroDefine;
      
      private var baseData:PlayerAiData = new PlayerAiData();
      
      public function HeroAITorData()
      {
         super();
      }
      
      public function initByDefine(heroD0:HeroDefine) : void
      {
         this.heroDef = heroD0;
         this.baseData.setTo(heroD0.oneAiLabel);
      }
      
      public function dealAiData(da0:PlayerAiData) : void
      {
         da0.inData_byObj(this.baseData);
         da0.inData_byObj(torSave.obj);
      }
      
      override public function getTorEditAgent() : TorEditAgent
      {
         var a0:TorEditAgent = new TorEditAgent();
         var ca0:String = this.PD.isMainPlayerB() ? "main" : "parnter";
         a0.da = this;
         a0.initByData(this,EditProGather.heroAI,false,ca0);
         var dropFollowB0:Boolean = false;
         var pd0:PlayerData = this.PD as PlayerData;
         if(Boolean(pd0))
         {
            dropFollowB0 = pd0.post.isDropFollowB();
         }
         if(dropFollowB0 == false)
         {
            a0.addNoChange("restartB");
         }
         return a0;
      }
      
      override public function getDefValue(proD0:EditProDefine) : *
      {
         return this.getBaseValue(proD0);
      }
      
      override public function getBaseValue(proD0:EditProDefine) : *
      {
         if(proD0.name == "warningRange")
         {
            return AiType.followBodyAttack_WarningRange;
         }
         return proD0.getObjValue(this.baseData);
      }
      
      override public function getValue(proD0:EditProDefine) : *
      {
         var v0:* = getObjValue(proD0);
         if(v0 == null)
         {
            v0 = this.getBaseValue(proD0);
         }
         return v0;
      }
   }
}

