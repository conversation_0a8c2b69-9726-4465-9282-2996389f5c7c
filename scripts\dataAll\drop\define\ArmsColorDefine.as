package dataAll.drop.define
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.arms.define.ArmsType;
   import dataAll.equip.define.EquipColor;
   
   public class ArmsColorDefine extends DropColorDefine
   {
      
      public var proPro:Array = [];
      
      public var dpsRange:Array = [];
      
      public var specialNum:Array = [];
      
      public var skillNum:Array = [];
      
      public var godSkillNum:Array = [];
      
      public var colorTypeRange:Object = {};
      
      public function ArmsColorDefine()
      {
         super();
      }
      
      override public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         super.inData_byXML(xml0);
         var pro_arr0:Array = ["proPro","specialNum","skillNum","godSkillNum"];
         for(n in pro_arr0)
         {
            pro0 = pro_arr0[n];
            this[pro0] = ComMethod.stringToNumberArr(String(xml0[pro0]));
         }
         this.dpsRange = ComMethod.stringToRangeArr(String(xml0.dpsRange));
         this.addBlack();
      }
      
      private function addBlack() : void
      {
         if(name.indexOf(EquipColor.BLACK) == -1)
         {
            name.push(EquipColor.BLACK);
            itemsLvRange.push([0,0]);
            this.dpsRange.push([1,1]);
            this.godSkillNum.push(2);
            this.proPro.push(0);
            this.skillNum.push(2);
            this.specialNum.push(4);
            normalPro.push(0);
            superPro.push(0);
            bossPro.push(0);
         }
      }
      
      public function getColorTypeArr(color0:String) : Array
      {
         return ArmsType.NORMAL_TYPE_ARR;
      }
   }
}

