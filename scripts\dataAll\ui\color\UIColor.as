package dataAll.ui.color
{
   public class UIColor
   {
      
      public static const orange:String = "orange";
      
      public static const orangeness:String = "orangeness";
      
      public static const red:String = "red";
      
      public static const redness:String = "redness";
      
      public static const yellow:String = "yellow";
      
      public static const yellowdark:String = "yellowdark";
      
      public static const green:String = "green";
      
      public static const greeness:String = "greeness";
      
      public static const greenDark:String = "greenDark";
      
      public static const yellowGreen:String = "yellowGreen";
      
      public static const grayness:String = "grayness";
      
      public static const gray:String = "gray";
      
      public static const graydark:String = "graydark";
      
      public static const purple:String = "purple";
      
      public static const purpleness:String = "purpleness";
      
      public static const navy:String = "navy";
      
      public static const navyness:String = "navyness";
      
      public static const blue:String = "blue";
      
      public static const blueness:String = "blueness";
      
      public static const blove:String = "blove";
      
      public static const white:String = "white";
      
      public static const black:String = "black";
      
      private static const orangeColor:String = "FF6600";
      
      private static const orangenessColor:String = "FF924A";
      
      private static const redColor:String = "FF1212";
      
      private static const rednessColor:String = "FF6666";
      
      private static const yellowColor:String = "FFFF00";
      
      private static const yellowdarkColor:String = "D4BA00";
      
      private static const greenColor:String = "00FF00";
      
      private static const greenessColor:String = "79FF79";
      
      private static const greenDarkColor:String = "00CC00";
      
      private static const yellowGreenColor:String = "ADFF00";
      
      private static const graynessColor:String = "CCCCCC";
      
      private static const grayColor:String = "999999";
      
      private static const graydarkColor:String = "666666";
      
      private static const purpleColor:String = "FF66FF";
      
      private static const purplenessColor:String = "FF99FF";
      
      private static const navyColor:String = "00FFFF";
      
      private static const navynessColor:String = "70FFFF";
      
      private static const blueColor:String = "0000FF";
      
      private static const bluenessColor:String = "8383FF";
      
      private static const bloveColor:String = "0099FF";
      
      private static const whiteColor:String = "FFFFFF";
      
      private static const blackColor:String = "7979BF";
      
      public static const colorNameArr:Array = [greenDark,blove,orange,orangeness,red,redness,yellow,yellowdark,green,greeness,yellowGreen,grayness,gray,graydark,purple,purpleness,navy,navyness,blue,blueness,white,black];
      
      public function UIColor()
      {
         super();
      }
      
      public static function getHtml(name0:String) : String
      {
         return "#" + UIColor[name0 + "Color"];
      }
      
      public static function getValue(name0:String) : uint
      {
         return uint("0x" + UIColor[name0 + "Color"]);
      }
      
      public static function mixed(str0:String, colorName0:String) : String
      {
         if(colorName0 != "")
         {
            return "<" + colorName0 + ">" + str0 + "</>";
         }
         return str0;
      }
      
      public static function getTitleInfo(title0:String, info0:String, titleColor0:String = "", infoColor0:String = "") : String
      {
         title0 += "：";
         if(titleColor0 != "")
         {
            title0 = UIColor.mixed(title0,titleColor0);
         }
         if(infoColor0 != "")
         {
            info0 = UIColor.mixed(info0,infoColor0);
         }
         return title0 + info0;
      }
   }
}

