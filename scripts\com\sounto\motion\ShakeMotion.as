package com.sounto.motion
{
   import com.sounto.math.Maths;
   import dataAll.image.BoomShakeDefine;
   
   public class ShakeMotion
   {
      
      public var x:Number = 0;
      
      public var y:Number = 0;
      
      private var range1:Number = 0;
      
      private var range2:Number = 0;
      
      private var number:Number = 0;
      
      private var way:String = "random";
      
      private var ra:Number = 0;
      
      public var time:int = 1;
      
      public var now_t:int = 0;
      
      private var attenuate:Number = 0;
      
      public var enabled:Boolean = false;
      
      public var sceneMiddeX:int = 0;
      
      public var sceneMiddeY:int = 0;
      
      public function ShakeMotion()
      {
         super();
      }
      
      public function startShake(_number:Number, _time:Number, _ra:Number, _range1:Number, _range2:Number, _attenuate:Number = 0, _way:String = "cos") : void
      {
         this.init();
         this.number = _number;
         this.time = int(_time * INIT.FPS);
         if(this.time == 0)
         {
            this.time = 1;
         }
         this.ra = _ra;
         if(_range1 < this.range1)
         {
            this.range1 = _range1;
         }
         if(_range2 > this.range2)
         {
            this.range2 = _range2;
         }
         this.attenuate = _attenuate;
         this.way = _way;
         this.enabled = true;
         this.now_t = 0;
      }
      
      public function start(_time:Number, _ra:Number, _range1:Number, num0:Number = 1) : void
      {
         this.startShake(num0,_time,_ra,0,_range1,0,"cos");
      }
      
      public function startGap(_time:Number, _ra:Number, _range1:Number, x0:Number, y0:Number, num0:Number = 1) : void
      {
         var mul0:Number = this.getShakeMulByXY(x0,y0);
         this.startShake(num0,_time,_ra,0,_range1 * mul0,0,"cos");
      }
      
      public function startBoom(_number:Number, _time:Number, _ra:Number, _range1:Number) : void
      {
         this.startShake(_number,_time,_ra,-_range1,_range1);
      }
      
      public function startByBoomDefine(d0:BoomShakeDefine, _ra:Number, x0:Number, y0:Number, mul0:Number = 1) : void
      {
         var range0:Number = d0.range * mul0;
         mul0 *= this.getShakeMulByXY(x0,y0);
         if(d0.ra != -1000)
         {
            _ra = d0.ra;
         }
         if(d0.type == "")
         {
            this.startBoom(d0.num,d0.time,_ra,range0);
         }
         else
         {
            this.startShake(d0.num,d0.time,_ra,-range0,range0,0,d0.type);
         }
      }
      
      private function getShakeMulByXY(x0:Number, y0:Number) : Number
      {
         var len0:Number = Maths.Long(x0 - this.sceneMiddeX,y0 - this.sceneMiddeY);
         if(len0 < 400)
         {
            return 1;
         }
         return 1 / ((len0 - 400) / 200 + 1);
      }
      
      public function init() : void
      {
         this.x = 0;
         this.y = 0;
         this.range1 = 0;
         this.range2 = 0;
         this.number = 0;
         this.ra = 0;
         this.time = 1;
         this.now_t = 0;
         this.attenuate = 0;
         this.enabled = false;
      }
      
      private function getData() : void
      {
         var av:Number = (this.time - this.now_t) / this.time * (1 - this.attenuate) + this.attenuate;
         var av2:Number = 0;
         if(this.way == "random")
         {
            this.x = (Math.random() * (this.range2 - this.range1) + this.range1) * Math.cos(this.ra) * av;
            this.y = (Math.random() * (this.range2 - this.range1) + this.range1) * Math.sin(this.ra) * av;
         }
         else if(this.way == "cos")
         {
            av2 = (Math.cos(this.now_t / this.time * Math.PI * this.number) + 1) / 2;
            this.x = (av2 * (this.range2 - this.range1) + this.range1) * Math.cos(this.ra) * av;
            this.y = (av2 * (this.range2 - this.range1) + this.range1) * Math.sin(this.ra) * av;
         }
         else if(this.way == "s_sin")
         {
            av2 = Math.sin(this.now_t / this.time * Math.PI * this.number) / 2;
            this.x = (av2 * (this.range2 - this.range1) + this.range1) * Math.cos(this.ra) * av;
            this.y = (av2 * (this.range2 - this.range1) + this.range1) * Math.sin(this.ra) * av;
         }
         else if(this.way == "mess")
         {
            this.x = Math.random() * this.range1 * Math.cos(Math.random() * Math.PI * 2) * av;
            this.y = Math.random() * this.range1 * Math.sin(Math.random() * Math.PI * 2) * av;
         }
      }
      
      public function shakeTimer() : void
      {
         if(this.enabled)
         {
            if(this.now_t < this.time)
            {
               ++this.now_t;
               this.getData();
            }
            else
            {
               this.x = 0;
               this.y = 0;
               this.range1 = 0;
               this.range2 = 0;
               this.enabled = false;
            }
         }
      }
   }
}

