package dataAll.equip.add
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ObjectMethod;
   import com.sounto.utils.StringMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._player.define.PlayerDataName;
   import dataAll.pro.PropertyArrayDefine;
   
   public class EquipAddChild
   {
      
      public var def:PropertyArrayDefine;
      
      public var dataFrom:String = "";
      
      public var value:Number = 0;
      
      public var max:Number = 0;
      
      public var maxObj:Object = null;
      
      public var tipArr:Array = null;
      
      public function EquipAddChild()
      {
         super();
      }
      
      public static function addTipInObj(addObj0:Object, tipObj0:Object, cn0:String) : void
      {
         var n:* = undefined;
         var proD0:PropertyArrayDefine = null;
         var tipArr0:Array = null;
         var tip0:String = null;
         for(n in addObj0)
         {
            if(addObj0[n] > 0)
            {
               proD0 = Gaming.defineGroup.getPropertyArrayDefine(n);
               if(Boolean(proD0))
               {
                  tipArr0 = ObjectMethod.getEleAndAdd(tipObj0,n,Array);
                  tip0 = "·" + cn0 + " <yellow " + proD0.getValueString(addObj0[n]) + "/>";
                  tipArr0.push(tip0);
               }
            }
         }
      }
      
      public static function addMaxInObj(addObj0:Object, maxObj0:Object, cn0:String, name0:String) : void
      {
         var n:* = undefined;
         var mo:Object = null;
         for(n in addObj0)
         {
            if(maxObj0.hasOwnProperty(n) == false)
            {
               maxObj0[n] = {"v":0};
            }
            mo = maxObj0[n];
            if(addObj0[n] > mo.v)
            {
               IN_MaxObj(mo,addObj0[n],cn0,name0);
            }
         }
      }
      
      public static function IN_MaxObj(mo:Object, v0:Number, cn0:String = "", name0:String = "") : Object
      {
         mo.v = v0;
         mo.cn = cn0;
         mo.name = name0;
         return mo;
      }
      
      public function getText() : String
      {
         var fromCn0:String = PlayerDataName.getOneCn(this.dataFrom,this.def.name);
         fromCn0 = "[" + fromCn0 + "]";
         if(this.max > 0 && this.dataFrom != PlayerDataName.vip)
         {
            if(this.value >= this.max)
            {
               fromCn0 = ComMethod.color(fromCn0,"#009900");
            }
            else
            {
               fromCn0 = ComMethod.color(fromCn0,"#913A00");
            }
         }
         else
         {
            fromCn0 = ComMethod.graydrak(fromCn0);
         }
         var vs0:String = this.def.getValueString(this.value,false,this.def.isPer() ? this.def.unit : "");
         var link0:String = this.def.name + ":" + this.dataFrom;
         vs0 = TextMethod.link(vs0,link0);
         return fromCn0 + vs0;
      }
      
      public function getTip() : String
      {
         var maxCn0:String = null;
         var s0:String = "<orange " + PlayerDataName.getCn(this.dataFrom,this.def.name) + "加成/>";
         if(Boolean(this.tipArr))
         {
            s0 += "\n" + StringMethod.concatStringArr(this.tipArr,1,"\n");
         }
         if(this.max > 0)
         {
            s0 += "\n\n最大可获得：<green " + this.def.getValueString(this.max) + "/>";
            if(Boolean(this.maxObj))
            {
               maxCn0 = this.getMaxCn();
               if(maxCn0 != "")
               {
                  s0 += "<gray (" + maxCn0 + ")/>";
               }
            }
         }
         return s0;
      }
      
      public function getMaxCn() : String
      {
         return ObjectMethod.getEleIfHave(this.maxObj,"cn","");
      }
      
      public function getMaxName() : String
      {
         return ObjectMethod.getEleIfHave(this.maxObj,"name","");
      }
   }
}

