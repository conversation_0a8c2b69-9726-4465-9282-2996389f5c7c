package dataAll.arms.creator
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.arms.creator.evo.BeforeArmsEvoCtrl;
   import dataAll.arms.creator.evo.CateEvoCtrl;
   import dataAll.arms.creator.evo.ChristmasGunEvoCtrl;
   import dataAll.arms.creator.evo.DarkgoldArmsEvoCtrl;
   import dataAll.arms.creator.evo.DiyArmsEvoCtrl;
   import dataAll.arms.creator.evo.SkyArchEvoCtrl;
   import dataAll.arms.creator.evo.YearEvoCtrl;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.define.EquipColor;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   import gameAll.arms.GameArmsCtrl;
   
   public class ArmsEvoCtrl
   {
      
      private static const skyArch:SkyArchEvoCtrl = new SkyArchEvoCtrl();
      
      private static const rocketCate:CateEvoCtrl = new CateEvoCtrl();
      
      private static const christmasGun:ChristmasGunEvoCtrl = new ChristmasGunEvoCtrl();
      
      private static const year:YearEvoCtrl = new YearEvoCtrl();
      
      private static const darkgold:DarkgoldArmsEvoCtrl = new DarkgoldArmsEvoCtrl();
      
      public static const darkgoldSkillArr:Array = ["fear_godArmsSkill","booby_godArmsSkill","fox_godArmsSkill","sickle_godArmsSkill","redFireSkill","addFlamer_ArmsSkill"];
      
      private static const hurtMulArr:Array = [90,100,110,145,155,165,180,195,205,220,230,240,250].concat([300,340]).concat([380]);
      
      private static const cnArr:Array = ["$","$II","$III","闪耀·$","闪耀·$II","闪耀·$III","闪耀·$IV","绝世·$","绝世·$II","超凡·$","超凡·$II","超凡·$III","超凡·$IV"].concat(["无双·$","无双·$II"]).concat(["氩星·$"]);
      
      public function ArmsEvoCtrl()
      {
         super();
      }
      
      public static function get darkgoldLv() : int
      {
         return 10;
      }
      
      public static function get purgoldLv() : int
      {
         return 14;
      }
      
      public static function get yagoldLv() : int
      {
         return 16;
      }
      
      public static function getDiy(name0:String) : DiyArmsEvoCtrl
      {
         var diy0:DiyArmsEvoCtrl = ArmsEvoCtrl[name0];
         if(Boolean(diy0))
         {
            return diy0;
         }
         if(YearEvoCtrl.nameArr.indexOf(name0) >= 0)
         {
            return year;
         }
         if(DarkgoldArmsEvoCtrl.nameArr.indexOf(name0) >= 0)
         {
            return darkgold;
         }
         return null;
      }
      
      public static function getFirstCn(evoLv0:int, d0:ArmsDefine) : String
      {
         var s0:String = getCnName("",evoLv0,d0);
         if(s0.indexOf("·") == s0.length - 1)
         {
            s0 = s0.substr(0,s0.length - 1);
         }
         return s0;
      }
      
      public static function getCnName(cn0:String, evoLv0:int, d0:ArmsDefine, diyB0:Boolean = true) : String
      {
         var diy0:DiyArmsEvoCtrl = null;
         if(diyB0)
         {
            diy0 = getDiy(d0.name);
            if(Boolean(diy0))
            {
               return diy0.getCnName(cn0,evoLv0,d0);
            }
         }
         var index0:int = evoLv0 - 1;
         if(evoLv0 + d0.evoMustFirstLv >= darkgoldLv)
         {
            index0 = evoLv0 + d0.evoMustFirstLv - 1;
         }
         if(d0.evoMustFirstLv >= purgoldLv - 1 && index0 + 1 < yagoldLv)
         {
            index0 = evoLv0 - 1;
         }
         var arr0:Array = cnArr;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return String(arr0[index0]).replace("$",cn0);
      }
      
      public static function getHurtMul(evoLv0:int, d0:ArmsDefine, diyB0:Boolean = true) : Number
      {
         var diy0:DiyArmsEvoCtrl = null;
         var index0:int = 0;
         var arr0:Array = null;
         var v0:Number = NaN;
         if(diyB0)
         {
            diy0 = getDiy(d0.name);
            if(Boolean(diy0))
            {
               return diy0.getHurtMul(evoLv0,d0);
            }
         }
         if(d0.isCanEvoB())
         {
            index0 = evoLv0 - 1 + d0.evoMustFirstLv;
            arr0 = hurtMulArr;
            if(index0 < 0)
            {
               index0 = 0;
            }
            if(index0 > arr0.length - 1)
            {
               index0 = arr0.length - 1;
            }
            v0 = arr0[index0] / 100;
            if(d0.color == EquipColor.PURGOLD && d0.evoMaxLv > 1)
            {
               if(index0 <= 13)
               {
                  v0 *= 0.8;
               }
            }
            return v0;
         }
         return 1;
      }
      
      private static function getColor(evoLv0:int, d0:ArmsDefine) : String
      {
         var color0:String = d0.color;
         var panLv0:int = evoLv0 + d0.evoMustFirstLv;
         if(panLv0 >= ArmsEvoCtrl.yagoldLv)
         {
            color0 = EquipColor.YAGOLD;
         }
         else if(panLv0 >= ArmsEvoCtrl.purgoldLv)
         {
            color0 = EquipColor.PURGOLD;
         }
         else if(panLv0 >= ArmsEvoCtrl.darkgoldLv)
         {
            color0 = EquipColor.DARKGOLD;
         }
         return color0;
      }
      
      public static function getAfterData(da0:ArmsData) : ArmsData
      {
         var affter_s0:ArmsSave = null;
         var affter_da0:ArmsData = null;
         var diy0:DiyArmsEvoCtrl = null;
         if(!isMaxB(da0))
         {
            affter_s0 = da0.save.copy();
            affter_da0 = new ArmsData();
            affter_da0.inData_bySave(affter_s0,da0.normalPlayerData,true,da0.fatherData as ArmsDataGroup);
            diy0 = getDiy(da0.def.name);
            if(Boolean(diy0))
            {
               diy0.doEvo(affter_da0);
            }
            else
            {
               affter_s0.doEvo();
               affter_s0.armsImgLabel = getArmsImageName(da0.def,affter_s0.evoLv);
            }
            GameArmsCtrl.addArmsSaveResoure(affter_s0);
            affter_da0.fleshData_byMeEquip();
            return affter_da0;
         }
         return null;
      }
      
      public static function getArmsImageName(d0:ArmsDefine, evoLv0:int) : String
      {
         var diy0:DiyArmsEvoCtrl = getDiy(d0.name);
         if(Boolean(diy0))
         {
            return diy0.getArmsImageName(d0,evoLv0);
         }
         return getArmsImageNameOne(d0,evoLv0);
      }
      
      public static function getArmsImageNameOne(d0:ArmsDefine, evoLv0:int) : String
      {
         var bodyUrl0:String = null;
         var back0:String = "";
         var allEvoLv0:int = evoLv0 + d0.evoMustFirstLv;
         if(allEvoLv0 >= yagoldLv)
         {
            back0 = "6";
         }
         else if(allEvoLv0 >= purgoldLv)
         {
            back0 = "5";
         }
         else if(allEvoLv0 >= darkgoldLv)
         {
            back0 = "4";
         }
         else if(evoLv0 >= 8)
         {
            back0 = "3";
         }
         else if(evoLv0 >= 4)
         {
            back0 = "2";
         }
         if(back0 != "")
         {
            bodyUrl0 = d0.getFirstBodyImgUrl(back0);
            if(Gaming.swfLoaderManager.haveResourceFull(bodyUrl0) == false)
            {
               back0 = "";
            }
         }
         return d0.getImageLabelByBodySuffix(back0);
      }
      
      public static function evo(da0:ArmsData) : void
      {
         var s0:ArmsSave = da0.save;
         var diy0:DiyArmsEvoCtrl = getDiy(da0.def.name);
         if(Boolean(diy0))
         {
            diy0.doEvo(da0);
         }
         else
         {
            s0.doEvo();
            s0.armsImgLabel = getArmsImageName(da0.def,s0.evoLv);
         }
         s0.lockB = true;
         GameArmsCtrl.addArmsSaveResoure(s0);
         da0.fleshData_byMeEquip();
      }
      
      private static function haveEvoLv(d0:ArmsDefine, evoLv0:int) : Boolean
      {
         var diy0:DiyArmsEvoCtrl = getDiy(d0.name);
         if(Boolean(diy0))
         {
            return diy0.haveEvoLv(d0,evoLv0);
         }
         return true;
      }
      
      public static function getMaxLv(armsD0:ArmsDefine) : int
      {
         var lv0:int = armsD0.evoMaxLv;
         var max0:int = 16;
         if(lv0 > max0)
         {
            lv0 = max0;
         }
         return lv0;
      }
      
      public static function canEvoB(armsD0:ArmsDefine, evoLv0:int) : Boolean
      {
         if(armsD0.isCanEvoB())
         {
            if(evoLv0 < getMaxLv(armsD0))
            {
               return true;
            }
         }
         return false;
      }
      
      public static function isMaxB(da0:ArmsData) : Boolean
      {
         return isMaxBBy(da0.def,da0.save.evoLv);
      }
      
      public static function isMaxBBy(armsD0:ArmsDefine, evoLv0:int) : Boolean
      {
         if(evoLv0 >= getMaxLv(armsD0))
         {
            return true;
         }
         return false;
      }
      
      private static function getChipMustNum(evoLv0:int) : int
      {
         var arr0:Array = [50,60,70,80,100,120,150,200,200];
         var index0:int = evoLv0 - 1;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0];
      }
      
      public static function getAllChipMustNum(evoLv0:int) : int
      {
         var num0:Number = 0;
         for(var i:int = 1; i <= evoLv0; i++)
         {
            num0 += getChipMustNum(i);
         }
         return num0;
      }
      
      private static function getRadiumMustNum(evoLv0:int) : int
      {
         var arr0:Array = [0,120,140,160,200,240,280,320,320];
         var index0:int = evoLv0 - 1;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0];
      }
      
      public static function getAllRadiumMustNum(evoLv0:int) : int
      {
         var num0:Number = 0;
         for(var i:int = 1; i <= evoLv0; i++)
         {
            num0 += getRadiumMustNum(i);
         }
         return num0;
      }
      
      private static function getTitaniumMustNum(evoLv0:int) : int
      {
         var arr0:Array = [0,180,210,240,300,360,420,480,480];
         var index0:int = evoLv0 - 1;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0];
      }
      
      private static function getErosionMustNum(evoLv0:int) : int
      {
         var arr0:Array = [0,0,0,0,0,0,80,150];
         var index0:int = evoLv0 - 1;
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > arr0.length - 1)
         {
            index0 = arr0.length - 1;
         }
         return arr0[index0];
      }
      
      public static function getMust(da0:ArmsData, diyB0:Boolean = true) : MustDefine
      {
         var diy0:DiyArmsEvoCtrl = null;
         var name0:String = da0.def.name;
         if(diyB0)
         {
            diy0 = getDiy(name0);
            if(Boolean(diy0))
            {
               return diy0.getMust(da0.save.evoLv,da0.def);
            }
         }
         return getMustBy(da0.def,da0.save.evoLv);
      }
      
      private static function getMustAndDiy(armsD0:ArmsDefine, evoLv0:int) : MustDefine
      {
         var name0:String = armsD0.name;
         var diy0:DiyArmsEvoCtrl = getDiy(name0);
         if(Boolean(diy0))
         {
            return diy0.getMust(evoLv0,armsD0);
         }
         return getMustBy(armsD0,evoLv0);
      }
      
      public static function getMustBy(armsD0:ArmsDefine, evoLv0:int) : MustDefine
      {
         var mustLv0:int = evoLv0 + 1 + armsD0.evoMustFirstLv;
         if(mustLv0 >= darkgoldLv)
         {
            return getDarkgoldMustBy(armsD0,evoLv0);
         }
         var name0:String = armsD0.name;
         var d0:MustDefine = new MustDefine();
         d0.lv = 86;
         d0.coin = Gaming.defineGroup.normal.getLevelCoinIncome(d0.lv);
         var thingArr0:Array = [];
         var chip0:Number = getChipMustNum(mustLv0) * getChipMustMul(armsD0);
         var r0:Number = getRadiumMustNum(mustLv0);
         var t0:Number = getTitaniumMustNum(mustLv0);
         var e0:Number = getErosionMustNum(mustLv0);
         if(chip0 > 0)
         {
            thingArr0.push(armsD0.getChipName() + ";" + chip0);
         }
         if(r0 > 0)
         {
            thingArr0.push("armsRadium;" + r0);
         }
         if(t0 > 0)
         {
            thingArr0.push("armsTitanium;" + t0);
         }
         if(e0 > 0)
         {
            if(mustLv0 == 7)
            {
               thingArr0.push("erosionFlamer;" + e0);
            }
            else if(mustLv0 == 8)
            {
               thingArr0.push("AircraftGunCash;" + e0);
            }
         }
         if(mustLv0 == 9)
         {
            thingArr0.push("rocketCate;100");
         }
         d0.inThingsDataByArr(thingArr0);
         return d0;
      }
      
      private static function getChipMustMul(armsD0:ArmsDefine) : Number
      {
         if(armsD0.name == "lightCone")
         {
            return 1.5;
         }
         return 1;
      }
      
      private static function getDarkgoldMustBy(armsD0:ArmsDefine, evoLv0:int) : MustDefine
      {
         var d0:MustDefine = null;
         var chip0:Number = NaN;
         var numMul0:Number = NaN;
         var typeArr0:Array = null;
         var gemNameArr0:Array = null;
         var gemName0:String = null;
         var mustLv0:int = evoLv0 + 1 + armsD0.evoMustFirstLv;
         var thingArr0:Array = [];
         var name0:String = armsD0.name;
         if(name0 == "rifleHornet")
         {
            thingArr0 = rifleHornetDarkgoldMust(mustLv0);
         }
         else if(name0 == "sniperCicada")
         {
            thingArr0 = sniperCicadaDarkgoldMust(mustLv0);
         }
         else if(name0 == "shotgunSkunk")
         {
            thingArr0 = shotgunSkunkDarkgoldMust(mustLv0);
         }
         else if(name0 == "pistolFox")
         {
            thingArr0 = pistolFoxDarkgoldMust(mustLv0);
         }
         else if(name0 == "redFire")
         {
            thingArr0 = redFireDarkgoldMust(mustLv0);
         }
         else if(name0 == "meltFlamer")
         {
            thingArr0 = meltFlamerDarkgoldMust(mustLv0);
         }
         else if(name0 == "lightCone")
         {
            thingArr0 = lightConeDarkgoldMust(mustLv0);
         }
         else if(name0 == "consVirgo")
         {
            thingArr0 = consVirgoMust(mustLv0);
         }
         else
         {
            chip0 = 200;
            if(mustLv0 == 10)
            {
               chip0 = 300;
            }
            if(chip0 > 0)
            {
               thingArr0.push(armsD0.getChipName() + ";" + chip0);
            }
            numMul0 = 1.5;
            typeArr0 = [ArmsType.sniper];
            if(typeArr0.indexOf(armsD0.armsType) >= 0)
            {
               numMul0 = 1;
            }
            gemNameArr0 = getDarkgoldMustGemArr(armsD0);
            for each(gemName0 in gemNameArr0)
            {
               if(gemName0 == gemNameArr0[0])
               {
                  thingArr0.push(gemName0 + ";" + 200 * numMul0);
               }
               else
               {
                  thingArr0.push(gemName0 + ";" + 50 * numMul0);
               }
            }
         }
         d0 = new MustDefine();
         d0.lv = 90;
         d0.inThingsDataByArr(thingArr0);
         return d0;
      }
      
      private static function rifleHornetDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["poisonGem;200","yearDog;50","yearSnake;50","yearHourse;30"];
         }
         if(mustLv0 == 11)
         {
            arr0 = ["rifleHornet;200","electricGem;100","poisonGem;200","frozenGem;100"];
         }
         if(mustLv0 == 12)
         {
            arr0 = ["rifleHornet;200","electricGem;100","poisonGem;200","yearPig;70"];
         }
         if(mustLv0 == 13)
         {
            arr0 = ["rifleHornet;200","electricGem;100","poisonGem;200","yearCattle;60"];
         }
         if(mustLv0 == 14)
         {
            arr0 = ["rifleHornet;300","demBall;300","demStone;285"];
         }
         if(mustLv0 == 15)
         {
            arr0 = ["zodiacCash;50","demBall;400","demStone;350"];
         }
         if(mustLv0 == 16)
         {
            arr0 = ["yaStone;100"];
         }
         return arr0;
      }
      
      private static function sniperCicadaDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10 || mustLv0 == 11)
         {
            arr0 = ["sniperCicada;200","electricGem;200","fireGem;50","frozenGem;50"];
         }
         if(mustLv0 == 12)
         {
            arr0 = ["sniperCicada;200","electricGem;200","yearDog;50","yearMonkey;35"];
         }
         if(mustLv0 == 13)
         {
            arr0 = ["sniperCicada;200","electricGem;200","yearDog;100","yearMonkey;40"];
         }
         if(mustLv0 == 14)
         {
            arr0 = ["sniperCicada;300","demBall;300","demStone;285"];
         }
         if(mustLv0 == 15)
         {
            arr0 = ["zodiacCash;50","demBall;400","demStone;350"];
         }
         if(mustLv0 == 16)
         {
            arr0 = ["yaStone;100"];
         }
         return arr0;
      }
      
      private static function shotgunSkunkDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["shotgunSkunk;200","frozenGem;200","electricGem;75","fireGem;75"];
         }
         if(mustLv0 == 11)
         {
            arr0 = ["shotgunSkunk;200","frozenGem;200","electricGem;75","fireGem;75"];
         }
         if(mustLv0 == 12 || mustLv0 == 13)
         {
            arr0 = ["shotgunSkunk;200","frozenGem;200","yearHourse;60","yearMonkey;60"];
         }
         if(mustLv0 == 14)
         {
            arr0 = ["shotgunSkunk;300","demBall;300","demStone;285"];
         }
         if(mustLv0 == 15)
         {
            arr0 = ["zodiacCash;50","demBall;400","demStone;350"];
         }
         if(mustLv0 == 16)
         {
            arr0 = ["yaStone;100"];
         }
         return arr0;
      }
      
      private static function pistolFoxDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["pistolFox;200","fireGem;200","poisonGem;75","frozenGem;75"];
         }
         if(mustLv0 == 11 || mustLv0 == 12)
         {
            arr0 = ["pistolFox;200","fireGem;200","poisonGem;100","yearHourse;49"];
         }
         if(mustLv0 == 13)
         {
            arr0 = ["pistolFox;200","fireGem;200","yearCattle;30","pianoGun;20"];
         }
         if(mustLv0 == 14)
         {
            arr0 = ["pistolFox;300","demBall;300","demStone;285"];
         }
         if(mustLv0 == 15)
         {
            arr0 = ["zodiacCash;50","demBall;400","demStone;350"];
         }
         if(mustLv0 == 16)
         {
            arr0 = ["yaStone;100"];
         }
         return arr0;
      }
      
      private static function redFireDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["redFire;200","fireGem;200","electricGem;100","yearMouse;20"];
         }
         if(mustLv0 == 11 || mustLv0 == 12 || mustLv0 == 13)
         {
            arr0 = ["redFire;200","fireGem;200","yearSnake;50","yearMouse;20"];
         }
         if(mustLv0 == 14)
         {
            arr0 = ["redFire;300","demBall;310","demStone;310"];
         }
         if(mustLv0 == 15)
         {
            arr0 = ["zodiacCash;50","demBall;400","demStone;350"];
         }
         if(mustLv0 == 16)
         {
            arr0 = ["yaStone;100","roamRocket;90"];
         }
         return arr0;
      }
      
      private static function meltFlamerDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["meltFlamer;200","poisonGem;200","frozenGem;100","yearRabbit;30"];
         }
         if(mustLv0 == 11)
         {
            arr0 = ["meltFlamer;200","poisonGem;200","yearSnake;50","yearRabbit;30"];
         }
         if(mustLv0 == 12 || mustLv0 == 13)
         {
            arr0 = ["meltFlamer;200","poisonGem;200","yearCattle;30","pianoGun;20"];
         }
         if(mustLv0 == 14)
         {
            arr0 = ["meltFlamer;300","demBall;310","demStone;310"];
         }
         if(mustLv0 == 15)
         {
            arr0 = ["zodiacCash;50","demBall;400","demStone;350"];
         }
         if(mustLv0 == 16)
         {
            arr0 = ["yaStone;140"];
         }
         return arr0;
      }
      
      private static function lightConeDarkgoldMust(mustLv0:int) : Array
      {
         var arr0:Array = ["poisonGem;9999999"];
         if(mustLv0 == 10)
         {
            arr0 = ["lightCone;300","electricGem;200","yearCattle;30","pianoGun;20"];
         }
         if(mustLv0 == 11)
         {
            arr0 = ["lightCone;200","electricGem;200","yearCattle;30","pianoGun;20"];
         }
         if(mustLv0 == 12)
         {
            arr0 = ["lightCone;200","electricGem;200","yearCattle;30","pianoGun;20"];
         }
         if(mustLv0 == 13)
         {
            arr0 = ["lightCone;200","electricGem;200","yearCattle;30","pianoGun;20"];
         }
         if(mustLv0 == 14)
         {
            arr0 = ["lightCone;300","demBall;310","demStone;310","starTrails;100"];
         }
         return arr0;
      }
      
      private static function consVirgoMust(mustLv0:int) : Array
      {
         var arr0:Array = ["yaStone;9999999"];
         if(mustLv0 == 15)
         {
            arr0 = ["consVirgo;270","zodiacCash;100","demBall;400","demStone;300"];
         }
         return arr0;
      }
      
      private static function getDarkgoldMustGemArr(armsD0:ArmsDefine) : Array
      {
         var type0:String = armsD0.armsType;
         if(type0 == ArmsType.sniper)
         {
            return ["electricGem","fireGem","frozenGem"];
         }
         if(type0 == ArmsType.shotgun)
         {
            return ["frozenGem","electricGem","fireGem"];
         }
         if(type0 == ArmsType.pistol)
         {
            return ["fireGem","frozenGem","poisonGem"];
         }
         return ["electricGem","fireGem","frozenGem"];
      }
      
      public static function doEvoPurgold(s0:ArmsSave) : void
      {
         var armsD0:ArmsDefine = s0.getArmsRangeDefine().def;
         var name0:String = armsD0.name;
         if(name0 == "rifleHornet")
         {
            ArrayMethod.addNoRepeatInArr(s0.godSkillArr,"noFoggyDef");
         }
         else if(name0 == "sniperCicada")
         {
            ArrayMethod.addNoRepeatInArr(s0.godSkillArr,"immuneNemesis");
         }
         else if(name0 == "shotgunSkunk")
         {
            ArrayMethod.replace(s0.godSkillArr,"booby_godArmsSkill2","boobyPurgold");
            ArrayMethod.addNoRepeatInArr(s0.godSkillArr,"purgoldSkunk");
         }
         else if(name0 == "pistolFox")
         {
            ArrayMethod.addNoRepeatInArr(s0.godSkillArr,"purgoldFox");
         }
         else if(name0 == "redFire")
         {
            ArrayMethod.addNoRepeatInArr(s0.godSkillArr,"redFireDem");
         }
         else if(name0 == "meltFlamer")
         {
            ArrayMethod.addNoRepeatInArr(s0.godSkillArr,"meltFlamerPurgold");
         }
         else if(name0 == "meltFlamer")
         {
            ArrayMethod.addNoRepeatInArr(s0.godSkillArr,"meltFlamerPurgold");
         }
         else if(name0 == "lightCone")
         {
            ArrayMethod.addNoRepeatInArr(s0.godSkillArr,"lightConePurgold");
         }
         else if(name0 == "rocketCate")
         {
            rocketCate.doEvoPurgold(s0);
         }
      }
      
      public static function yagoldEvo(s0:ArmsSave) : void
      {
         s0.s = "";
      }
      
      public static function getAllEvoTip(da0:ArmsData) : String
      {
         var cnName0:String = null;
         var hurtAdd0:Number = NaN;
         var color0:String = null;
         var colorStr0:String = null;
         var mustTip0:String = null;
         var chipName0:String = null;
         var chipCn0:String = null;
         var must0:MustDefine = null;
         var s0:String = "";
         var evoLv0:int = 1;
         var d0:ArmsDefine = da0.def;
         for(var i:int = 0; i < 99; i++)
         {
            if(isMaxBBy(d0,evoLv0 - 1))
            {
               break;
            }
            if(haveEvoLv(d0,evoLv0))
            {
               cnName0 = getCnName(d0.cnName,evoLv0,d0);
               hurtAdd0 = getHurtMul(evoLv0,d0);
               color0 = getColor(evoLv0,d0);
               colorStr0 = EquipColor.htmlColor(color0);
               mustTip0 = "";
               if(evoLv0 <= 1)
               {
                  if(d0.haveChipB())
                  {
                     chipName0 = d0.getChipName();
                     chipCn0 = Gaming.defineGroup.things.getCn(chipName0);
                     mustTip0 = chipCn0 + "*" + d0.chipNum;
                  }
                  else
                  {
                     mustTip0 = "无";
                  }
               }
               else
               {
                  must0 = getMustAndDiy(d0,evoLv0 - 1);
                  mustTip0 = must0.getThingsMustTip();
               }
               if(da0.getEvoLv() == evoLv0)
               {
                  s0 += "<green √/>";
               }
               else if(da0.getEvoLv() + 1 == evoLv0)
               {
                  s0 += "<yellow ◆/>";
               }
               else
               {
                  s0 += " ";
               }
               s0 += "<b>" + ComMethod.color(cnName0,colorStr0) + "</b>";
               s0 += "<gray2 [" + NumberMethod.toPer(hurtAdd0,0) + "]/>：" + mustTip0 + "\n";
            }
            evoLv0++;
         }
         return s0;
      }
      
      public static function countEvoMore(da0:ArmsData, traceB0:Boolean = false) : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         var evoLv0:int = 0;
         var now0:MustDefine = null;
         var before0:MustDefine = null;
         var nowG0:GiftAddDefineGroup = null;
         var beforeG0:GiftAddDefineGroup = null;
         var oneg0:GiftAddDefine = null;
         var cnName0:String = null;
         var str0:String = null;
         var testB0:Boolean = traceB0 || Gaming.testCtrl.canCheatingB();
         var nowEvoLv0:int = da0.save.evoLv;
         var d0:ArmsDefine = da0.def;
         var maxLv0:int = getMaxLv(d0);
         if(nowEvoLv0 < 0 || nowEvoLv0 > maxLv0)
         {
            return null;
         }
         if(d0.isCanEvoB() && nowEvoLv0 > 1)
         {
            g0 = new GiftAddDefineGroup();
            for(evoLv0 = 1; evoLv0 < nowEvoLv0; evoLv0++)
            {
               if(evoLv0 + d0.evoMustFirstLv < 12)
               {
                  now0 = getMustBy(d0,evoLv0);
                  before0 = BeforeArmsEvoCtrl.getMustBy(d0,evoLv0);
                  nowG0 = new GiftAddDefineGroup();
                  nowG0.inMustDefineOnlyThings(now0);
                  beforeG0 = new GiftAddDefineGroup();
                  beforeG0.inMustDefineOnlyThings(before0);
                  beforeG0.deduct(nowG0);
                  for each(oneg0 in beforeG0.arr)
                  {
                     if(oneg0.num > 400)
                     {
                        oneg0.num = 1;
                     }
                  }
                  g0.merge(beforeG0);
                  if(testB0 && beforeG0.arr.length > 0)
                  {
                     cnName0 = evoLv0 + 1 + "：" + getCnName(d0.cnName,evoLv0 + 1,d0);
                     str0 = cnName0 + "  " + beforeG0.getDescription(99,false);
                     trace(str0);
                  }
               }
            }
            if(testB0 && g0.arr.length > 0)
            {
               trace("---------------一共返还：" + g0.getDescription(99,false));
            }
            return g0;
         }
         trace("---------------没有返还");
         return null;
      }
      
      public static function traceAllMustReduce_da() : void
      {
         var rd0:ArmsRangeDefine = null;
         var d0:ArmsDefine = null;
         var s0:ArmsSave = null;
         var da0:ArmsData = null;
         var g0:GiftAddDefineGroup = null;
         var darr0:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
         for each(rd0 in darr0)
         {
            d0 = rd0.def;
            if(d0.isCanEvoB())
            {
               s0 = Gaming.defineGroup.armsCreator.getBlackSave(d0.composeLv,d0);
               da0 = new ArmsData();
               da0.inData_bySave(s0,Gaming.PG.da);
               s0.evoLv = getMaxLv(da0.def) - da0.def.evoMustFirstLv;
               trace("evoLv:" + s0.evoLv + " " + d0.cnName + "  --------------------------------------------");
               g0 = countEvoMore(da0,true);
            }
         }
      }
      
      public static function traceAllMustReduce() : void
      {
         var rd0:ArmsRangeDefine = null;
         var d0:ArmsDefine = null;
         var evoLv0:int = 0;
         var i:int = 0;
         var must0:MustDefine = null;
         var before0:MustDefine = null;
         var cnName0:String = null;
         var nowG0:GiftAddDefineGroup = null;
         var beforeG0:GiftAddDefineGroup = null;
         var str0:String = null;
         var darr0:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
         for each(rd0 in darr0)
         {
            d0 = rd0.def;
            if(d0.isCanEvoB())
            {
               trace("--------------------------------------------");
               evoLv0 = 1;
               for(i = 0; i < 99; i++)
               {
                  if(isMaxBBy(d0,evoLv0))
                  {
                     break;
                  }
                  if(evoLv0 + d0.evoMustFirstLv < 12)
                  {
                     must0 = getMustBy(d0,evoLv0);
                     before0 = BeforeArmsEvoCtrl.getMustBy(d0,evoLv0);
                     cnName0 = getCnName(d0.cnName,evoLv0 + 1,d0);
                     nowG0 = new GiftAddDefineGroup();
                     nowG0.inMustDefineOnlyThings(must0);
                     beforeG0 = new GiftAddDefineGroup();
                     beforeG0.inMustDefineOnlyThings(before0);
                     beforeG0.deduct(nowG0);
                     str0 = cnName0 + "  " + beforeG0.getDescription(99,false);
                     trace(str0);
                  }
                  evoLv0++;
               }
            }
         }
      }
      
      public static function traceAllMust() : void
      {
         var rd0:ArmsRangeDefine = null;
         var d0:ArmsDefine = null;
         var evoLv0:int = 0;
         var i:int = 0;
         var must0:MustDefine = null;
         var cnName0:String = null;
         var gift0:GiftAddDefineGroup = null;
         var str0:String = null;
         var darr0:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
         for each(rd0 in darr0)
         {
            d0 = rd0.def;
            if(d0.isCanEvoB())
            {
               trace("--------------------------------------------");
               evoLv0 = 1;
               for(i = 0; i < 99; i++)
               {
                  if(isMaxBBy(d0,evoLv0))
                  {
                     break;
                  }
                  must0 = getMustBy(d0,evoLv0);
                  cnName0 = getCnName(d0.cnName,evoLv0 + 1,d0);
                  gift0 = new GiftAddDefineGroup();
                  gift0.inMustDefineOnlyThings(must0);
                  str0 = cnName0 + "  " + gift0.getDescription(99,false);
                  trace(str0);
                  evoLv0++;
               }
            }
         }
      }
      
      public static function traceAllMust_da() : void
      {
         var rd0:ArmsRangeDefine = null;
         var d0:ArmsDefine = null;
         var s0:ArmsSave = null;
         var da0:ArmsData = null;
         var i:int = 0;
         var must0:MustDefine = null;
         var gift0:GiftAddDefineGroup = null;
         var str0:String = null;
         var darr0:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
         for each(rd0 in darr0)
         {
            d0 = rd0.def;
            if(d0.isCanEvoB())
            {
               trace("--------------------------------------------");
               s0 = Gaming.defineGroup.armsCreator.getBlackSave(d0.composeLv,d0);
               da0 = new ArmsData();
               da0.inData_bySave(s0,Gaming.PG.da);
               for(i = 0; i < 99; i++)
               {
                  if(isMaxB(da0))
                  {
                     break;
                  }
                  must0 = getMust(da0);
                  da0.save.doEvo();
                  gift0 = new GiftAddDefineGroup();
                  gift0.inMustDefineOnlyThings(must0);
                  str0 = da0.getCnName() + "  " + gift0.getDescription(99,false);
                  trace(str0);
               }
            }
         }
      }
   }
}

