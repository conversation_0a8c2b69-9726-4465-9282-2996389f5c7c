package com.sounto.cf
{
   public class ObjectToXml
   {
      
      public function ObjectToXml()
      {
         super();
      }
      
      public static function decode(obj0:Object) : XML
      {
         return toXML(obj0,"null");
      }
      
      public static function decode4399(obj0:Object) : XML
      {
         var xml0:XML = <saveXml type="Object" game4399="true"><ignoreWhitespace>true</ignoreWhitespace></saveXml>;
         xml0.prependChild(toXML(obj0,"null"));
         return xml0;
      }
      
      public static function decode4399Two(obj0:Object) : XML
      {
         var xml0:XML = decode4399(obj0);
         var obj2:Object = encode4399(xml0);
         return decode4399(obj2);
      }
      
      private static function toXML(a:*, n:String) : XML
      {
         var ax:XML = new XML(<s></s>);
         var v:* = null;
         var type0:String = "";
         if(a is Boolean)
         {
            type0 = "Boolean";
            v = Boolean(a).toString();
         }
         else if(a is Number || a is int)
         {
            type0 = "Number";
            v = String(a);
         }
         else if(a is String)
         {
            type0 = "String";
            v = String(a);
         }
         else if(a is Array)
         {
            type0 = "Array";
            v = arrToXMLlist(a);
         }
         else if(a is Object)
         {
            type0 = "Object";
            v = objToXMLlist(a);
         }
         ax.@type = type0;
         ax.@name = n;
         ax.appendChild(v);
         return ax;
      }
      
      private static function objToXMLlist(obj0:Object) : XMLList
      {
         var n:* = undefined;
         var bx:XML = null;
         var ax:XML = new XML(<s></s>);
         for(n in obj0)
         {
            bx = toXML(obj0[n],n);
            ax.appendChild(bx);
         }
         return ax.s;
      }
      
      private static function arrToXMLlist(arr0:Array) : XMLList
      {
         var n:* = undefined;
         var bx:XML = null;
         var ax:XML = new XML(<s></s>);
         for(n in arr0)
         {
            bx = toXML(arr0[n],"null");
            ax.prependChild(bx);
         }
         return ax.s;
      }
      
      public static function encode(xml0:XML) : Object
      {
         return toObj(xml0);
      }
      
      public static function encode4399(xml0:XML) : Object
      {
         return toObj(xml0)["null"];
      }
      
      private static function toObj(x:XML) : *
      {
         var v:* = null;
         var type0:String = x.@type;
         if(type0 == "Boolean")
         {
            v = String(x) == "true" ? true : false;
         }
         else if(type0 == "Number")
         {
            v = Number(String(x));
         }
         else if(type0 == "String")
         {
            v = String(x);
         }
         else if(type0 == "Array")
         {
            v = xmllistToArray(x.s);
         }
         else if(type0 == "Object")
         {
            v = xmllistToObj(x.s);
         }
         return v;
      }
      
      private static function xmllistToObj(xl:XMLList) : Object
      {
         var n:* = undefined;
         var x:XML = null;
         var v:* = undefined;
         var name0:String = null;
         var b:Object = {};
         for(n in xl)
         {
            x = xl[n];
            v = toObj(x);
            name0 = x.@name;
            b[name0] = v;
         }
         return b;
      }
      
      private static function xmllistToArray(xl:XMLList) : Array
      {
         var n:* = undefined;
         var x:XML = null;
         var v:* = undefined;
         var b:Array = [];
         for(n in xl)
         {
            x = xl[n];
            v = toObj(x);
            b[n] = v;
         }
         return b;
      }
   }
}

