package com.sounto.oldUtils
{
   import com.common.text.TextWay;
   
   public class StringDate
   {
      
      public var fullYear:int = 0;
      
      public var month:int = 0;
      
      public var date:int = 0;
      
      public var hours:int = 0;
      
      public var minutes:int = 0;
      
      public var seconds:int = 0;
      
      public function StringDate(str0:String = "")
      {
         super();
         this.inData_byStr(str0);
      }
      
      public static function isZero(time0:String) : Boolean
      {
         return time0 == "" || time0 == "0-01-00 00:00:00";
      }
      
      public static function compareDateByStr(str1:String, str2:String) : int
      {
         var s1:StringDate = new StringDate(str1);
         var s2:StringDate = new StringDate(str2);
         return s1.compareDate(s2);
      }
      
      public static function converToCnWeek(day0:int) : int
      {
         return (day0 + 6) % 7 + 1;
      }
      
      public static function getLocalTimeStr() : String
      {
         var date0:Date = new Date();
         var d0:StringDate = new StringDate();
         d0.inData_byObj(date0);
         return d0.getStr();
      }
      
      public static function getDayByTimeValue(v0:Number) : Number
      {
         return Math.floor(v0 / 1000 / 3600 / 24);
      }
      
      public static function testGetWeekData() : void
      {
         var str0:String = null;
         var now0:StringDate = null;
         var j:int = 0;
         var c0:StringDate = null;
         for(var i:int = 1; i <= 14; i++)
         {
            str0 = "2016-10-" + TextWay.toNum(i + "",2);
            now0 = new StringDate(str0);
            trace("日期：  " + str0 + "  -------------------------");
            for(j = 1; j <= 7; j++)
            {
               c0 = now0.getWeekData(j);
               trace(j + "：" + c0.getDateStr() + "     day：" + c0.getDateClass().getDay());
            }
         }
      }
      
      public function inData_byStr(str0:String) : *
      {
         var arr0:Array = null;
         var arr1:Array = null;
         var arr2:Array = null;
         this.init();
         if(str0 != "")
         {
            arr0 = str0.split(" ");
            arr1 = arr0[0].split("-");
            this.fullYear = int(arr1[0]);
            this.month = int(arr1[1] - 1);
            this.date = int(arr1[2]);
            if(Boolean(arr0[1]))
            {
               arr2 = arr0[1].split(":");
               this.hours = int(arr2[0]);
               this.minutes = int(arr2[1]);
               this.seconds = int(arr2[2]);
            }
         }
      }
      
      public function inData_byObj(obj:Object) : *
      {
         var n:* = undefined;
         var pro0:String = null;
         var pro_arr:Array = ["fullYear","month","date","hours","minutes","seconds"];
         for(n in pro_arr)
         {
            pro0 = pro_arr[n];
            this[pro0] = obj[pro0];
         }
      }
      
      public function inTimeValue(v0:Number) : void
      {
         var date0:Date = new Date(v0);
         this.inData_byObj(date0);
      }
      
      public function copy() : StringDate
      {
         var sd:StringDate = new StringDate();
         sd.inData_byObj(this);
         return sd;
      }
      
      public function init() : *
      {
         this.fullYear = 0;
         this.month = 0;
         this.date = 0;
         this.hours = 0;
         this.minutes = 0;
         this.seconds = 0;
      }
      
      public function initTimeKeepDate() : void
      {
         this.hours = 0;
         this.minutes = 0;
         this.seconds = 0;
      }
      
      public function getStr() : String
      {
         return this.getDateStr() + " " + this.getTimeStr();
      }
      
      public function getDateStr() : String
      {
         return this.fullYear + "-" + this.get2(this.month + 1) + "-" + this.get2(this.date);
      }
      
      public function getMonthDayStr() : String
      {
         return this.get2(this.month + 1) + "-" + this.get2(this.date);
      }
      
      public function getTimeStr() : String
      {
         return this.get2(this.hours) + ":" + this.get2(this.minutes) + ":" + this.get2(this.seconds);
      }
      
      public function getDateClass() : Date
      {
         return new Date(this.fullYear,this.month,this.date,this.hours,this.minutes,this.seconds);
      }
      
      public function getOnlyDateClass() : Date
      {
         return new Date(this.fullYear,this.month,this.date,0,0,0,0);
      }
      
      public function toString() : String
      {
         return this.getStr();
      }
      
      private function get2(num0:int) : String
      {
         var str0:String = String(num0);
         if(str0.length == 1)
         {
            str0 = "0" + str0;
         }
         return str0;
      }
      
      public function compareDate(sd2:StringDate) : int
      {
         var da0:StringDate = this.copy();
         da0.initTimeKeepDate();
         var da2:StringDate = sd2.copy();
         da2.initTimeKeepDate();
         var d0:Date = da0.getOnlyDateClass();
         var d2:Date = da2.getOnlyDateClass();
         var ct:Number = d2.getTime() - d0.getTime();
         return Math.round(ct / 1000 / 3600 / 24);
      }
      
      public function reductionOne(sd0:StringDate) : int
      {
         return sd0.compareDate(this);
      }
      
      public function reductionOneStr(str0:String) : int
      {
         var sd0:StringDate = new StringDate(str0);
         return this.reductionOne(sd0);
      }
      
      public function compareDateValue(sd0:StringDate) : Number
      {
         var d0:Date = this.getDateClass();
         var d1:Date = sd0.getDateClass();
         var ct:Number = d1.getTime() - d0.getTime();
         return ct / 1000 / 3600 / 24;
      }
      
      public function betweenIn(startTime0:String, endTime0:String, endLastPanB0:Boolean = false) : int
      {
         if(endLastPanB0)
         {
            if(endTime0.indexOf(":") == -1)
            {
               endTime0 += " 23:59:59";
            }
         }
         var s0:StringDate = new StringDate(startTime0);
         var e0:StringDate = new StringDate(endTime0);
         var start0:Number = Number(s0.getDateClass().getTime());
         if(startTime0 == "")
         {
            start0 = 0;
         }
         var end0:Number = Number(e0.getDateClass().getTime());
         if(endTime0 == "")
         {
            end0 = 99999999999;
         }
         var now0:Number = Number(this.getDateClass().getTime());
         if(now0 < start0)
         {
            return -1;
         }
         if(now0 >= start0 && now0 <= end0)
         {
            return 0;
         }
         return 1;
      }
      
      public function getBetweenTip(startTime0:String, endTime0:String, title0:String = "活动时间：") : String
      {
         var b0:int = this.betweenIn(startTime0,endTime0);
         var s0:String = ComMethod.color("\n" + title0 + startTime0 + "至" + endTime0,"#FFFF00");
         if(b0 == -1)
         {
            s0 += ComMethod.color("(未开始)","#FF0000");
         }
         if(b0 == 1)
         {
            s0 += ComMethod.color("(已结束)","#FF0000");
         }
         return s0;
      }
      
      public function addDay(v0:int) : StringDate
      {
         var da0:Date = this.getDateClass();
         var da2:Date = new Date(da0.time + v0 * 1000 * 60 * 60 * 24);
         this.inData_byObj(da2);
         return this;
      }
      
      public function isWeekendB() : Boolean
      {
         var da0:Date = this.getOnlyDateClass();
         if(da0.getDay() == 0 || da0.getDay() == 6)
         {
            return true;
         }
         return false;
      }
      
      public function getWeekData(day0:int) : StringDate
      {
         day0 = converToCnWeek(day0);
         var da0:Date = this.getOnlyDateClass();
         var nowDay0:int = converToCnWeek(da0.day);
         var cday0:int = nowDay0 - day0;
         da0.setTime(da0.getTime() - cday0 * 24 * 1000 * 3600);
         var sd0:StringDate = new StringDate();
         sd0.inData_byObj(da0);
         return sd0;
      }
   }
}

