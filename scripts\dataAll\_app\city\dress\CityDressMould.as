package dataAll._app.city.dress
{
   import dataAll._app.city.define.CityBodyDefine;
   
   public class CityDressMould
   {
      
      public var uiIndex:int = 0;
      
      private var def:CityBodyDefine = null;
      
      private var maker:IO_CityDressMouldMaker = null;
      
      private var addNum:int = 0;
      
      public function CityDressMould()
      {
         super();
      }
      
      public function inDataByDefine(d0:CityBodyDefine, uiIndex0:int) : void
      {
         this.def = d0;
         this.maker = null;
         this.uiIndex = uiIndex0;
      }
      
      public function getDefine() : CityBodyDefine
      {
         return this.def;
      }
      
      public function inDataByMaker(maker0:IO_CityDressMouldMaker, uiIndex0:int) : void
      {
         this.def = Gaming.defineGroup.cityBody.getDefine(maker0.getCityDressType());
         this.maker = maker0;
         this.uiIndex = uiIndex0;
      }
      
      public function getId() : String
      {
         if(Boolean(this.maker))
         {
            return this.maker.getId();
         }
         return this.def.name;
      }
      
      public function getCn() : String
      {
         if(Boolean(this.maker))
         {
            return this.maker.getCnName();
         }
         return this.def.cnName;
      }
      
      public function getDressType() : String
      {
         if(Boolean(this.maker))
         {
            return this.maker.getCityDressType();
         }
         return CityDressType.decora;
      }
      
      public function getShadowUrl() : String
      {
         var type0:String = this.getDressType();
         var url0:String = this.def.shadow;
         if(type0 == CityDressType.vehicle)
         {
            url0 = type0;
         }
         else if(type0 == CityDressType.player)
         {
            url0 = "unit";
         }
         else if(type0 == CityDressType.pet)
         {
            url0 = "unit";
         }
         if(url0 != "")
         {
            return "CityUI/" + url0 + "Shadow";
         }
         return "";
      }
      
      public function getUseSpace() : int
      {
         var space0:int = this.def.space;
         if(space0 < 1)
         {
            space0 = 1;
         }
         return space0;
      }
      
      public function canUseB() : Boolean
      {
         if(this.canRepeatB())
         {
            return true;
         }
         return this.addNum <= 0;
      }
      
      private function canRepeatB() : Boolean
      {
         return !this.maker;
      }
      
      public function clearAddNum() : void
      {
         this.addNum = 0;
      }
      
      public function addAddNum() : void
      {
         ++this.addNum;
      }
      
      public function getAddNum() : int
      {
         return this.addNum;
      }
      
      public function getIconUrl() : String
      {
         if(Boolean(this.maker))
         {
            return this.maker.getIconUrl();
         }
         return this.def.iconUrl;
      }
      
      public function getImgUrl() : String
      {
         if(Boolean(this.maker))
         {
            return this.maker.getCityDressImgUrl();
         }
         return this.def.imgUrl;
      }
   }
}

