package dataAll._app.space
{
   public class SpaceLevelCount
   {
      
      public var exp:Number = 0;
      
      public var enemy:Number = 0;
      
      public function SpaceLevelCount()
      {
         super();
      }
      
      public function clearData() : void
      {
         this.exp = 0;
         this.enemy = 0;
      }
      
      public function getStar(time0:Number) : int
      {
         if(time0 < 100)
         {
            return 5;
         }
         if(time0 < 150)
         {
            return 4;
         }
         if(time0 < 200)
         {
            return 3;
         }
         if(time0 < 250)
         {
            return 2;
         }
         return 1;
      }
   }
}

