package dataAll.pet.gene.save
{
   import com.sounto.utils.ClassProperty;
   import dataAll.items.save.ItemsSaveGroup;
   
   public class GeneSaveGroup extends ItemsSaveGroup
   {
      
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = [];
      
      public function GeneSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObjAndClass(obj0,GeneSave);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
      }
      
      public function clone() : GeneSaveGroup
      {
         var dg0:GeneSaveGroup = new GeneSaveGroup();
         dg0.inData_byObj(this);
         return dg0;
      }
   }
}

