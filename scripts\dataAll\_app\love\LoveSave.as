package dataAll._app.love
{
   import com.sounto.oldUtils.OldCodeCF;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.love.define.LoveLevelDefine;
   import dataAll._app.love.define.LoveRoleAll;
   import dataAll._player.PlayerData;
   
   public class LoveSave
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      private var codeCF:OldCodeCF = new OldCodeCF();
      
      private var _value:Number = 0;
      
      public var getGirlGiftB:Boolean = false;
      
      public var getGirlGiftB8:Boolean = false;
      
      public var talkArr:Array = [];
      
      public var likeHateArr:Array = [];
      
      public var likeName:String = "";
      
      public var hateName:String = "";
      
      public var fashionAddTime:String = "";
      
      public function LoveSave()
      {
         super();
         this.value = 0;
         this.todayGivingNum = 0;
         this.buyGivingNum = 0;
         this.allGivingNum = 0;
         this.dieNum = 0;
         this.showNum = 0;
         this.hideNum = 0;
         this.allGiftNum = 0;
         this.likeGiftNum = 0;
         this.hateGiftNum = 0;
         this.beforeGetGiftTimeStr = "";
      }
      
      public function get value() : Number
      {
         return this.CF.getAttribute("value");
      }
      
      public function set value(v0:Number) : void
      {
         this.CF.setAttribute("value",v0);
         this._value = v0;
      }
      
      public function get todayGivingNum() : Number
      {
         return this.CF.getAttribute("todayGivingNum");
      }
      
      public function set todayGivingNum(v0:Number) : void
      {
         this.CF.setAttribute("todayGivingNum",v0);
      }
      
      public function get buyGivingNum() : Number
      {
         return this.CF.getAttribute("buyGivingNum");
      }
      
      public function set buyGivingNum(v0:Number) : void
      {
         this.CF.setAttribute("buyGivingNum",v0);
      }
      
      public function get allGivingNum() : Number
      {
         return this.CF.getAttribute("allGivingNum");
      }
      
      public function set allGivingNum(v0:Number) : void
      {
         this.CF.setAttribute("allGivingNum",v0);
      }
      
      public function get beforeGetGiftTimeStr() : String
      {
         return this.codeCF.getAttribute("beforeGetGiftTimeStr");
      }
      
      public function set beforeGetGiftTimeStr(str0:String) : void
      {
         this.codeCF.setAttribute("beforeGetGiftTimeStr",str0);
      }
      
      public function get dieNum() : Number
      {
         return this.CF.getAttribute("dieNum");
      }
      
      public function set dieNum(v0:Number) : void
      {
         this.CF.setAttribute("dieNum",v0);
      }
      
      public function get showNum() : Number
      {
         return this.CF.getAttribute("showNum");
      }
      
      public function set showNum(v0:Number) : void
      {
         this.CF.setAttribute("showNum",v0);
      }
      
      public function get hideNum() : Number
      {
         return this.CF.getAttribute("hideNum");
      }
      
      public function set hideNum(v0:Number) : void
      {
         this.CF.setAttribute("hideNum",v0);
      }
      
      public function get allGiftNum() : Number
      {
         return this.CF.getAttribute("allGiftNum");
      }
      
      public function set allGiftNum(v0:Number) : void
      {
         this.CF.setAttribute("allGiftNum",v0);
      }
      
      public function get likeGiftNum() : Number
      {
         return this.CF.getAttribute("likeGiftNum");
      }
      
      public function set likeGiftNum(v0:Number) : void
      {
         this.CF.setAttribute("likeGiftNum",v0);
      }
      
      public function get hateGiftNum() : Number
      {
         return this.CF.getAttribute("hateGiftNum");
      }
      
      public function set hateGiftNum(v0:Number) : void
      {
         this.CF.setAttribute("hateGiftNum",v0);
      }
      
      public function initSave() : void
      {
         this.value = 700;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.likeHateArr = ClassProperty.copySaveArray(obj0["likeHateArr"],LoveLikeSave);
      }
      
      public function newDayCtrl(defAll0:LoveRoleAll, pd0:PlayerData) : void
      {
         this.getGirlGiftB = false;
         this.getGirlGiftB8 = false;
         this.buyGivingNum = 0;
         this.todayGivingNum = 0;
         this.likeName = "";
         this.hateName = "";
         this.likeHateArr.shift();
         this.suppleLikeHateArr(defAll0);
         this.fleshTalkArrByValue(defAll0,pd0);
      }
      
      public function getFastValue() : Number
      {
         return this._value;
      }
      
      public function getGiftB(d0:LoveLevelDefine) : Boolean
      {
         if(d0.lv == 8)
         {
            return this.getGirlGiftB8;
         }
         return this.getGirlGiftB;
      }
      
      public function setGiftB(d0:LoveLevelDefine, bb0:Boolean) : void
      {
         if(d0.lv == 8)
         {
            this.getGirlGiftB8 = bb0;
         }
         else
         {
            this.getGirlGiftB = bb0;
         }
      }
      
      public function getGiftAllB() : Boolean
      {
         return this.getGirlGiftB && this.getGirlGiftB;
      }
      
      public function setGiftAllB(bb0:Boolean) : void
      {
         this.getGirlGiftB8 = bb0;
         this.getGirlGiftB = bb0;
      }
      
      public function setGiftBByLvArr(lvArr0:Array, bb0:Boolean) : void
      {
         var d0:LoveLevelDefine = null;
         for each(d0 in lvArr0)
         {
            this.setGiftB(d0,bb0);
         }
      }
      
      public function buyNum(num0:int) : void
      {
         this.buyGivingNum += num0;
      }
      
      public function useGivingNum() : void
      {
         ++this.todayGivingNum;
      }
      
      public function getCanGivingNum() : int
      {
         return this.getGivingMaxNum() + this.buyGivingNum - this.todayGivingNum;
      }
      
      public function getGivingMaxNum() : int
      {
         return 1;
      }
      
      public function getBuyMaxNum() : int
      {
         return 10;
      }
      
      public function getCanBuyNum() : int
      {
         return this.getBuyMaxNum() - this.buyGivingNum;
      }
      
      public function addValue(v0:Number, defAll0:LoveRoleAll, pd0:PlayerData) : void
      {
         var beforeD0:LoveLevelDefine = defAll0.getLevelDefineByValue(this.value);
         var now0:int = this.value;
         now0 += v0;
         if(now0 < 0)
         {
            now0 = 0;
         }
         if(now0 > this.getMaxLoveValue())
         {
            now0 = this.getMaxLoveValue();
         }
         this.value = now0;
         var nowD0:LoveLevelDefine = defAll0.getLevelDefineByValue(this.value);
         if(nowD0.lv != beforeD0.lv)
         {
            this.fleshTalkArrByValue(defAll0,pd0);
         }
      }
      
      public function getMaxLoveValue() : int
      {
         return 24100;
      }
      
      private function suppleLikeHateArr(defAll0:LoveRoleAll) : void
      {
         var i:int = 0;
         var likeSave0:LoveLikeSave = null;
         var t0:int = 3 - this.likeHateArr.length;
         if(t0 > 0)
         {
            for(i = 0; i < t0; i++)
            {
               likeSave0 = new LoveLikeSave();
               likeSave0.random(defAll0);
               this.likeHateArr.push(likeSave0);
            }
         }
      }
      
      private function fleshTalkArrByValue(defAll0:LoveRoleAll, pd0:PlayerData) : void
      {
         var d0:LoveLevelDefine = defAll0.getLevelDefineByValue(this.value);
         this.talkArr = defAll0.getRandomNameArrByType(d0.getTalkName(defAll0),3);
      }
   }
}

