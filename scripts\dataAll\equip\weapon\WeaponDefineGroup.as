package dataAll.equip.weapon
{
   import dataAll.equip.define.EquipDefine;
   
   public class WeaponDefineGroup
   {
      
      private var nowIndex:int = 0;
      
      public var obj:Object = {};
      
      public var fatherArrObj:Object = {};
      
      public var arr:Array = [];
      
      private var oneNameArr:Array = [];
      
      private var dropNameArr:Array = [];
      
      public var baseNameArr:Array = [];
      
      private var dropBaseNameArr:Array = [];
      
      public var actionLabelArr:Array = ["swordAfterAttack","ghost_swordAttack","snow_daggerAttack","great_stickAttack","fox_dartsAttack"];
      
      public function WeaponDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var x0:XML = null;
         var father0:String = null;
         var xl2:XMLList = null;
         var x2:XML = null;
         var baseLabel0:String = null;
         var d0:WeaponDefine = null;
         var dropB0:Boolean = false;
         var baseB0:Boolean = false;
         var xl0:XMLList = xml0.father;
         for each(x0 in xl0)
         {
            father0 = x0.@name;
            xl2 = x0.equip;
            for each(x2 in xl2)
            {
               baseLabel0 = x2.@baseLabel;
               d0 = this.getDefine(x2.@baseLabel + "_1");
               dropB0 = false;
               baseB0 = false;
               if(d0 is WeaponDefine)
               {
                  d0 = d0.cloneBase();
               }
               else
               {
                  d0 = new WeaponDefine();
                  this.oneNameArr.push(baseLabel0 + "_1");
                  this.baseNameArr.push(baseLabel0);
                  dropB0 = true;
                  baseB0 = true;
               }
               d0.inData_byXML(x2,father0);
               this.obj[d0.name] = d0;
               this.arr.push(d0);
               this.addInFatherArr(d0,father0);
               if(dropB0 && !d0.rareB)
               {
                  this.dropNameArr.push(x2.@baseLabel + "_1");
                  this.dropBaseNameArr.push(baseLabel0);
               }
               if(baseB0)
               {
                  this.actionLabelArr.push(d0.actionLabel);
                  this.actionLabelArr.push("sec_" + d0.actionLabel);
                  Gaming.defineGroup.dropItems.inWeaponDefine(d0);
               }
            }
         }
      }
      
      private function addInFatherArr(d0:EquipDefine, father0:String) : void
      {
         d0.index = this.nowIndex;
         ++this.nowIndex;
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
         }
         this.fatherArrObj[father0].push(d0);
      }
      
      public function getDefine(name0:String) : WeaponDefine
      {
         return this.obj[name0];
      }
      
      public function getNormalBaseNameArr() : Array
      {
         return this.dropBaseNameArr;
      }
      
      public function getRandomDropName() : String
      {
         return this.dropNameArr[int(Math.random() * this.dropNameArr.length)];
      }
      
      public function getWeaponDefineByCn(cn0:String) : WeaponDefine
      {
         var d0:WeaponDefine = null;
         for each(d0 in this.arr)
         {
            if(d0.cnName == cn0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function isWeaponActionLabel(label0:String) : Boolean
      {
         return this.actionLabelArr.indexOf(label0) >= 0;
      }
      
      public function getIconNum() : int
      {
         var d0:EquipDefine = null;
         var obj0:Object = {};
         var num0:int = 0;
         for each(d0 in this.obj)
         {
            if(!obj0.hasOwnProperty(d0.iconLabel))
            {
               obj0[d0.iconLabel] = 0;
               num0++;
            }
         }
         return num0;
      }
   }
}

