package dataAll.pet
{
   import com.sounto.net.SWFLoaderUrl;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import dataAll._player.PlayerData;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.add.EquipAddChild;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.add.IO_EquipAddTipGetter;
   import dataAll.pet.dispatch.PetDispatchData;
   import dataAll.pet.gene.GeneData;
   import gameAll.body.IO_NormalBody;
   
   public class PetDataGroup implements IO_EquipAddTipGetter, IO_EquipAddGetter
   {
      
      public var saveGroup:PetSaveGroup;
      
      public var PD:PlayerData;
      
      public var arr:Array = [];
      
      public var dispatch:PetDispatchData = new PetDispatchData();
      
      public var tempAddData:Object = new Object();
      
      private var tempTipObj:Object = null;
      
      public function PetDataGroup()
      {
         super();
         this.dispatch.setDataGroup(this);
      }
      
      public static function getZeroAddData() : Object
      {
         return {
            "dps":0,
            "life":0
         };
      }
      
      public function setPlayerData(pd0:PlayerData) : void
      {
         this.PD = pd0;
      }
      
      public function inData_bySaveGroup(sg0:PetSaveGroup) : void
      {
         var s0:PetSave = null;
         var da0:PetData = null;
         this.saveGroup = sg0;
         this.dispatch.inData_bySave(sg0.dispatch);
         this.arr.length = 0;
         for each(s0 in sg0.arr)
         {
            da0 = new PetData();
            da0.inData_bySave(s0,this.PD);
            if(s0.base.id == "")
            {
               s0.base.setIdBy(this.saveGroup.getIdIndexAdd());
            }
            this.arr.push(da0);
         }
      }
      
      public function fleshSaveGroup() : void
      {
         var da0:PetData = null;
         var arr2:Array = [];
         for each(da0 in this.arr)
         {
            arr2.push(da0.save);
         }
         this.saveGroup.arr = arr2;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.saveGroup.newDayCtrl(timeStr0);
      }
      
      public function getGeneArr() : Array
      {
         var da0:PetData = null;
         var arr0:Array = [];
         for each(da0 in this.arr)
         {
            arr0.push(da0.gene);
         }
         return arr0;
      }
      
      public function getCanTransferArrByDa(da0:PetData) : Array
      {
         var da2:PetData = null;
         var arr0:Array = [];
         var lv0:int = da0.base.save.level;
         for each(da2 in this.arr)
         {
            if(da2.base.save.level == lv0 && da0 != da2)
            {
               arr0.push(da2);
            }
         }
         return arr0;
      }
      
      public function addByGeneData(petDa0:GeneData) : PetData
      {
         var da0:PetData = null;
         if(this.getSpaceNum() == 0)
         {
            return null;
         }
         da0 = new PetData();
         da0.initData_byGeneData(petDa0,this.PD);
         this.addData(da0);
         return da0;
      }
      
      private function addData(da0:PetData) : void
      {
         da0.save.base.setIdBy(this.saveGroup.getIdIndexAdd());
         this.arr.push(da0);
         this.fleshSaveGroup();
      }
      
      public function clearData() : void
      {
         this.arr.length = 0;
         this.fleshSaveGroup();
      }
      
      public function overGamingClear() : void
      {
         var da0:PetData = null;
         for each(da0 in this.arr)
         {
            da0.setTempBody(null);
         }
      }
      
      public function giveUpData(da0:PetData) : void
      {
         var index0:int = int(this.arr.indexOf(da0));
         if(index0 >= 0)
         {
            this.arr.splice(index0,1);
         }
         this.fleshSaveGroup();
      }
      
      public function getDataById(id0:String) : PetData
      {
         var da0:PetData = null;
         for each(da0 in this.arr)
         {
            if(da0.getId() == id0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getFightPetDataArr() : Array
      {
         var da0:PetData = null;
         var arr0:Array = [];
         for each(da0 in this.arr)
         {
            if(da0.getState() == PetState.FIGHT)
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function getSupplePetDataArr() : Array
      {
         var da0:PetData = null;
         var arr0:Array = [];
         for each(da0 in this.arr)
         {
            if(da0.getState() == PetState.SUPPLE)
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function getFightAndSupplePetDataArr() : Array
      {
         var da0:PetData = null;
         var arr0:Array = [];
         for each(da0 in this.arr)
         {
            if(da0.getState() == PetState.FIGHT || da0.getState() == PetState.SUPPLE)
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function getFightAndSuppleNameArr() : Array
      {
         var da0:PetData = null;
         var nameArr0:Array = [];
         var arr0:Array = this.getFightAndSupplePetDataArr();
         for each(da0 in arr0)
         {
            nameArr0.push(da0.gene.getBodyName());
         }
         return nameArr0;
      }
      
      public function getFightAndSuppleBodyArr() : Array
      {
         var da0:PetData = null;
         var bodyArr0:Array = [];
         var arr0:Array = this.getFightAndSupplePetDataArr();
         for each(da0 in arr0)
         {
            if(Boolean(da0.tempBody))
            {
               bodyArr0.push(da0.tempBody);
            }
         }
         return bodyArr0;
      }
      
      public function getArrByPetName(name0:String) : Array
      {
         var da0:PetData = null;
         var arr2:Array = [];
         for each(da0 in this.arr)
         {
            if(da0.gene.save.name == name0)
            {
               arr2.push(da0);
            }
         }
         return arr2;
      }
      
      public function getBagSizeText() : String
      {
         return ComMethod.color("(" + this.arr.length + "/" + this.saveGroup.lockLen + ")","#00FF00");
      }
      
      public function getLivePetBodyArr() : Array
      {
         var b0:IO_NormalBody = null;
         var arr2:Array = [];
         var arr0:Array = this.getFightAndSuppleBodyArr();
         for each(b0 in arr0)
         {
            if(b0.getDie() == 0)
            {
               arr2.push(b0);
            }
         }
         return arr2;
      }
      
      public function getBestPetData(sortType0:String) : PetData
      {
         var arr0:Array = null;
         if(this.arr.length == 0)
         {
            return null;
         }
         arr0 = this.arr.concat([]);
         arr0.sort(sortType0 == "dps" ? this.dpsSortFun : this.lifeSortFun);
         return arr0[0];
      }
      
      private function dpsSortFun(da1:PetData, da2:PetData) : int
      {
         var dps1:Number = da1.base.getDps();
         var dps2:Number = da2.base.getDps();
         if(dps1 > dps2)
         {
            return -1;
         }
         if(dps1 < dps2)
         {
            return 1;
         }
         return 0;
      }
      
      private function lifeSortFun(da1:PetData, da2:PetData) : int
      {
         var dps1:Number = da1.base.getMaxLife();
         var dps2:Number = da2.base.getMaxLife();
         if(dps1 > dps2)
         {
            return -1;
         }
         if(dps1 < dps2)
         {
            return 1;
         }
         return 0;
      }
      
      public function toppingData(da0:PetData) : void
      {
         var bb0:Boolean = ArrayMethod.delOneInArr(this.arr,da0);
         if(bb0)
         {
            this.arr.unshift(da0);
            this.fleshSaveGroup();
         }
      }
      
      public function getSWFLoaderUrlArr() : Array
      {
         var da0:PetData = null;
         var body_d0:NormalBodyDefine = null;
         var body_url0:SWFLoaderUrl = null;
         var arr0:Array = [];
         for each(da0 in this.arr)
         {
            body_d0 = da0.getBodyDefine();
            body_url0 = new SWFLoaderUrl();
            body_url0.url = body_d0.swfUrl;
            body_url0.label = body_d0.getSwfName();
            body_url0.info = body_d0.cnName;
            arr0.push(body_url0);
         }
         return arr0;
      }
      
      public function getSpaceNum() : int
      {
         return this.saveGroup.lockLen - this.arr.length;
      }
      
      public function addBagNum(num0:int) : void
      {
         this.saveGroup.lockLen += num0;
      }
      
      public function setOneState(da2:PetData, state0:String) : void
      {
         var da0:PetData = null;
         if(this.arr.indexOf(da2) == -1)
         {
            INIT.showError("尸宠数据不在arr中");
         }
         else if(state0 == PetState.DEFENCE)
         {
            da2.setState(PetState.DEFENCE);
         }
         else
         {
            for each(da0 in this.arr)
            {
               if(da0 == da2)
               {
                  da0.setState(state0);
               }
               else if(da0.getState() == state0)
               {
                  da0.setState(PetState.DEFENCE);
               }
            }
         }
      }
      
      public function getAddData() : Object
      {
         var da0:PetData = null;
         var dps0:Number = NaN;
         var life0:Number = NaN;
         var addObj0:Object = null;
         var tipObj0:Object = {};
         var add_da0:Object = getZeroAddData();
         var arr0:Array = this.getFightPetDataArr();
         for each(da0 in arr0)
         {
            dps0 = Math.ceil(da0.base.getDps(false) / 8);
            life0 = Math.ceil(da0.base.getMaxLife(false) / 8);
            add_da0.dps += dps0;
            add_da0.life += life0;
            addObj0 = {
               "dps":dps0,
               "life":life0
            };
            EquipAddChild.addTipInObj(addObj0,tipObj0,da0.getCnName());
         }
         this.tempAddData = add_da0;
         this.tempTipObj = tipObj0;
         return add_da0;
      }
      
      public function getProAddObj() : Object
      {
         return this.getAddData();
      }
      
      public function getProAddMaxObj(pro0:String) : Object
      {
         return null;
      }
      
      public function getProAddTipObj() : Object
      {
         return this.tempTipObj;
      }
      
      public function dealRed496() : void
      {
         var da0:PetData = null;
         var bb0:Boolean = false;
         for each(da0 in this.arr)
         {
            bb0 = da0.gene.save.dealRed496();
            if(bb0)
            {
               da0.fleshProData();
            }
         }
      }
   }
}

