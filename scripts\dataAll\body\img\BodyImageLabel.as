package dataAll.body.img
{
   public class BodyImageLabel
   {
      
      public static const showAttack:String = "showAttack";
      
      public static const disappearAttack:String = "disappearAttack";
      
      public static const stand:String = "stand";
      
      public static const run:String = "run";
      
      public static const move:String = "move";
      
      public static const squat:String = "squat";
      
      public static const jump:String = "jump";
      
      public static const fill:String = "fill";
      
      public static const hurt:String = "hurt";
      
      public static const die:String = "die";
      
      public static const stru:String = "stru";
      
      public static const Skill:String = "Skill";
      
      public static const Attack:String = "Attack";
      
      public static const Ride:String = "Ride";
      
      public static const baseAllowArr:Array = [hurt,die,Attack,stru,Ride];
      
      public static const heroAllowArr:Array = [fill,hurt,die,Attack,stru,Ride];
      
      public static const normalAllowArr:Array = [fill,hurt,die,stru];
      
      public static const Stop:String = "Stop";
      
      public static const Back:String = "Back";
      
      public static const Forward:String = "Forward";
      
      public static const Down:String = "Down";
      
      public static const Up:String = "Up";
      
      public static const __jumpUp:String = "__jumpUp";
      
      public static const jumpDown__:String = "jumpDown__";
      
      public static const __fill:String = "__fill";
      
      public static const standStop__squatStop:String = "standStop__squatStop";
      
      public static const squatStop__standStop:String = "squatStop__standStop";
      
      public static const standForward:String = "standForward";
      
      public static const standBack:String = "standBack";
      
      public static const squatForward:String = "squatForward";
      
      public static const squatBack:String = "squatBack";
      
      public static const walkArr:Array = [move,run,standForward,standBack,squatForward,squatBack];
      
      public static const walkEffectArr:Array = walkArr.concat(jumpDown__);
      
      public function BodyImageLabel()
      {
         super();
      }
      
      public static function isAttackB(label0:String) : Boolean
      {
         if(label0.indexOf(Attack) >= 0)
         {
            return true;
         }
         return false;
      }
   }
}

