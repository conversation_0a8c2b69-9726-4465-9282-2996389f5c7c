package dataAll.items
{
   import com.sounto.oldUtils.StringDate;
   import dataAll._player.PlayerData;
   import dataAll._player.io.IO_HavePlayerData;
   import dataAll._player.more.MoreData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipColor;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.define.IO_ResolveItemsDefine;
   import dataAll.items.save.ItemsSave;
   import dataAll.skill.HeroSkillData;
   import dataAll.things.ThingsData;
   
   public class NormalItemsData implements IO_HavePlayerData
   {
      
      protected var fatherData:ItemsDataGroup = null;
      
      public var placeType:String = "";
      
      public var tempSortId:String = "";
      
      public var hidingB:Boolean = false;
      
      private var _isChosen:Boolean = false;
      
      private var _newB:Boolean = false;
      
      public var playerData:PlayerData;
      
      public var normalPlayerData:NormalPlayerData;
      
      public function NormalItemsData()
      {
         super();
      }
      
      public function set isChosen(bb0:Boolean) : void
      {
         this._isChosen = bb0;
      }
      
      public function get isChosen() : Boolean
      {
         return this._isChosen;
      }
      
      public function set newB(bb0:Boolean) : void
      {
         this._newB = bb0;
      }
      
      public function get newB() : Boolean
      {
         return this._newB;
      }
      
      public function setFatherData(dg0:ItemsDataGroup) : void
      {
         this.fatherData = dg0;
      }
      
      public function getFatherData() : ItemsDataGroup
      {
         return this.fatherData;
      }
      
      public function setPlayerData(pd0:NormalPlayerData) : void
      {
         this.normalPlayerData = pd0;
         this.playerData = this.normalPlayerData as PlayerData;
      }
      
      public function setAllPlayerData(pd0:NormalPlayerData) : void
      {
         this.setPlayerData(pd0);
      }
      
      public function getPlayerData() : NormalPlayerData
      {
         return this.normalPlayerData;
      }
      
      public function setPlaceType(type0:String) : void
      {
         this.placeType = type0;
      }
      
      public function getPlaceType() : String
      {
         return this.placeType;
      }
      
      public function getDataType() : String
      {
         if(this is EquipData)
         {
            return ItemsDataGroup.TYPE_EQUIP;
         }
         if(this is ThingsData)
         {
            return ItemsDataGroup.TYPE_THINGS;
         }
         if(this is HeroSkillData)
         {
            return ItemsDataGroup.TYPE_SKILL;
         }
         if(this is MoreData)
         {
            return ItemsDataGroup.TYPE_MORE;
         }
         return "";
      }
      
      public function isCanNumSwapB() : Boolean
      {
         return false;
      }
      
      public function isCanOverlayB() : Boolean
      {
         return false;
      }
      
      public function getNowNum() : int
      {
         return 1;
      }
      
      public function setNowNum(num0:int) : void
      {
      }
      
      public function addNowNum(num0:int, otherDa0:IO_ItemsData = null) : void
      {
      }
      
      protected function addNowTrueNum(num0:int, otherDa0:IO_ItemsData = null) : void
      {
         var t0:String = null;
         var m0:String = null;
         var ts0:StringDate = null;
         var ms0:StringDate = null;
         if(this.getSave().hasOwnProperty("nowNum"))
         {
            this.getSave()["nowNum"] = this.getSave()["nowNum"] + num0;
            if(Boolean(otherDa0))
            {
               t0 = this.getSave().getSeverTime();
               m0 = otherDa0.getSave().getSeverTime();
               if(m0 != "")
               {
                  ts0 = new StringDate(t0);
                  ms0 = new StringDate(m0);
                  if(ts0.reductionOne(ms0) < 0)
                  {
                     this.getSave().setSeverTime(m0);
                  }
               }
            }
         }
      }
      
      public function getTempSortId() : String
      {
         return this.tempSortId;
      }
      
      public function setTempSortId(str0:String) : void
      {
         this.tempSortId = str0;
      }
      
      public function toOneSortId(str0:String) : void
      {
         this.tempSortId = "";
      }
      
      public function isArenaGiftB() : Boolean
      {
         return false;
      }
      
      public function getAllUpgradeConverStoneNum() : int
      {
         return 0;
      }
      
      public function setHidingB(bb0:Boolean) : void
      {
         this.hidingB = bb0;
      }
      
      public function getHidingB() : Boolean
      {
         return this.hidingB;
      }
      
      public function canStrengthenB() : Boolean
      {
         return false;
      }
      
      public function canStrengthenMoveB() : Boolean
      {
         return false;
      }
      
      public function canRefiningB() : Boolean
      {
         return false;
      }
      
      public function getResolveItemsDefine() : IO_ResolveItemsDefine
      {
         return null;
      }
      
      public function getResolveGift() : GiftAddDefineGroup
      {
         var d0:IO_ResolveItemsDefine = this.getResolveItemsDefine();
         if(Boolean(d0))
         {
            return d0.getResolveGift();
         }
         return null;
      }
      
      public function canResolveB() : Boolean
      {
         var d0:IO_ResolveItemsDefine = this.getResolveItemsDefine();
         if(Boolean(d0))
         {
            return d0.canResolveB();
         }
         return false;
      }
      
      public function dealBtnListCn(label0:String) : String
      {
         return "";
      }
      
      public function getSave() : ItemsSave
      {
         return null;
      }
      
      public function getColor() : String
      {
         var s0:ItemsSave = this.getSave();
         if(Boolean(s0))
         {
            return s0.color;
         }
         return EquipColor.WHITE;
      }
   }
}

