package dataAll.pet.gene.creator
{
   import UI.bag.ItemsGripBtnListCtrl;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.setting.SettingSave;
   import dataAll._player.PlayerData;
   import dataAll.drop.BodyDropData;
   import dataAll.items.ItemsBatchColor;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.GeneDataGroup;
   import dataAll.pet.gene.save.GeneSave;
   
   public class GeneDataDecompose
   {
      
      private static var nowDa:GeneData;
      
      private static var nowDg:GeneDataGroup;
      
      public function GeneDataDecompose()
      {
         super();
      }
      
      public static function getDecomposeName() : String
      {
         return "strengthenDrug";
      }
      
      public static function decomposeOne(da0:GeneData, dg0:GeneDataGroup) : void
      {
         if(da0.save.getLockB())
         {
            Gaming.uiGroup.alertBox.showError("已锁定基因体不能被分解！");
            return;
         }
         nowDa = da0;
         nowDg = dg0;
         var num0:int = getStrengthenDrug(da0);
         var str0:String = "分解" + da0.getCnName() + "可以获得：\n" + ComMethod.color("强化剂x" + num0,"#00FF00");
         Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",yes_decomposeOne);
      }
      
      private static function yes_decomposeOne() : void
      {
         var da0:GeneData = nowDa;
         var dg0:GeneDataGroup = nowDg;
         var drugNum0:int = getStrengthenDrug(da0);
         dg0.removeData(da0);
         dg0.playerData.thingsBag.addDataByName(getDecomposeName(),drugNum0);
         Gaming.uiGroup.alertBox.showCheck("分解成功！请到物品背包查看。","",0.25,null,null,"yes","icon");
         ItemsGripBtnListCtrl.fleshAllBy(dg0,false);
      }
      
      public static function decomposeDataGroup(dg0:GeneDataGroup, stateArr0:Array) : void
      {
         var arr0:Array = dg0.getBatchSellDataArr(stateArr0);
         decomposeArr(arr0,dg0.playerData);
      }
      
      private static function decomposeArr(arr0:Array, pd0:PlayerData) : void
      {
         if(arr0.length == 0)
         {
            Gaming.uiGroup.alertBox.showCheck("没找到指定条件的基因体。","",0.25);
            return;
         }
         var drugNum0:int = getStrengthenDrugArr(arr0);
         pd0.geneBag.removeDataArr(arr0);
         pd0.thingsBag.addDataByName(getDecomposeName(),drugNum0);
         Gaming.uiGroup.alertBox.showCheck("分解成功！请到物品背包查看。","",0.25,null,null,"yes","icon");
         ItemsGripBtnListCtrl.fleshAllBy(pd0.geneBag,false);
      }
      
      private static function getStrengthenDrugArr(arr0:Array) : int
      {
         var da0:GeneData = null;
         var num0:int = 0;
         for each(da0 in arr0)
         {
            num0 += getStrengthenDrug(da0);
         }
         return num0;
      }
      
      private static function getStrengthenDrug(da0:GeneData) : int
      {
         var color0:String = da0.getColor();
         return getStrengthenDrugBy(color0);
      }
      
      private static function getStrengthenDrugBy(color0:String) : int
      {
         if(color0 == "red")
         {
            return 12;
         }
         if(color0 == "orange")
         {
            return 8;
         }
         if(color0 == "purple")
         {
            return 4;
         }
         if(color0 == "blue")
         {
            return 2;
         }
         return 1;
      }
      
      public static function autoDecomposePan(s0:GeneSave, bd0:BodyDropData) : Boolean
      {
         var drugNum0:int = 0;
         var desB0:Boolean = false;
         var setting0:SettingSave = Gaming.PG.save.setting;
         var desArr0:Array = setting0.gene_batchDecomposeColorArr;
         if(desArr0.indexOf(ItemsBatchColor.auto) >= 0)
         {
            desB0 = ItemsBatchColor.dataPanColorArr(s0,desArr0);
         }
         if(desB0)
         {
            drugNum0 = getStrengthenDrugBy(s0.color);
            Gaming.dropGroup.dropThings(getDecomposeName(),bd0.x,bd0.y,drugNum0);
            return true;
         }
         return false;
      }
   }
}

