package com.sounto.image
{
   public class AlignType
   {
      
      public static const NO:String = "";
      
      public static const LEFT:String = "left";
      
      public static const MID:String = "mid";
      
      public static const RIGHT:String = "right";
      
      public static const TOP:String = "top";
      
      public static const DOWN:String = "down";
      
      public function AlignType()
      {
         super();
      }
      
      public static function getYAlign(x0:String) : String
      {
         if(x0 == LEFT)
         {
            return TOP;
         }
         if(x0 == RIGHT)
         {
            return DOWN;
         }
         if(x0 == MID)
         {
            return MID;
         }
         return NO;
      }
   }
}

