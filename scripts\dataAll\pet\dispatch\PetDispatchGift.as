package dataAll.pet.dispatch
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.pro.dataList.DataListDefine;
   import dataAll.things.define.ThingsDefine;
   
   public class PetDispatchGift
   {
      
      public function PetDispatchGift()
      {
         super();
      }
      
      public static function getHourByLife(life0:Number) : int
      {
         var l0:DataListDefine = Gaming.defineGroup.dataList.getDefine("petDispatchLife");
         var lv0:int = l0.findLv(life0);
         var hour0:int = lv0;
         if(hour0 > 8)
         {
            hour0 = 8;
         }
         return hour0;
      }
      
      public static function getHighProByDps(dps0:Number) : Number
      {
         var l0:DataListDefine = Gaming.defineGroup.dataList.getDefine("petDispatchDps");
         var lv0:int = l0.findLv(dps0);
         var pro0:Number = 0.1 + (lv0 - 1) * 0.01;
         if(pro0 > 0.5)
         {
            pro0 = 0.5;
         }
         return pro0;
      }
      
      public static function getHighProByGrow(growPro0:Number) : Number
      {
         var pro0:Number = growPro0 * 0.2;
         pro0 = NumberMethod.toFixed(pro0,2);
         if(pro0 > 0.48)
         {
            pro0 = 0.48;
         }
         return pro0;
      }
      
      public static function countGift(useMin0:Number, petArr0:Array) : GiftAddDefineGroup
      {
         var da0:PetData = null;
         var breakB0:Boolean = false;
         var h0:int = 0;
         var useH0:int = 0;
         var g0:GiftAddDefineGroup = null;
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var hour0:int = Math.floor(useMin0 / 60);
         TRACE("开始计算宠物派遣=================  有效" + hour0 + "小时");
         for each(da0 in petArr0)
         {
            breakB0 = false;
            h0 = da0.getDispatchHour();
            useH0 = 0;
            if(hour0 > h0)
            {
               useH0 = h0;
               hour0 -= h0;
            }
            else
            {
               useH0 = hour0;
               breakB0 = true;
            }
            TRACE("【" + da0.getGeneDefine().cnName + "】" + useH0 + "小时---------------------------");
            g0 = getGiftPetData(da0,useH0);
            gift0.merge(g0);
            if(breakB0)
            {
               TRACE("宠物派遣结束======================");
               break;
            }
         }
         return gift0;
      }
      
      private static function getGiftPetData(da0:PetData, useHour0:int) : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var firstGeneD0:GeneDefine = da0.getGeneDefine();
         if(Boolean(firstGeneD0.firstDefine))
         {
            firstGeneD0 = firstGeneD0.firstDefine;
         }
         TRACE("初级------------");
         for(var i:int = 0; i < useHour0; i++)
         {
            addGiftByDispaArr(2.5,g0,firstGeneD0.lowDispaArr);
         }
         TRACE("高级------------");
         var highHour0:int = getHighHour(useHour0);
         var highPro0:Number = da0.getDispatchHighPro();
         for(var j:int = 0; j < highHour0; j++)
         {
            addGiftByDispaArr(2.5,g0,firstGeneD0.highDispaArr,highPro0);
         }
         return g0;
      }
      
      private static function getHighHour(hour0:int) : int
      {
         var v0:int = hour0 + 2;
         if(v0 > 8)
         {
            v0 = 8;
         }
         return v0;
      }
      
      private static function addGiftByDispaArr(money0:int, g0:GiftAddDefineGroup, dispaCnArr0:Array, pro0:Number = 1) : void
      {
         var numV0:Number = NaN;
         var baseNum0:int = 0;
         var otherPro0:Number = NaN;
         var num0:int = 0;
         var cn0:String = ArrayMethod.getRandomOne(dispaCnArr0);
         var tip0:String = cn0 + "：";
         var d0:ThingsDefine = Gaming.defineGroup.things.getDefineByCnName(cn0);
         var price0:Number = d0.smeltD.price;
         if(price0 > 0)
         {
            if(price0 < 1)
            {
               price0 = 1;
            }
            numV0 = money0 / price0 * pro0;
            baseNum0 = Math.floor(numV0);
            otherPro0 = numV0 - baseNum0;
            num0 = baseNum0;
            if(Math.random() < otherPro0)
            {
               num0++;
            }
            if(num0 > 0)
            {
               g0.mergeGiftByStr("things;" + d0.name + ";" + num0);
            }
            tip0 += num0 + "  ，单价:" + price0 + "  ，baseNum:" + baseNum0 + "   otherPro:" + NumberMethod.toFixed(otherPro0,4);
         }
         else
         {
            tip0 += "0";
         }
         TRACE(tip0);
      }
      
      private static function TRACE(obj0:*) : void
      {
         trace(obj0);
      }
   }
}

