package dataAll._app.goods.define
{
   public class CrrencyGroup
   {
      
      private var obj:Object;
      
      public var info:String = "";
      
      public var goodsDefine:GoodsDefine;
      
      public function CrrencyGroup()
      {
         var name0:String = null;
         this.obj = {};
         super();
         var arr0:Array = PriceType.typeArr;
         for each(name0 in arr0)
         {
            this.obj[name0] = 0;
         }
      }
      
      public function addValue(type0:String, v0:Number) : Number
      {
         this.obj[type0] += v0;
         return this.obj[type0];
      }
      
      public function getValue(type0:String) : Number
      {
         return this.obj[type0];
      }
      
      public function inGoodsDefine(d0:GoodsDefine) : void
      {
         this.addValue(d0.priceType,d0.price);
         this.info = d0.cnName + "：" + d0.priceType + "：" + d0.price;
         this.goodsDefine = d0;
      }
      
      public function addGroup(g0:CrrencyGroup) : void
      {
         var name0:String = null;
         var arr0:Array = PriceType.typeArr;
         for each(name0 in arr0)
         {
            this.obj[name0] += g0.getValue(name0);
         }
      }
   }
}

