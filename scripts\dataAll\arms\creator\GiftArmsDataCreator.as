package dataAll.arms.creator
{
   import dataAll.arms.save.ArmsSave;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.level.define.unit.UnitType;
   
   public class GiftArmsDataCreator
   {
      
      public function GiftArmsDataCreator()
      {
         super();
      }
      
      public static function getArmsSave(d0:GiftAddDefine) : ArmsSave
      {
         var s0:ArmsSave = null;
         var fun0:Function = null;
         if(d0.itemsSave is ArmsSave)
         {
            s0 = d0.itemsSave.copyOne() as ArmsSave;
         }
         else
         {
            fun0 = GiftArmsDataCreator[d0.name];
            if(fun0 is Function)
            {
               s0 = fun0(d0);
            }
         }
         return s0;
      }
      
      private static function dropLv(d0:GiftAddDefine) : ArmsSave
      {
         return Gaming.defineGroup.armsCreator.getSaveByBody(UnitType.NORMAL,d0.lv as int,d0.dropName,d0.childType);
      }
      
      private static function drop(d0:GiftAddDefine) : ArmsSave
      {
         var hero_lv0:int = Gaming.PG.da.level;
         return Gaming.defineGroup.armsCreator.getSaveByBody(UnitType.NORMAL,hero_lv0,d0.dropName,d0.childType);
      }
      
      private static function dropBoss(d0:GiftAddDefine) : ArmsSave
      {
         var hero_lv0:int = Gaming.PG.da.level;
         return Gaming.defineGroup.armsCreator.getSaveByBody(UnitType.BOSS,hero_lv0,d0.dropName,d0.childType);
      }
      
      private static function gift(d0:GiftAddDefine) : ArmsSave
      {
         var s0:ArmsSave = Gaming.defineGroup.armsCreator.getSaveByColor(d0.color,int(d0.lv),d0.dropName,d0.childType);
         s0.setLockB(true);
         return s0;
      }
   }
}

