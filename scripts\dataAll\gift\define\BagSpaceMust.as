package dataAll.gift.define
{
   import dataAll._player.PlayerData;
   import dataAll.items.ItemsDataGroup;
   
   public class BagSpaceMust
   {
      
      public static var pro_arr:Array = ["arms","equip","things","parts","more","gene","bossCard"];
      
      public var arms:int = 0;
      
      public var equip:int = 0;
      
      public var things:int = 0;
      
      public var parts:int = 0;
      
      public var more:int = 0;
      
      public var gene:int = 0;
      
      public var bossCard:int = 0;
      
      public function BagSpaceMust()
      {
         super();
      }
      
      public static function getCnNameByName(str0:String) : String
      {
         return ItemsDataGroup.getCnNameBy(str0);
      }
      
      public function getOneNum() : int
      {
         var n:* = undefined;
         var num0:int = 0;
         for(n in pro_arr)
         {
            num0 = int(this[pro_arr[n]]);
            if(num0 > 0)
            {
               return num0;
            }
         }
         return 0;
      }
      
      public function getOneLabel() : String
      {
         var n:* = undefined;
         var num0:int = 0;
         for(n in pro_arr)
         {
            num0 = int(this[pro_arr[n]]);
            if(num0 > 0)
            {
               return pro_arr[n];
            }
         }
         return "";
      }
      
      public function getCanUseNum(useNum0:int, pd0:PlayerData) : int
      {
         var n0:String = null;
         var must0:int = 0;
         var now0:int = 0;
         var minNum0:int = 0;
         var min0:int = 999999;
         for each(n0 in pro_arr)
         {
            if(n0 != "things")
            {
               must0 = int(this[n0]);
               now0 = pd0.getBagSpaceByType(n0);
               minNum0 = useNum0 - (must0 - now0);
               if(min0 > minNum0)
               {
                  min0 = minNum0;
               }
            }
         }
         if(min0 > useNum0)
         {
            min0 = useNum0;
         }
         return min0;
      }
   }
}

