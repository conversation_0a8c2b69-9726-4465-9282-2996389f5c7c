package dataAll._app.head
{
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.head.define.HeadDefine;
   
   public class HeadOneSave
   {
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var getTimeStr:String = "";
      
      public var index:int = 0;
      
      public function HeadOneSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function inData(name0:String, timeStr0:String, index0:int) : void
      {
         this.name = name0;
         this.getTimeStr = timeStr0;
         this.index = index0;
      }
      
      public function getDefine() : HeadDefine
      {
         return Gaming.defineGroup.head.getDefine(this.name);
      }
      
      public function getDataTime() : Number
      {
         var s0:StringDate = null;
         if(this.getTimeStr == "")
         {
            return 0;
         }
         s0 = new StringDate(this.getTimeStr);
         return s0.getDateClass().getTime();
      }
      
      public function getLifeDay(nowTimeStr0:String) : int
      {
         var now0:StringDate = null;
         var get0:StringDate = null;
         var d0:HeadDefine = this.getDefine();
         if(d0.haveLifeB())
         {
            now0 = new StringDate(nowTimeStr0);
            get0 = new StringDate(this.getTimeStr);
            return d0.life - get0.compareDate(now0);
         }
         return 9999;
      }
   }
}

