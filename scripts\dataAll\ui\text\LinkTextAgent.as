package dataAll.ui.text
{
   import com.sounto.utils.TextMethod;
   import dataAll.skill.define.SkillDefine;
   import dataAll.ui.GatherColor;
   
   public class LinkTextAgent
   {
      
      private var cnArr:Array = [];
      
      private var linkArr:Array = [];
      
      private var dataArr:Array = [];
      
      public function LinkTextAgent()
      {
         super();
      }
      
      public static function titleColor(str0:String) : String
      {
         return "<b>" + TextMethod.color(str0,GatherColor.orangeColor) + "</b>";
      }
      
      public static function secColor(str0:String) : String
      {
         return TextMethod.color(str0,GatherColor.bluenessColor);
      }
      
      public function addData(cn0:String, link0:String = "", data0:* = null) : void
      {
         this.cnArr.push(cn0);
         this.linkArr.push(link0);
         this.dataArr.push(data0);
      }
      
      public function addSkillNameArr(skillArr0:Array, linkB0:Boolean) : void
      {
         var name0:String = null;
         var d0:SkillDefine = null;
         if(!skillArr0)
         {
            return;
         }
         for each(name0 in skillArr0)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            this.addData(d0.cnName,linkB0 ? d0.name : "",d0);
         }
      }
      
      public function getText() : String
      {
         var n:* = undefined;
         var cn0:String = null;
         var link0:String = null;
         var s0:String = "";
         var firstB0:Boolean = true;
         for(n in this.cnArr)
         {
            cn0 = this.cnArr[n];
            link0 = this.linkArr[n];
            if(!firstB0)
            {
               s0 += "\n";
            }
            if(link0 == "")
            {
               s0 += cn0;
            }
            else
            {
               s0 += TextMethod.link(cn0,link0);
            }
            firstB0 = false;
         }
         return s0;
      }
      
      public function getData(lineIndex0:int) : *
      {
         return this.dataArr[lineIndex0];
      }
      
      public function getDataByLink(link0:String) : *
      {
         var f0:int = 0;
         if(link0 != "")
         {
            f0 = this.linkArr.indexOf(link0);
            if(f0 >= 0)
            {
               return this.dataArr[f0];
            }
         }
         return null;
      }
      
      public function getLink(lineIndex0:int) : String
      {
         return this.linkArr[lineIndex0];
      }
      
      public function getLen() : int
      {
         return this.cnArr.length;
      }
   }
}

