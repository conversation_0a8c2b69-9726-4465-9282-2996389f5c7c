package dataAll.skill.save
{
   import com.sounto.utils.ClassProperty;
   import dataAll._base.IO_CanThinSave;
   import dataAll.items.save.ItemsSaveGroup;
   
   public class HeroSkillSaveGroup extends ItemsSaveGroup implements IO_CanThinSave
   {
      
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = [];
      
      public var delNameArr:Array = [];
      
      public function HeroSkillSaveGroup()
      {
         super();
      }
      
      public function getProArr() : Array
      {
         return pro_arr;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         super.inData_byObjAndClass(obj0,HeroSkillSave);
         if(!(obj0["delNameArr"] is Array))
         {
            obj0["delNameArr"] = [];
         }
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
      }
   }
}

