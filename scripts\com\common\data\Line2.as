package com.common.data
{
   import flash.geom.Point;
   
   public class Line2
   {
      
      private static const tempPoint:Point = new Point();
      
      public var x:Number = 0;
      
      public var y:Number = 0;
      
      public var x1:Number = 0;
      
      public var y1:Number = 0;
      
      public var ra:Number = 0;
      
      public var w:Number = 0;
      
      public var len:Number = 900;
      
      public function Line2(_x0:Number = 0, _y0:Number = 0, _ra:Number = 0, _w:Number = 0, _len:Number = 900)
      {
         super();
         this.x = _x0;
         this.y = _y0;
         this.ra = _ra;
         this.w = _w;
         this.len = _len;
      }
      
      public static function pointToLineDistance(px:Number, py:Number, startX:Number, startY:Number, length:Number, ra0:Number) : Number
      {
         var radians:Number = ra0;
         var endX:Number = startX + length * Math.cos(radians);
         var endY:Number = startY + length * Math.sin(radians);
         var dx:Number = endX - startX;
         var dy:Number = endY - startY;
         var lengthSquared:Number = dx * dx + dy * dy;
         if(lengthSquared == 0)
         {
            return Math.sqrt((px - startX) * (px - startX) + (py - startY) * (py - startY));
         }
         var t:Number = ((px - startX) * dx + (py - startY) * dy) / lengthSquared;
         t = Math.max(0,Math.min(1,t));
         var projX:Number = startX + t * dx;
         var projY:Number = startY + t * dy;
         dx = px - projX;
         dy = py - projY;
         return Math.sqrt(dx * dx + dy * dy);
      }
      
      public static function getFootPoint(px:Number, py:Number, lineX:Number, lineY:Number, length:Number, radians:Number) : Point
      {
         if(length <= 0)
         {
            tempPoint.x = lineX;
            tempPoint.y = lineY;
            return tempPoint;
         }
         var dx:Number = Math.cos(radians);
         var dy:Number = Math.sin(radians);
         var vx:Number = px - lineX;
         var vy:Number = py - lineY;
         var projection:Number = vx * dx + vy * dy;
         var footX:Number = lineX + projection * dx;
         var footY:Number = lineY + projection * dy;
         tempPoint.x = footX;
         tempPoint.y = footY;
         return tempPoint;
      }
      
      public function init() : void
      {
         this.x = 0;
         this.y = 0;
         this.x1 = 0;
         this.y1 = 0;
         this.ra = 0;
         this.w = 0;
         this.len = 0;
      }
      
      public function clone() : Line2
      {
         return new Line2(this.x,this.y,this.ra,this.w,this.len);
      }
      
      public function toString() : String
      {
         return "x:" + this.x + ",y:" + this.y + ",ra:" + this.ra + ",w:" + this.w;
      }
      
      public function countGap(l0:Object) : Number
      {
         return Math.sqrt((this.x - l0.x) * (this.x - l0.x) + (this.y - l0.y) * (this.y - l0.y));
      }
   }
}

