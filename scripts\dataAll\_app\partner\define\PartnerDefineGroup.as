package dataAll._app.partner.define
{
   import dataAll._app.partner.ability.PartnerAbility;
   import dataAll._app.partner.ability.PartnerAbilityDefine;
   
   public class PartnerDefineGroup
   {
      
      private var abilityArr:Array = [];
      
      private var abilityObj:Object = {};
      
      public function PartnerDefineGroup()
      {
         super();
      }
      
      public function init() : void
      {
         var d0:PartnerAbilityDefine = null;
         d0 = null;
         d0 = new PartnerAbilityDefine();
         d0.name = PartnerAbility.attack;
         d0.cnName = "攻击能力";
         this.addAbilityDefine(d0);
         d0 = new PartnerAbilityDefine();
         d0.name = PartnerAbility.defence;
         d0.cnName = "防御能力";
         this.addAbilityDefine(d0);
      }
      
      private function addAbilityDefine(d0:PartnerAbilityDefine) : void
      {
         this.abilityArr.push(d0);
         this.abilityObj[d0.name] = d0;
      }
      
      public function getAbilityArr() : Array
      {
         return this.abilityArr;
      }
   }
}

