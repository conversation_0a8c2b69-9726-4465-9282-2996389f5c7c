package dataAll.test
{
   public class ZuoBiMax
   {
      
      public static const endlessScore:Number = 2100000000;
      
      public static const dps:Number = 38000000000;
      
      public static const armsDps:Number = 1200000000;
      
      public static const lastUid:Number = 4033289181;
      
      public var dpsScoreMul:Number = 0.1;
      
      public function ZuoBiMax()
      {
         super();
      }
      
      public static function hideTextPan(uid0:String) : Boolean
      {
         if(uid0 == "")
         {
            return false;
         }
         if(Number(uid0) < lastUid)
         {
            return false;
         }
         return true;
      }
   }
}

