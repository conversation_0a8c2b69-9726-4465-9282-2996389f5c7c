package com.sounto.math
{
   import flash.geom.Rectangle;
   
   public class IDRect extends Rectangle
   {
      
      public var id:String = "";
      
      public function IDRect(id0:String = "")
      {
         super();
         this.id = id0;
      }
      
      public function inData(rect0:Rectangle) : void
      {
         x = rect0.x;
         y = rect0.y;
         width = rect0.width;
         height = rect0.height;
      }
      
      public function clone2() : IDRect
      {
         var idr:IDRect = new IDRect();
         idr.inData(this);
         idr.id = this.id;
         return idr;
      }
   }
}

