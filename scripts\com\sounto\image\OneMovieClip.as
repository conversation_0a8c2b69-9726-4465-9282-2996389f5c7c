package com.sounto.image
{
   import com.sounto.utils.DisplayMethod;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class OneMovieClip extends SimulateMovieClip
   {
      
      public var mc:MovieClip = null;
      
      public var dieType:String = "normal";
      
      public var die:int = 0;
      
      protected var timeStopB:Boolean = false;
      
      public function OneMovieClip()
      {
         super();
      }
      
      public function add(mc0:MovieClip, father0:String, label0:String) : *
      {
         father = father0;
         label = label0;
         if(!this.mc)
         {
            this.mc = mc0;
            addChild(this.mc);
            this.mc.stop();
         }
      }
      
      override public function toDie() : void
      {
         super.toDie();
         this.die = 2;
      }
      
      override public function get currentFrame() : int
      {
         return this.mc.currentFrame;
      }
      
      override public function get totalFrames() : int
      {
         return this.mc.totalFrames;
      }
      
      override public function addRideSp(sp0:DisplayObject) : void
      {
         var rideMc0:Sprite = this.getSpInPart("body","ride");
         if(rideMc0 is Sprite)
         {
            rideMc0.addChild(sp0);
         }
      }
      
      override public function getSpInPart(part0:String, name0:String) : Sprite
      {
         var rideMc0:Sprite = null;
         var bodyMc0:Sprite = this.getMcByLabel(part0);
         if(bodyMc0 is Sprite)
         {
            rideMc0 = bodyMc0.getChildByName(name0) as Sprite;
            if(rideMc0 is Sprite)
            {
               return rideMc0;
            }
         }
         return null;
      }
      
      override public function addInPartSp(part0:String, sp0:DisplayObject) : void
      {
         var bodyMc0:Sprite = this.getMcByLabel(part0);
         if(bodyMc0 is Sprite)
         {
            bodyMc0.addChild(sp0);
         }
      }
      
      public function clearMcInsideArr(labelArr0:Array) : void
      {
         var mc0:MovieClip = null;
         var len0:int = this.mc.numChildren;
         for(var i:int = 0; i < len0; i++)
         {
            mc0 = this.mc.getChildAt(i) as MovieClip;
            if(mc0 is MovieClip)
            {
               if(labelArr0.indexOf(mc0.currentLabel) >= 0)
               {
                  DisplayMethod.clearAllChild(mc0);
               }
            }
         }
      }
      
      public function getMcByLabel(label0:String) : Sprite
      {
         var mc0:MovieClip = null;
         var len0:int = this.mc.numChildren;
         for(var i:int = 0; i < len0; i++)
         {
            mc0 = this.mc.getChildAt(i) as MovieClip;
            if(mc0 is MovieClip)
            {
               if(mc0.currentLabel == label0)
               {
                  return mc0;
               }
            }
         }
         return null;
      }
      
      override public function stop() : void
      {
         this.mc.stop();
         isPlaying = false;
      }
      
      override public function play() : void
      {
         if(enabled && this.timeStopB == false)
         {
            this.mc.play();
         }
         isPlaying = true;
      }
      
      override public function gotoAndPlay(num:int) : *
      {
         if(enabled && this.timeStopB == false)
         {
            this.mc.gotoAndPlay(num);
         }
         else
         {
            this.mc.gotoAndStop(num);
         }
         isPlaying = true;
      }
      
      override public function gotoAndStop(num:int) : *
      {
         if(enabled)
         {
            this.mc.gotoAndStop(num);
            isPlaying = false;
         }
      }
      
      override public function pause() : void
      {
         this.mc.stop();
         enabled = false;
      }
      
      override public function resume() : void
      {
         enabled = true;
         if(isPlaying)
         {
            this.play();
         }
      }
      
      override public function FTimer(timeStopB0:Boolean = false) : void
      {
         this.timeStopB = timeStopB0;
         if(Boolean(this.mc))
         {
            if(timeStopB0 || enabled == false || isPlaying == false)
            {
               this.mc.stop();
            }
            else
            {
               this.mc.play();
            }
         }
      }
   }
}

