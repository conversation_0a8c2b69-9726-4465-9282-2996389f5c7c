package dataAll._app.goods.define
{
   import com.sounto.utils.ArrayMethod;
   import dataAll.items.find.IO_CnNameFinder;
   
   public class GoodsDefineGroup implements IO_CnNameFinder
   {
      
      public var obj:Object = {};
      
      public var fatherObj:Object = {};
      
      private var fatherObjObj:Object = {};
      
      public var fatherDefineObj:Object = {};
      
      public var fatherNameArrObj:Object = {};
      
      public var fatherCnNameArrObj:Object = {};
      
      private var labelArrObj:Object = {};
      
      private var havePropsIdObj:Object = {};
      
      private var findCnObj:Object = {};
      
      public var monthCardArr:Array = [];
      
      public function GoodsDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var typeXml0:* = undefined;
         var fatherName0:String = null;
         var fatherCnName0:String = null;
         var fatherDefine0:GoodsFatherDefine = null;
         var j:* = undefined;
         var uiType0:String = null;
         var thingsXML0:* = undefined;
         var fobj0:Object = null;
         var typeArr0:Array = null;
         var n:* = undefined;
         var d0:GoodsDefine = null;
         var xx0:int = 0;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            typeXml0 = fatherXML0[i].type;
            fatherName0 = fatherXML0[i].@name;
            fatherCnName0 = fatherXML0[i].@cnName;
            fatherDefine0 = new GoodsFatherDefine();
            fatherDefine0.inData_byXML(fatherXML0[i]);
            this.addFather(fatherName0,fatherDefine0);
            for(j in typeXml0)
            {
               uiType0 = typeXml0[j].@name;
               thingsXML0 = typeXml0[j].goods;
               fobj0 = this.fatherObjObj[fatherName0];
               if(fobj0.hasOwnProperty(uiType0) == false)
               {
                  fobj0[uiType0] = [];
               }
               typeArr0 = fobj0[uiType0];
               for(n in thingsXML0)
               {
                  d0 = new GoodsDefine();
                  d0.inData_byXML(thingsXML0[n],fatherName0,uiType0);
                  if(this.obj.hasOwnProperty(d0.name))
                  {
                     xx0 = 0;
                  }
                  this.obj[d0.name] = d0;
                  this.fatherObj[fatherName0].push(d0);
                  typeArr0.push(d0);
                  this.addDefineLabel(d0);
               }
            }
         }
      }
      
      private function addFather(fatherName0:String, d0:GoodsFatherDefine) : void
      {
         this.fatherDefineObj[d0.name] = d0;
         if(!this.fatherObj.hasOwnProperty(fatherName0))
         {
            this.fatherObj[fatherName0] = [];
            this.fatherObjObj[fatherName0] = {};
         }
         if(!this.fatherNameArrObj.hasOwnProperty(d0.type))
         {
            this.fatherNameArrObj[d0.type] = [];
            this.fatherCnNameArrObj[d0.type] = [];
         }
         this.fatherNameArrObj[d0.type].push(d0.name);
         this.fatherCnNameArrObj[d0.type].push(d0.cnName);
      }
      
      private function addDefineLabel(d0:GoodsDefine) : void
      {
         if(!this.labelArrObj.hasOwnProperty(d0.defineLabel))
         {
            this.labelArrObj[d0.defineLabel] = [];
         }
         this.labelArrObj[d0.defineLabel].push(d0);
         if(d0.name.indexOf("MonthCard") >= 0)
         {
            this.monthCardArr.push(d0);
         }
         if(d0.propId != "")
         {
            this.havePropsIdObj[d0.name] = d0;
         }
      }
      
      private function addInFindCnObj(d0:GoodsDefine) : void
      {
         var fd0:GoodsDefine = null;
         if(d0.father != GoodsFatherType.OTHER)
         {
            fd0 = this.findCnObj[d0.cnName];
            if(!fd0 || fd0.father != GoodsFatherType.SHOP)
            {
               this.findCnObj[d0.cnName] = d0;
            }
         }
      }
      
      public function outInArr(father0:String, defineArr0:Array) : void
      {
         var d0:GoodsDefine = null;
         for each(d0 in defineArr0)
         {
            this.obj[d0.name] = d0;
            this.fatherObj[father0].push(d0);
            this.addDefineLabel(d0);
         }
      }
      
      public function getDefine(name0:String) : GoodsDefine
      {
         return this.obj[name0];
      }
      
      public function getDefineArr(type0:String) : Array
      {
         return this.fatherObj[type0];
      }
      
      public function getFatherDefine(label0:String) : GoodsFatherDefine
      {
         return this.fatherDefineObj[label0];
      }
      
      public function getByPropsId(id0:String) : GoodsDefine
      {
         var n:* = undefined;
         var d0:GoodsDefine = null;
         for(n in this.obj)
         {
            d0 = this.obj[n];
            if(d0.propId == id0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getHavePropsIdDefine(name0:String) : GoodsDefine
      {
         return this.havePropsIdObj[name0];
      }
      
      public function getDefineArrByDefineLabel(name0:String) : Array
      {
         var arr0:Array = this.labelArrObj[name0];
         if(!arr0)
         {
            arr0 = [];
         }
         return arr0;
      }
      
      public function fleshAllByDefine() : void
      {
         var obj0:Object = null;
         var d0:GoodsDefine = null;
         for each(obj0 in this.fatherObj)
         {
            for each(d0 in obj0)
            {
               d0.fleshAllByDefine();
               this.addInFindCnObj(d0);
            }
         }
      }
      
      public function getCnArrByFind(str0:String) : Array
      {
         var objArr0:Array = null;
         var cn0:* = undefined;
         var f0:int = 0;
         var d0:GoodsDefine = null;
         var obj0:Object = null;
         var cnArr0:Array = [];
         var cud0:GoodsDefine = this.findCnObj[str0];
         if(Boolean(cud0))
         {
            cnArr0.push(cud0.cnName);
         }
         else
         {
            objArr0 = [];
            for(cn0 in this.findCnObj)
            {
               f0 = int((cn0 as String).indexOf(str0));
               d0 = this.findCnObj[cn0];
               if(f0 >= 0)
               {
                  obj0 = new Object();
                  obj0.index = f0;
                  obj0.def = d0;
                  objArr0.push(obj0);
               }
            }
            if(objArr0.length > 0)
            {
               objArr0.sort(this.sortFindCnObjFun);
               for each(obj0 in objArr0)
               {
                  cnArr0.push(obj0.def.cnName);
               }
            }
         }
         return cnArr0;
      }
      
      private function sortFindCnObjFun(a:Object, b:Object) : int
      {
         var ad0:GoodsDefine = null;
         var bd0:GoodsDefine = null;
         var s2:int = 0;
         var s1:int = ArrayMethod.sortNumberFun(a.index,b.index);
         if(s1 == 0)
         {
            ad0 = a.def;
            bd0 = b.def;
            s2 = ArrayMethod.sortNumberFun(ad0.cnName.length,bd0.cnName.length);
            if(s2 == 0)
            {
               return ArrayMethod.sortNumberFun(ad0.propId,bd0.propId);
            }
            return s2;
         }
         return s1;
      }
   }
}

