package dataAll.skill.define
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   
   public class SkillDescrip
   {
      
      public function SkillDescrip()
      {
         super();
      }
      
      public static function getCnText(skillNameArr0:Array) : String
      {
         return StringMethod.concatStringArr(Gaming.defineGroup.skill.getCnArrByNameArr(skillNameArr0),99);
      }
      
      public static function getSkillArrGather(skillArr0:Array, titleColor0:String = "", mustWantDescripB0:Boolean = true, mustBarShowB0:Boolean = false, fleshDesrpB0:Boolean = false, maxLvB0:Boolean = false) : String
      {
         var name0:String = null;
         var skillD0:SkillDefine = null;
         var title0:String = null;
         var heroD0:HeroSkillDefine = null;
         var maxD0:HeroSkillDefine = null;
         var str0:String = null;
         if(skillArr0.length == 0)
         {
            return "";
         }
         var strArr0:Array = [];
         for each(name0 in skillArr0)
         {
            skillD0 = Gaming.defineGroup.skill.getDefine(name0);
            if(skillD0 is SkillDefine)
            {
               title0 = skillD0.cnName;
               if(maxLvB0)
               {
                  heroD0 = skillD0 as HeroSkillDefine;
                  if(Boolean(heroD0))
                  {
                     maxD0 = heroD0.getMaxLevelDefine();
                     if(maxD0.lv > 1)
                     {
                        skillD0 = maxD0;
                        title0 += "(第" + maxD0.lv + "级)";
                     }
                  }
               }
               if((!mustWantDescripB0 || skillD0.wantDescripB) && (!mustBarShowB0 || skillD0.showInLifeBarB))
               {
                  str0 = (titleColor0 == "" ? title0 : ComMethod.color(title0,titleColor0)) + ":" + skillD0.getDescription(fleshDesrpB0);
                  strArr0.push(str0);
               }
            }
         }
         return TextWay.mixedStringArr(strArr0,1,"\n");
      }
      
      public static function getSkillGather(name0:String) : String
      {
         var skillD0:SkillDefine = Gaming.defineGroup.skill.getDefine(name0);
         if(Boolean(skillD0))
         {
            return skillD0.getDescription();
         }
         return "";
      }
   }
}

