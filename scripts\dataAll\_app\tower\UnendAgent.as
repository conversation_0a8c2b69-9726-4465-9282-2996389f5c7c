package dataAll._app.tower
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.body.define.BodyCamp;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.level.define.unit.UnitOrderDefine;
   import dataAll.level.define.unit.UnitType;
   import dataAll.level.modeDiy.ModeDiyDefine;
   
   public class UnendAgent
   {
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var bossCnArr:Array = [];
      
      public var enemyCnArr:Array = [];
      
      public var gift:GiftAddDefineGroup = null;
      
      public var map:String = "";
      
      public var modeDiy:String = "";
      
      public function UnendAgent()
      {
         super();
         this.lv = 1;
         this.bossLife = 1;
         this.bossDps = 1;
         this.enemyLife = 1;
         this.enemyDps = 1;
      }
      
      public static function get MAX_LV() : int
      {
         return 85;
      }
      
      public static function get ONE_WHOLE_DPS() : Number
      {
         return 0.0003;
      }
      
      public static function get ONE_RAN_ARMS() : Number
      {
         return 0.01;
      }
      
      public static function dealLv(lv0:int) : int
      {
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         else if(lv0 > MAX_LV)
         {
            lv0 = MAX_LV;
         }
         return lv0;
      }
      
      public static function GET_LIFE(lv0:int, unitType0:String) : Number
      {
         var v0:Number = 50 + lv0 * 10;
         if(lv0 >= 76)
         {
            v0 = lv0 * lv0 * 0.5 + 500;
         }
         else if(lv0 >= 21)
         {
            v0 = lv0 * lv0 * 0.5 + 40;
         }
         if(unitType0 == UnitType.BOSS)
         {
            v0 *= 2;
         }
         else if(unitType0 == UnitType.SUPER)
         {
            v0 *= 2;
         }
         else
         {
            v0 *= 5;
         }
         if(lv0 == 85)
         {
            v0 *= 1.8;
         }
         return Math.ceil(v0);
      }
      
      public static function GET_DPS(lv0:int, unitType0:String) : Number
      {
         var v0:Number = 4 + lv0 * 0.5;
         if(lv0 >= 31)
         {
            v0 = 17 + lv0 * 0.1;
         }
         if(unitType0 == UnitType.BOSS)
         {
            v0 *= 1;
         }
         return v0 * 0.5;
      }
      
      public static function GET_SKILL(lv0:int, unitType0:String) : Array
      {
         var arr0:Array = [];
         if(lv0 >= 76)
         {
         }
         return arr0;
      }
      
      public static function GET_BODY_LIFE_MUL(d0:NormalBodyDefine, lv0:int, unitType0:String) : Number
      {
         var v0:Number = NaN;
         var bossSkillArr0:Array = null;
         if(unitType0 == UnitType.BOSS)
         {
            v0 = Gaming.defineGroup.unend.getBodyLife(d0.cnName);
            bossSkillArr0 = d0.getEditSkillArr();
            if(bossSkillArr0.indexOf("State_SpellImmunity") >= 0 || bossSkillArr0.indexOf("spellImmunityMax") >= 0)
            {
               v0 *= 0.6;
            }
            return v0;
         }
         return 1;
      }
      
      public static function GET_BODY_DPS_MUL(d0:NormalBodyDefine, lv0:int, unitType0:String) : Number
      {
         var v0:Number = NaN;
         if(unitType0 == UnitType.BOSS)
         {
            return Gaming.defineGroup.unend.getBodyDps(d0.cnName);
         }
         return 1;
      }
      
      public static function GET_MODE_DIY(lv0:int) : String
      {
         if(lv0 % 5 == 0)
         {
            return ModeDiyDefine.UNEND;
         }
         return ModeDiyDefine.UNEND_SINGLE;
      }
      
      public function get lv() : Number
      {
         return this.CF.getAttribute("lv");
      }
      
      public function set lv(v0:Number) : void
      {
         this.CF.setAttribute("lv",v0);
      }
      
      public function get bossLife() : Number
      {
         return this.CF.getAttribute("bossLife");
      }
      
      public function set bossLife(v0:Number) : void
      {
         this.CF.setAttribute("bossLife",v0);
      }
      
      public function get bossDps() : Number
      {
         return this.CF.getAttribute("bossDps");
      }
      
      public function set bossDps(v0:Number) : void
      {
         this.CF.setAttribute("bossDps",v0);
      }
      
      public function get enemyLife() : Number
      {
         return this.CF.getAttribute("enemyLife");
      }
      
      public function set enemyLife(v0:Number) : void
      {
         this.CF.setAttribute("enemyLife",v0);
      }
      
      public function get enemyDps() : Number
      {
         return this.CF.getAttribute("enemyDps");
      }
      
      public function set enemyDps(v0:Number) : void
      {
         this.CF.setAttribute("enemyDps",v0);
      }
      
      public function isMaxB() : Boolean
      {
         return this.lv >= MAX_LV;
      }
      
      public function getModeDiy() : String
      {
         return GET_MODE_DIY(this.lv);
      }
      
      public function getModeDiyDef() : ModeDiyDefine
      {
         return Gaming.defineGroup.modelDiy.getDefine(this.getModeDiy());
      }
      
      public function getUILife(unitType0:String) : Number
      {
         var v0:Number = this.enemyLife;
         if(unitType0 == UnitType.BOSS)
         {
            v0 = this.bossLife;
         }
         v0 *= this.getModeDiyDef().lifeMul;
         return v0 * UnitType.getLifeMul(unitType0);
      }
      
      public function getUIDps(unitType0:String) : Number
      {
         var v0:Number = this.enemyDps;
         if(unitType0 == UnitType.BOSS)
         {
            v0 = this.bossDps;
         }
         v0 *= this.getModeDiyDef().dpsMul;
         v0 *= UnitType.getDpsMul(unitType0);
         return NumberMethod.toFixed(v0,2);
      }
      
      public function getBossSkillArr() : Array
      {
         return GET_SKILL(this.lv,UnitType.BOSS);
      }
      
      public function getLevelDefine(da0:UnendData) : LevelDefine
      {
         var baseD0:LevelDefine = null;
         var mapD0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(this.map);
         var d0:LevelDefine = Gaming.defineGroup.level.getDefineBy("unend").clone();
         var base0:String = mapD0.getLevelName();
         if(base0 != "")
         {
            baseD0 = Gaming.defineGroup.level.getDefine(base0);
            d0.rectG = baseD0.getCloneRectG();
         }
         d0.sceneLabel = mapD0.name;
         d0.info.tm = 5 * 60 + da0.getPointValue("tm");
         d0.info.modeDiy = this.getModeDiy();
         var allDefault0:OneUnitOrderDefine = d0.unitG.allDefault;
         var bossUD0:UnitOrderDefine = this.getUnit("boss1",UnitType.BOSS,this.bossCnArr,allDefault0);
         var enemyUD0:UnitOrderDefine = this.getUnit("enemy1",UnitType.NORMAL,this.enemyCnArr,allDefault0);
         d0.unitG.addUnitOrderDefine(bossUD0);
         d0.unitG.addUnitOrderDefine(enemyUD0);
         d0.fleshDataByOutChange();
         return d0;
      }
      
      private function getUnit(id0:String, unitType0:String, cnArr0:Array, default_d0:OneUnitOrderDefine) : UnitOrderDefine
      {
         var cn0:String = null;
         var od0:OneUnitOrderDefine = null;
         var ud0:UnitOrderDefine = new UnitOrderDefine();
         ud0.id = id0;
         for each(cn0 in cnArr0)
         {
            od0 = this.getOneUnit(cn0,unitType0);
            ud0.addOneUnitOrderDefine(od0,default_d0);
         }
         return ud0;
      }
      
      private function getOneUnit(cn0:String, unitType0:String) : OneUnitOrderDefine
      {
         var bodyD0:NormalBodyDefine = Gaming.defineGroup.body.getCnDefine(cn0);
         var od0:OneUnitOrderDefine = new OneUnitOrderDefine();
         od0.defaultDeal();
         od0.name = bodyD0.name;
         od0.cnName = bodyD0.cnName;
         od0.camp = BodyCamp.ENEMY;
         od0.unitType = unitType0;
         od0.warningRange = 99999;
         if(unitType0 == UnitType.BOSS)
         {
            od0.lifeMul *= this.bossLife * GET_BODY_LIFE_MUL(bodyD0,this.lv,unitType0);
            od0.dpsMul *= this.bossDps * GET_BODY_DPS_MUL(bodyD0,this.lv,unitType0);
         }
         else
         {
            od0.lifeMul *= this.enemyLife;
            od0.dpsMul *= this.enemyDps;
         }
         od0.skillArr = od0.skillArr.concat(GET_SKILL(this.lv,unitType0));
         return od0;
      }
   }
}

