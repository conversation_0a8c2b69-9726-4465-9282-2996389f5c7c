package dataAll._app.achieve.define
{
   import com.common.data.Base64;
   import dataAll._app.achieve.creator.AchievePropertyCreator;
   import dataAll.ui.label.LabelAddData;
   
   public class AchieveDefineGroup
   {
      
      public var gatherNameArr:Array = [];
      
      public var fatherNameArr:Array = [];
      
      public var fatherDefineObj:Object = {};
      
      public var obj:Object = {};
      
      public var fatherArrObj:Object = {};
      
      public var medelProObj:Object = {};
      
      public var medelProNameArr:Array = [];
      
      private var base64:String = "";
      
      public var creator:AchievePropertyCreator = new AchievePropertyCreator();
      
      public function AchieveDefineGroup()
      {
         super();
         this.creator.achieveDefineGroup = this;
      }
      
      public function init() : void
      {
         this.base64 = Base64.encodeObject([this.obj,this.medelProObj]);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var x0:XML = null;
         var gatherD0:AchieveFatherDefine = null;
         var xl1:XMLList = null;
         var fatherNameArr0:Array = null;
         var i:* = undefined;
         var x1:XML = null;
         var fatherD0:AchieveFatherDefine = null;
         var xl2:XMLList = null;
         var testPro0:String = null;
         var j:* = undefined;
         var x2:XML = null;
         var d0:AchieveDefine = null;
         var xl0:XMLList = xml0.gather;
         for(n in xl0)
         {
            x0 = xl0[n];
            gatherD0 = new AchieveFatherDefine();
            gatherD0.inData_byXML(x0);
            this.addFatherDefine(gatherD0);
            this.gatherNameArr.push(gatherD0.name);
            xl1 = x0.father;
            fatherNameArr0 = [];
            for(i in xl1)
            {
               x1 = xl1[i];
               fatherD0 = new AchieveFatherDefine();
               fatherD0.inData_byXML(x1);
               this.addFatherDefine(fatherD0);
               fatherNameArr0.push(fatherD0.name);
               xl2 = x1.achieve;
               testPro0 = "";
               for(j in xl2)
               {
                  x2 = xl2[j];
                  d0 = new AchieveDefine();
                  d0.inData_byXML(x2,fatherD0.name,gatherD0.name);
                  this.add(d0,fatherD0.name);
                  if(testPro0 == "")
                  {
                     testPro0 = d0.medelProArr[0];
                  }
                  else if(testPro0 != d0.medelProArr[0])
                  {
                     INIT.TRACE(d0.cnName + "  " + d0.medelProArr[0] + " !=" + testPro0);
                  }
               }
            }
            this.fatherNameArr.push(fatherNameArr0);
         }
      }
      
      public function afterInit() : void
      {
         var f0:AchieveFatherDefine = null;
         var arr0:Array = null;
         for each(f0 in this.fatherDefineObj)
         {
            arr0 = this.getArrByFather(f0.name);
            if(Boolean(arr0) && arr0.length > 0)
            {
               f0.maxDef = arr0[arr0.length - 1];
            }
         }
      }
      
      public function inMedelPropertyXml(xml0:XML) : void
      {
         var n:* = undefined;
         var x0:XML = null;
         var d0:MedelProDefine = null;
         var xl0:XMLList = xml0.pro;
         for(n in xl0)
         {
            x0 = xl0[n];
            d0 = new MedelProDefine();
            d0.inData_byXML(x0);
            this.medelProObj[d0.name] = d0;
            this.medelProNameArr.push(d0.name);
         }
      }
      
      private function addFatherDefine(d0:AchieveFatherDefine) : void
      {
         this.fatherDefineObj[d0.name] = d0;
      }
      
      private function add(d0:AchieveDefine, father0:String) : void
      {
         this.obj[d0.name] = d0;
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
         }
         this.fatherArrObj[father0].push(d0);
      }
      
      public function getDefine(name0:String) : AchieveDefine
      {
         return this.obj[name0];
      }
      
      public function getFatherDefine(name0:String) : AchieveFatherDefine
      {
         return this.fatherDefineObj[name0];
      }
      
      public function getArrByFather(father0:String) : Array
      {
         return this.fatherArrObj[father0];
      }
      
      public function getMedelPropertyDefine(name0:String) : MedelProDefine
      {
         return this.medelProObj[name0];
      }
      
      public function getLabelAddData() : LabelAddData
      {
         var n:* = undefined;
         var name1:String = null;
         var f1:AchieveFatherDefine = null;
         var da1:LabelAddData = null;
         var fatherNameArr0:Array = null;
         var i:* = undefined;
         var name2:String = null;
         var f2:AchieveFatherDefine = null;
         var da2:LabelAddData = null;
         var da0:LabelAddData = new LabelAddData();
         da0.inDataOne("achieve","成就","AchieveUI/bigLabel",0);
         for(n in this.gatherNameArr)
         {
            name1 = this.gatherNameArr[n];
            f1 = this.getFatherDefine(name1);
            da1 = new LabelAddData();
            da1.inDataOne(name1,f1.cnName,"AchieveUI/midLabel",0);
            fatherNameArr0 = this.fatherNameArr[n];
            for(i in fatherNameArr0)
            {
               name2 = fatherNameArr0[i];
               f2 = this.getFatherDefine(name2);
               da2 = new LabelAddData();
               da2.inDataOne(name2,f2.getLabelCn(),"AchieveUI/midLabel",i);
               da1.addChildData(da2);
            }
            da0.addChildData(da1);
         }
         return da0;
      }
      
      public function testCount() : void
      {
         var d0:AchieveDefine = null;
         var n:* = undefined;
         var pro0:String = null;
         var proD0:MedelProDefine = null;
         var v0:Number = NaN;
         var obj0:Object = {};
         for each(d0 in this.obj)
         {
            for each(pro0 in d0.medelProArr)
            {
               if(!(pro0.indexOf("dps") == -1 && pro0.indexOf("hurt") == -1))
               {
                  if(!obj0.hasOwnProperty(pro0))
                  {
                     obj0[pro0] = 0;
                  }
                  proD0 = this.getMedelPropertyDefine(pro0);
                  v0 = proD0.v * d0.achieveDiff;
                  if(v0 > obj0[pro0])
                  {
                     obj0[pro0] = v0;
                  }
               }
            }
         }
         trace("---------------------------------");
         for(n in obj0)
         {
            trace(n + ":" + obj0[n]);
         }
      }
      
      public function zuobiPan() : String
      {
         var str0:String = Base64.encodeObject([this.obj,this.medelProObj]);
         if(str0 != this.base64)
         {
            return "achieve";
         }
         return "";
      }
      
      public function getIconNum() : int
      {
         var d0:AchieveDefine = null;
         var obj0:Object = {};
         var num0:int = 0;
         for each(d0 in this.obj)
         {
            if(!obj0.hasOwnProperty(d0.iconUrl))
            {
               obj0[d0.iconUrl] = 0;
               num0++;
            }
         }
         return num0;
      }
   }
}

