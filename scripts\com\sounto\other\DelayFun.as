package com.sounto.other
{
   public class DelayFun
   {
      
      public var delay:Number = 0.1;
      
      public var t:Number = -1;
      
      public var fun:Function = null;
      
      public function DelayFun()
      {
         super();
      }
      
      public function FTimer() : void
      {
         if(this.t >= 0)
         {
            if(this.t >= this.delay)
            {
               this.t = -1;
               this.doFun();
            }
            else
            {
               this.t += 1 / 30;
            }
         }
      }
      
      public function start() : void
      {
         if(this.t == -1)
         {
            this.t = 0;
         }
      }
      
      private function doFun() : void
      {
         if(this.fun is Function)
         {
            this.fun();
         }
      }
      
      public function haveDelayB() : Boolean
      {
         return this.delay >= 0;
      }
      
      public function over() : void
      {
         this.t = -1;
      }
   }
}

