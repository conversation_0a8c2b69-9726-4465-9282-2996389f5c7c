package dataAll.arms.creator.evo
{
   import com.sounto.utils.ArrayMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.define.EquipColor;
   import dataAll.must.define.MustDefine;
   
   public class YearEvoCtrl extends DiyArmsEvoCtrl
   {
      
      public static const nameArr:Array = ["yearDragon","yearTiger","yearSheep"];
      
      public function YearEvoCtrl()
      {
         super();
      }
      
      override public function getCnName(cn0:String, evoLv0:int, d0:ArmsDefine) : String
      {
         if(evoLv0 == 2)
         {
            return "无双" + cn0;
         }
         return cn0;
      }
      
      override public function getHurtMul(evoLv0:int, d0:ArmsDefine) : Number
      {
         if(evoLv0 == 2)
         {
            return 1.25;
         }
         return 1;
      }
      
      override public function doEvo(da0:ArmsData) : void
      {
         var s0:ArmsSave = da0.save;
         var d0:ArmsDefine = s0.getArmsRangeDefine().def;
         s0.color = EquipColor.PURGOLD;
         ArrayMethod.replace(s0.godSkillArr,d0.name + "Skill",d0.name + "Purgold");
         s0.doEvo();
         s0.armsImgLabel = d0.getImageLabelByBodySuffix(s0.evoLv + "");
      }
      
      override public function getMust(evoLv0:int, ad0:ArmsDefine) : MustDefine
      {
         var thingArr0:Array = ["poisonGem;9999999"];
         var mustLv0:int = evoLv0 + 1;
         var d0:MustDefine = new MustDefine();
         if(mustLv0 == 2)
         {
            if(ad0.name == "yearSheep")
            {
               thingArr0 = ["barrenAwn;100"];
            }
            else
            {
               thingArr0 = ["iceCone;100"];
            }
            thingArr0 = thingArr0.concat(["zodiacCash;50","demBall;310","demStone;310"]);
         }
         d0.lv = 80;
         d0.coin = 0;
         d0.inThingsDataByArr(thingArr0);
         return d0;
      }
   }
}

