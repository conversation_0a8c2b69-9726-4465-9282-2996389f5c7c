package dataAll.level.modeDiy
{
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._base.BaseDefineGroup;
   
   public class ModeDiyDefineGroup extends BaseDefineGroup
   {
      
      public function ModeDiyDefineGroup()
      {
         var d0:ModeDiyDefine = null;
         super();
         defineClass = ModeDiyDefine;
         this.addDefineNameArr(ModeDiyDefine.DEMON_ARR,MapMode.DEMON);
         this.addDefineNameArr(ModeDiyDefine.UNEND_ARR,ModeDiyDefine.UNEND);
         this.addDefineName(ModeDiyDefine.GREEN_IS,ModeDiyDefine.GREEN_IS);
         d0 = new ModeDiyDefine();
         d0.name = ModeDiyDefine.SPACE_CRAFT;
         d0.cnName = "太空飞船";
         d0.modeDiy = "space";
         d0.craftB = true;
         d0.toNoMainAll();
         addDefine(d0,MapMode.SPACE);
         d0 = new ModeDiyDefine();
         d0.name = ModeDiyDefine.SPACE_SUIT;
         d0.cnName = "宇航员";
         d0.modeDiy = "space";
         d0.spaceSuitB = true;
         d0.armsAddLifeReduceB = true;
         d0.toNoMainAll();
         addDefine(d0,MapMode.SPACE);
      }
      
      public function dealDiyArr(mapD0:WorldMapDefine, baseArr0:Array) : Array
      {
         var diy0:String = null;
         var d0:ModeDiyDefine = null;
         var newArr0:Array = [];
         for each(diy0 in baseArr0)
         {
            d0 = this.getDefine(diy0);
            if(d0.canMapB(mapD0))
            {
               newArr0.push(diy0);
            }
         }
         return newArr0;
      }
      
      private function addDefineNameArr(arr0:Array, father0:String) : void
      {
         var name0:String = null;
         for each(name0 in arr0)
         {
            this.addDefineName(name0,father0);
         }
      }
      
      private function addDefineName(name0:String, father0:String) : void
      {
         var d0:ModeDiyDefine = new ModeDiyDefine();
         d0["setTo_" + name0]();
         addDefine(d0,father0);
      }
      
      public function getDefine(name0:String) : ModeDiyDefine
      {
         return obj[name0];
      }
   }
}

