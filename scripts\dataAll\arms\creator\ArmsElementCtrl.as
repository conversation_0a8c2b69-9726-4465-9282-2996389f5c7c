package dataAll.arms.creator
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.arms.save.ArmsSave;
   import dataAll.body.attack.ElementHurtDefine;
   import dataAll.must.define.MustDefine;
   
   public class ArmsElementCtrl
   {
      
      public function ArmsElementCtrl()
      {
         super();
      }
      
      public static function getAfterData(da0:ArmsData, elementD0:ElementHurtDefine) : ArmsData
      {
         var affterS0:ArmsSave = null;
         var affterDa0:ArmsData = null;
         var nowEle0:String = da0.save.ele;
         if(elementD0.name != nowEle0 || !isMaxB(da0.save))
         {
            affterS0 = da0.save.copy();
            affterS0.doElement(elementD0.name);
            affterDa0 = new ArmsData();
            affterDa0.inData_bySave(affterS0,da0.normalPlayerData,true,da0.fatherData as ArmsDataGroup);
            affterDa0.fleshData_byMeEquip();
            return affterDa0;
         }
         return null;
      }
      
      public static function getMust(da0:ArmsData, elementD0:ElementHurtDefine) : MustDefine
      {
         var s0:ArmsSave = da0.save;
         var d0:MustDefine = new MustDefine();
         var thingArr0:Array = [];
         var targetLv0:int = s0.eleLv + 1;
         var num0:Number = getGemNum(targetLv0);
         if(s0.ele != "" && s0.ele != elementD0.name)
         {
            num0 = Math.ceil(getGemSum(s0.eleLv) * 0.66);
         }
         thingArr0.push(elementD0.getGemName() + ";" + num0);
         d0.inThingsDataByArr(thingArr0);
         return d0;
      }
      
      private static function getGemNum(lv0:int) : Number
      {
         return (lv0 - 1) * 10 + 50;
      }
      
      private static function getGemSum(lv0:int) : Number
      {
         var num0:Number = 0;
         for(var i:int = 1; i <= lv0; i++)
         {
            num0 += getGemNum(i);
         }
         return num0;
      }
      
      public static function deal(da0:ArmsData, elementD0:ElementHurtDefine) : void
      {
         da0.save.doElement(elementD0.name);
         da0.fleshData_byMeEquip();
      }
      
      public static function isMaxB(s0:ArmsSave) : Boolean
      {
         if(s0.eleLv >= getMaxLv())
         {
            return true;
         }
         return false;
      }
      
      public static function getMaxLv() : int
      {
         return 10;
      }
      
      public static function getHurtMul(lv0:int) : Number
      {
         var maxLv0:int = getMaxLv();
         if(lv0 > maxLv0)
         {
            lv0 = maxLv0;
         }
         return lv0 * 0.05 + 0.05;
      }
      
      public static function canMoveB(b0:ArmsData, a0:ArmsData) : String
      {
         var tip0:String = "";
         if(b0.getEle() == a0.getEle() && b0.save.eleLv == a0.save.eleLv)
         {
            tip0 = "二者元素伤害完全一致";
         }
         if(tip0 != "")
         {
            tip0 = ComMethod.color(tip0,"#FF3838");
         }
         return tip0;
      }
      
      public static function getMoveMust(b0:ArmsData, a0:ArmsData) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var arr0:Array = [];
         arr0.push("armsGemChest;1");
         d0.inThingsDataByArr(arr0);
         return d0;
      }
      
      public static function move(b0:ArmsData, a0:ArmsData) : void
      {
         var bs0:ArmsSave = b0.save;
         var as0:ArmsSave = a0.save;
         var b_ele:String = bs0.ele;
         var b_lv:Number = bs0.eleLv;
         var a_ele:String = as0.ele;
         var a_lv:Number = as0.eleLv;
         bs0.ele = a_ele;
         bs0.eleLv = a_lv;
         as0.ele = b_ele;
         as0.eleLv = b_lv;
         if(as0.eleLv > 0)
         {
            as0.lockB = true;
         }
         if(bs0.eleLv > 0)
         {
            bs0.lockB = true;
         }
      }
   }
}

