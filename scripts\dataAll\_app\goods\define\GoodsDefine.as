package dataAll._app.goods.define
{
   import UI.api.shop.ShopBuyObject;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.gift.define.GiftBase;
   import dataAll.pet.PetCount;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.things.define.ThingsDefine;
   
   public class GoodsDefine
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var father:String = "";
      
      public var otherArr:Array = [];
      
      public var uiType:String = "";
      
      public var type:String = "";
      
      public var propId:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var newB:Boolean = false;
      
      public var dataType:String = "things";
      
      private var _num:String = "";
      
      public var iconUrl:String = "";
      
      public var limitOneB:Boolean = false;
      
      public var levelLimit:int = 0;
      
      public var moreLimitB:Boolean = false;
      
      public var defineLabel:String = "";
      
      public var priceType:String = PriceType.COIN;
      
      public var priceExtra:String = "";
      
      public var right:String = "";
      
      public var inBagType:String = "";
      
      public var chooseNumB:Boolean = false;
      
      public var noOverlayB:Boolean = false;
      
      public var autoUseB:Boolean = false;
      
      public var noOtherPathB:Boolean = false;
      
      private var _description:String = "";
      
      public var fixedLimitB:Boolean = false;
      
      public var startTime:String = "";
      
      public var endTime:String = "";
      
      public var discount:Number = 1;
      
      public function GoodsDefine()
      {
         super();
         this.price = 0;
         this.num = 1;
         this.dayBuyLimitNum = 0;
         this.buyLimitNum = 0;
      }
      
      public function get price() : Number
      {
         return this.CF.getAttribute("price");
      }
      
      public function set price(v0:Number) : void
      {
         this.CF.setAttribute("price",v0);
      }
      
      public function get dayBuyLimitNum() : Number
      {
         return this.CF.getAttribute("dayBuyLimitNum");
      }
      
      public function set dayBuyLimitNum(v0:Number) : void
      {
         this.CF.setAttribute("dayBuyLimitNum",v0);
      }
      
      public function get buyLimitNum() : Number
      {
         return this.CF.getAttribute("buyLimitNum");
      }
      
      public function set buyLimitNum(v0:Number) : void
      {
         this.CF.setAttribute("buyLimitNum",v0);
      }
      
      public function set description(s0:String) : void
      {
         this._description = s0;
      }
      
      public function get description() : String
      {
         return this._description;
      }
      
      public function inData_byXML(xml0:XML, father0:String, uiType0:String) : void
      {
         var xx0:int = 0;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         this.father = father0;
         this.uiType = uiType0;
         if(PriceType.defineAutoBackArr.indexOf(this.father) >= 0)
         {
            this.priceType = this.father;
            if(this.name == "")
            {
               this.name = this.defineLabel + "_" + this.father.substr(0,1);
            }
            if(this.dataType == "things" || this.dataType == "parts")
            {
               this.chooseNumB = true;
            }
         }
         if(this.defineLabel == "")
         {
            this.defineLabel = this.name;
         }
         if(this.inBagType == "")
         {
            this.inBagType = this.dataType;
         }
         this.propId = String(int(this.propId));
         if(father0 == "normal" && this.priceType == PriceType.MONEY)
         {
         }
         if(this.priceType == PriceType.ANNI_COIN && this.fixedLimitB == false)
         {
            if(this.buyLimitNum > 4)
            {
               this.buyLimitNum = Math.floor(this.buyLimitNum * 5.5);
            }
         }
         if(this.priceType == PriceType.DEMBALL && this.fixedLimitB == false)
         {
            this.buyLimitNum = Math.floor(this.buyLimitNum * 1.5);
         }
         if(this.dataType == "equip")
         {
            xx0 = 0;
         }
      }
      
      public function isMonthCard() : Boolean
      {
         if(this.name.indexOf("MonthCard") > 0)
         {
            return true;
         }
         return false;
      }
      
      public function isCountInstantB() : Boolean
      {
         if(this.dataType == "" || this.dataType == "things" || this.dataType == "parts")
         {
            return true;
         }
         return false;
      }
      
      public function fleshAllByDefine() : void
      {
         if(this.dataType == "things" || this.dataType == "parts")
         {
            this.inThingsDefine(Gaming.defineGroup.things.getDefine(this.defineLabel));
         }
         else if(this.dataType == "fashion" || this.dataType == "equip" || this.dataType == "device" || this.dataType == "weapon")
         {
            this.inEquipDefine(Gaming.defineGroup.getAllEquipDefine(this.defineLabel),this.dataType);
         }
         else if(this.dataType == "vehicle")
         {
            this.inVehicleDefine(Gaming.defineGroup.vehicle.getDefine(this.defineLabel));
         }
         else if(this.dataType == "arms")
         {
            this.inArmsRangeDefine(Gaming.defineGroup.bullet.getArmsRangeDefine(this.defineLabel));
         }
         else if(this.dataType == "gene")
         {
            this.inGeneDefine(Gaming.defineGroup.gene.getDefine(this.defineLabel));
         }
         else if(this.dataType == "base")
         {
            this.inBase(this.defineLabel);
         }
      }
      
      public function getIconImgUrl(maxWidth0:int = 0, maxHeight0:int = 0) : String
      {
         var armsD0:ArmsRangeDefine = null;
         var d0:ArmsDefine = null;
         if(this.dataType == "arms")
         {
            if(maxWidth0 < 100 && maxWidth0 > 0)
            {
               armsD0 = Gaming.defineGroup.bullet.getArmsRangeDefine(this.defineLabel);
               d0 = armsD0.def;
               if(d0.iconUrl == "")
               {
                  return "IconGather/" + d0.armsType;
               }
               return d0.iconUrl;
            }
            return this.iconUrl;
         }
         return this.iconUrl;
      }
      
      private function inBase(name0:String) : void
      {
         this.noOverlayB = false;
         this.chooseNumB = true;
         if(this.cnName == "")
         {
            this.cnName = GiftBase.getCn(name0);
         }
         if(this.iconUrl == "")
         {
            this.iconUrl = GiftBase.getIconUrl(name0);
         }
         if(this.description == "")
         {
            this.description = GiftBase.getGatherTip(name0,this.num);
         }
      }
      
      private function inThingsDefine(d0:ThingsDefine) : void
      {
         if(!(d0 is ThingsDefine))
         {
            return;
         }
         this.noOverlayB = d0.noOverlayB;
         this.moreLimitB = d0.effectD.moreLimitB;
         if(this.cnName == "")
         {
            this.cnName = d0.cnName;
         }
         if(this.iconUrl == "")
         {
            this.iconUrl = d0.iconUrl;
         }
         if(this.description == "")
         {
            this.description = d0.description;
         }
         if(this.type == "")
         {
            if(d0.father.indexOf("Chip") >= 0)
            {
               this.type = "chip";
            }
            else
            {
               this.type = d0.father;
            }
         }
      }
      
      private function inEquipDefine(d0:EquipDefine, type0:String) : void
      {
         if(!(d0 is EquipDefine))
         {
            return;
         }
         this.noOverlayB = true;
         if(this.cnName == "")
         {
            this.cnName = d0.cnName;
         }
         if(this.iconUrl == "")
         {
            this.iconUrl = d0.iconLabel;
         }
         if(this.description == "")
         {
            this.description = d0.getGoodsTip();
         }
         if(this.type == "")
         {
            this.type = type0;
         }
      }
      
      private function inVehicleDefine(d0:VehicleDefine) : void
      {
         if(!(d0 is VehicleDefine))
         {
            return;
         }
         this.noOverlayB = true;
         if(this.cnName == "")
         {
            this.cnName = d0.cnName;
         }
         if(this.iconUrl == "")
         {
            this.iconUrl = d0.iconLabel;
         }
         if(this.type == "")
         {
            this.type = "vehicle";
         }
      }
      
      private function inArmsRangeDefine(d0:ArmsRangeDefine) : void
      {
         if(!(d0 is ArmsRangeDefine))
         {
            return;
         }
         this.noOverlayB = true;
         if(this.cnName == "")
         {
            this.cnName = d0.def.cnName;
         }
         if(this.iconUrl == "")
         {
            this.iconUrl = d0.def.getRandomImageLabel("",0,0);
         }
         if(this.type == "")
         {
            this.type = "arms";
         }
      }
      
      private function inGeneDefine(d0:GeneDefine) : void
      {
         if(!(d0 is GeneDefine))
         {
            return;
         }
         this.noOverlayB = true;
         if(this.cnName == "")
         {
            this.cnName = d0.cnName;
         }
         if(this.iconUrl == "")
         {
            this.iconUrl = d0.iconUrl;
         }
         if(this.type == "")
         {
            this.type = "gene";
         }
         if(this.levelLimit == 0)
         {
            this.levelLimit = PetCount.openLevel;
         }
      }
      
      public function isMoneyB() : Boolean
      {
         return this.priceType == PriceType.MONEY;
      }
      
      public function set num(v0:Number) : void
      {
         this._num = Sounto64.encode(String(v0));
      }
      
      public function get num() : Number
      {
         return Number(Sounto64.decode(this._num));
      }
      
      public function getShopObj(num0:int) : ShopBuyObject
      {
         var obj0:ShopBuyObject = new ShopBuyObject();
         obj0.count = num0;
         obj0.price = this.price;
         obj0.propId = this.propId;
         obj0.tag = "";
         return obj0;
      }
      
      public function isDiscountB() : Boolean
      {
         return this.getDiscountPrice() < this.price;
      }
      
      public function isTrueDiscountB() : Boolean
      {
         return this.isDiscountB() && this.priceType == PriceType.MONEY && this.father == "normal";
      }
      
      public function getDiscountPrice() : Number
      {
         return Math.ceil(this.price * this.discount);
      }
      
      public function getActionName() : String
      {
         return PriceType.getActionName(this.priceType);
      }
      
      public function getOverDay(now0:StringDate) : int
      {
         var end0:StringDate = new StringDate(this.endTime);
         return end0.reductionOne(now0) + 1;
      }
      
      public function getCountType() : int
      {
         if(this.dataType == "things")
         {
            return 1;
         }
         return 3;
      }
   }
}

