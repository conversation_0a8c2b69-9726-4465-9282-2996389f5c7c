package dataAll.pet.upgrade
{
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.EquipPropertyData;
   
   public class PetUpgradeSaveGroup
   {
      
      public static var pro_arr:Array = [];
      
      public var obj:Object = {};
      
      public function PetUpgradeSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copySaveObj(obj0["obj"],PetUpgradeSave);
      }
      
      public function getAddData() : EquipPropertyData
      {
         return new EquipPropertyData();
      }
   }
}

