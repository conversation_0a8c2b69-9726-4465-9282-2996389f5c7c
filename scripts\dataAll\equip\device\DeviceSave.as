package dataAll.equip.device
{
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.save.EquipSave;
   
   public class DeviceSave extends EquipSave
   {
      
      private static const ZERO:DeviceSave = new DeviceSave();
      
      public function DeviceSave()
      {
         super();
      }
      
      override public function getZero() : EquipSave
      {
         return ZERO;
      }
      
      override public function inDataByDefine(d0:EquipDefine) : void
      {
         super.inDataByDefine(d0);
         var deviceDefine0:DeviceDefine = d0 as DeviceDefine;
         name = deviceDefine0.name;
         itemsLevel = deviceDefine0.lv;
         skillArr = [deviceDefine0.skill];
      }
      
      override public function getDataClass() : EquipData
      {
         return new DeviceData();
      }
      
      override public function getDefine() : EquipDefine
      {
         return this.getDeviceDefine();
      }
      
      public function getDeviceDefine() : DeviceDefine
      {
         return Gaming.defineGroup.device.getDefine(name);
      }
      
      override public function getTrueObj() : Object
      {
         return obj;
      }
      
      override public function clone() : EquipSave
      {
         var obj0:Object = ClassProperty.copyObj(this);
         var s0:EquipSave = new DeviceSave();
         s0.inData_byObj(obj0);
         return s0;
      }
   }
}

