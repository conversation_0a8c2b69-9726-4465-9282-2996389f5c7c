package dataAll.things.define
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.define.GiftType;
   import dataAll.must.define.MustDefine;
   
   public class ThingsComposeDefine
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var name:String = "";
      
      public var giftType:String = "";
      
      public var mustArr:Array = [];
      
      private var gift:GiftAddDefine = new GiftAddDefine();
      
      public function ThingsComposeDefine()
      {
         super();
         this.mustLv = 0;
      }
      
      public function get mustLv() : Number
      {
         return this.CF.getAttribute("mustLv");
      }
      
      public function set mustLv(v0:Number) : void
      {
         this.CF.setAttribute("mustLv",v0);
      }
      
      public function get week() : Number
      {
         return this.CF.getAttribute("day");
      }
      
      public function set week(v0:Number) : void
      {
         this.CF.setAttribute("day",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var x0:XML = null;
         var type0:String = null;
         var d0:GiftAddDefineGroup = null;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         for each(x0 in xml0.must)
         {
            d0 = new GiftAddDefineGroup();
            d0.inData_byOneXML(x0,"");
            this.mustArr.push(d0);
         }
         type0 = this.giftType;
         if(type0 == "")
         {
            type0 = "things";
         }
         this.gift.inData_byStr(type0 + ";" + this.name);
      }
      
      public function getGift() : GiftAddDefine
      {
         return this.gift;
      }
      
      public function getThingsDefine() : ThingsDefine
      {
         return Gaming.defineGroup.things.getDefine(this.name);
      }
      
      public function isThingsB() : Boolean
      {
         return this.giftType == "" || this.giftType == GiftType.things;
      }
      
      public function getMustDefine(num0:int, index0:int) : MustDefine
      {
         if(index0 < 0)
         {
            index0 = 0;
         }
         if(index0 > this.mustArr.length - 1)
         {
            index0 = this.mustArr.length - 1;
         }
         var must0:GiftAddDefineGroup = this.mustArr[index0];
         var d0:GiftAddDefineGroup = must0.clone();
         d0.addNumMul(num0);
         var mustD0:MustDefine = d0.getMustDefine();
         mustD0.lv = this.mustLv;
         return mustD0;
      }
      
      public function countIndex(index0:int, add0:int) : int
      {
         return int((index0 + add0 + this.mustArr.length) % this.mustArr.length);
      }
      
      public function getComposeGiftAddDefineGroup(num0:int) : GiftAddDefineGroup
      {
         var d0:GiftAddDefine = this.getGift().clone();
         d0.num *= num0;
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         g0.addGift(d0);
         return g0;
      }
   }
}

