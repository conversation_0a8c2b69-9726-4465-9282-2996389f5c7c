package dataAll._app.head.define
{
   import com.common.data.Base64;
   import dataAll._app.achieve.define.AchieveFatherDefine;
   
   public class HeadDefineGroup
   {
      
      public var obj:Object = {};
      
      private var cnObj:Object = {};
      
      public var arr:Array = [];
      
      public var fatherArrObj:Object = {};
      
      public var fatherDefineObj:Object = {};
      
      public var fatherNameArr:Array = [];
      
      private var base64:String = "";
      
      public var honorArr:Array = [];
      
      public function HeadDefineGroup()
      {
         super();
      }
      
      public function init() : void
      {
         this.base64 = Base64.encodeObject([this.obj,this.honorArr]);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var x0:XML = null;
         var fatherD0:AchieveFatherDefine = null;
         var xl2:XMLList = null;
         var j:* = undefined;
         var x2:XML = null;
         var d0:HeadDefine = null;
         var xl0:XMLList = xml0.father;
         for(n in xl0)
         {
            x0 = xl0[n];
            fatherD0 = new AchieveFatherDefine();
            fatherD0.inData_byXML(x0);
            this.addFatherDefine(fatherD0);
            this.fatherNameArr.push(fatherD0.name);
            xl2 = x0.head;
            for(j in xl2)
            {
               x2 = xl2[j];
               d0 = new HeadDefine();
               d0.inData_byXML(x2,fatherD0.name);
               this.add(d0,fatherD0.name);
            }
         }
      }
      
      public function inHonor_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var x0:XML = null;
         var d0:HeadHonorDefine = null;
         var xl0:XMLList = xml0.honor;
         var beforeD0:HeadHonorDefine = null;
         for(n in xl0)
         {
            x0 = xl0[n];
            d0 = new HeadHonorDefine();
            d0.inData_byXML(x0);
            if(Boolean(beforeD0))
            {
               beforeD0.max = d0.min - 1;
            }
            if(n == xl0.length() - 1)
            {
               d0.max = 99999;
            }
            beforeD0 = d0;
            this.honorArr.push(d0);
         }
      }
      
      private function add(d0:HeadDefine, father0:String) : void
      {
         this.obj[d0.name] = d0;
         this.arr.push(d0);
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
         }
         this.fatherArrObj[father0].push(d0);
         this.cnObj[d0.cnName] = d0;
      }
      
      private function addFatherDefine(d0:AchieveFatherDefine) : void
      {
         this.fatherDefineObj[d0.name] = d0;
      }
      
      public function getDefine(name0:String) : HeadDefine
      {
         return this.obj[name0];
      }
      
      public function getDefineByCn(name0:String) : HeadDefine
      {
         return this.cnObj[name0];
      }
      
      public function getFatherDefine(name0:String) : AchieveFatherDefine
      {
         return this.fatherDefineObj[name0];
      }
      
      public function getArrByFather(father0:String) : Array
      {
         return this.fatherArrObj[father0];
      }
      
      public function zuobiPan() : String
      {
         var str0:String = Base64.encodeObject([this.obj,this.honorArr]);
         if(str0 != this.base64)
         {
            return "head";
         }
         return "";
      }
      
      public function getHonorDefineByValue(v0:Number) : HeadHonorDefine
      {
         var d0:HeadHonorDefine = null;
         for each(d0 in this.honorArr)
         {
            if(d0.panValue(v0))
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getAllHonor() : Number
      {
         var d0:HeadDefine = null;
         var v0:Number = 0;
         for each(d0 in this.obj)
         {
            v0 += d0.getHonorValue();
         }
         return v0;
      }
      
      public function getIconNum() : int
      {
         var d0:HeadDefine = null;
         var obj0:Object = {};
         var num0:int = 0;
         for each(d0 in this.obj)
         {
            if(!obj0.hasOwnProperty(d0.iconUrl))
            {
               obj0[d0.iconUrl] = 0;
               num0++;
            }
         }
         return num0;
      }
   }
}

