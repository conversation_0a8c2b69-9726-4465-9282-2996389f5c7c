package dataAll.items
{
   public class ItemsCompareData
   {
      
      public static var ZERO:ItemsCompareData = new ItemsCompareData();
      
      public var dataGroup:ItemsDataGroup;
      
      public var itemsData:IO_ItemsData;
      
      public function ItemsCompareData(dg0:ItemsDataGroup = null, da0:IO_ItemsData = null)
      {
         super();
         this.dataGroup = dg0;
         this.itemsData = da0;
      }
   }
}

