package dataAll.bullet
{
   import com.sounto.utils.ClassProperty;
   
   public class BulletSpeedDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var min:Number = 0;
      
      public var max:Number = 0;
      
      public var a:Number = 0;
      
      public var random:Number = 0;
      
      public var selfVra:Number = 0;
      
      public var raBackV:Number = 0;
      
      public function BulletSpeedDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, speed0:Number) : void
      {
         var xx0:int = 0;
         this.max = speed0;
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         if(speed0 > this.max)
         {
            xx0 = 0;
         }
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function copy() : BulletSpeedDefine
      {
         var d0:BulletSpeedDefine = new BulletSpeedDefine();
         ClassProperty.inData(d0,this,pro_arr);
         return d0;
      }
   }
}

